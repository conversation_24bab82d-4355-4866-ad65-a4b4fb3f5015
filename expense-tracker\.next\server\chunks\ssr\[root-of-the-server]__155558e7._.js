module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/contexts/ThemeContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ThemeProvider": ()=>ThemeProvider,
    "themes": ()=>themes,
    "useTheme": ()=>useTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const themes = {
    light: {
        id: 'light',
        name: 'Light Mode',
        description: 'Clean and bright interface',
        colors: {
            bg: {
                primary: '#ffffff',
                secondary: '#f8fafc',
                tertiary: '#f1f5f9',
                card: '#ffffff',
                modal: '#ffffff'
            },
            text: {
                primary: '#1f2937',
                secondary: '#6b7280',
                tertiary: '#9ca3af',
                inverse: '#ffffff'
            },
            accent: {
                primary: '#3b82f6',
                secondary: '#6366f1',
                success: '#10b981',
                warning: '#f59e0b',
                error: '#ef4444',
                info: '#06b6d4'
            },
            border: {
                primary: '#e5e7eb',
                secondary: '#d1d5db',
                focus: '#3b82f6'
            },
            glow: {
                primary: 'rgba(59, 130, 246, 0.3)',
                secondary: 'rgba(99, 102, 241, 0.3)',
                accent: 'rgba(16, 185, 129, 0.3)'
            }
        },
        effects: {
            blur: 'backdrop-blur-sm',
            shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            glow: '0 0 20px rgba(59, 130, 246, 0.3)',
            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            hover: {
                scale: 'scale-105',
                glow: '0 0 25px rgba(59, 130, 246, 0.4)',
                shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.2)'
            }
        }
    },
    dark: {
        id: 'dark',
        name: 'Dark Mode',
        description: 'Easy on the eyes',
        colors: {
            bg: {
                primary: '#111827',
                secondary: '#1f2937',
                tertiary: '#374151',
                card: '#1f2937',
                modal: '#111827'
            },
            text: {
                primary: '#f9fafb',
                secondary: '#d1d5db',
                tertiary: '#9ca3af',
                inverse: '#111827'
            },
            accent: {
                primary: '#60a5fa',
                secondary: '#818cf8',
                success: '#34d399',
                warning: '#fbbf24',
                error: '#f87171',
                info: '#22d3ee'
            },
            border: {
                primary: '#374151',
                secondary: '#4b5563',
                focus: '#60a5fa'
            },
            glow: {
                primary: 'rgba(96, 165, 250, 0.3)',
                secondary: 'rgba(129, 140, 248, 0.3)',
                accent: 'rgba(52, 211, 153, 0.3)'
            }
        },
        effects: {
            blur: 'backdrop-blur-sm',
            shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',
            glow: '0 0 20px rgba(96, 165, 250, 0.3)',
            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            hover: {
                scale: 'scale-105',
                glow: '0 0 25px rgba(96, 165, 250, 0.4)',
                shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.4)'
            }
        }
    },
    cyber: {
        id: 'cyber',
        name: 'Cyber Black',
        description: 'Futuristic super black with neon accents',
        colors: {
            bg: {
                primary: '#000000',
                secondary: '#0a0a0a',
                tertiary: '#111111',
                card: 'rgba(0, 0, 0, 0.9)',
                modal: 'rgba(0, 0, 0, 0.95)'
            },
            text: {
                primary: '#00ff88',
                secondary: '#00ccff',
                tertiary: '#888888',
                inverse: '#000000'
            },
            accent: {
                primary: '#00ff88',
                secondary: '#00ccff',
                success: '#00ff00',
                warning: '#ffaa00',
                error: '#ff0055',
                info: '#0088ff'
            },
            border: {
                primary: '#00ff88',
                secondary: '#00ccff',
                focus: '#00ff88'
            },
            glow: {
                primary: 'rgba(0, 255, 136, 0.5)',
                secondary: 'rgba(0, 204, 255, 0.5)',
                accent: 'rgba(0, 255, 0, 0.5)'
            }
        },
        effects: {
            blur: 'backdrop-blur-md',
            shadow: '0 0 30px rgba(0, 255, 136, 0.3)',
            glow: '0 0 40px rgba(0, 255, 136, 0.6)',
            gradient: 'linear-gradient(135deg, #00ff88 0%, #00ccff 100%)',
            hover: {
                scale: 'scale-110',
                glow: '0 0 50px rgba(0, 255, 136, 0.8)',
                shadow: '0 20px 40px -10px rgba(0, 255, 136, 0.4)'
            }
        }
    },
    neon: {
        id: 'neon',
        name: 'Neon Dreams',
        description: 'Vibrant neon with dark background',
        colors: {
            bg: {
                primary: '#0d1117',
                secondary: '#161b22',
                tertiary: '#21262d',
                card: 'rgba(13, 17, 23, 0.9)',
                modal: 'rgba(13, 17, 23, 0.95)'
            },
            text: {
                primary: '#ff006e',
                secondary: '#8338ec',
                tertiary: '#aaaaaa',
                inverse: '#0d1117'
            },
            accent: {
                primary: '#ff006e',
                secondary: '#8338ec',
                success: '#06ffa5',
                warning: '#ffbe0b',
                error: '#fb5607',
                info: '#3a86ff'
            },
            border: {
                primary: '#ff006e',
                secondary: '#8338ec',
                focus: '#ff006e'
            },
            glow: {
                primary: 'rgba(255, 0, 110, 0.5)',
                secondary: 'rgba(131, 56, 236, 0.5)',
                accent: 'rgba(6, 255, 165, 0.5)'
            }
        },
        effects: {
            blur: 'backdrop-blur-lg',
            shadow: '0 0 25px rgba(255, 0, 110, 0.4)',
            glow: '0 0 35px rgba(255, 0, 110, 0.7)',
            gradient: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',
            hover: {
                scale: 'scale-108',
                glow: '0 0 45px rgba(255, 0, 110, 0.9)',
                shadow: '0 15px 35px -8px rgba(255, 0, 110, 0.5)'
            }
        }
    },
    matrix: {
        id: 'matrix',
        name: 'Matrix Code',
        description: 'Green matrix-style interface',
        colors: {
            bg: {
                primary: '#000000',
                secondary: '#001100',
                tertiary: '#002200',
                card: 'rgba(0, 17, 0, 0.9)',
                modal: 'rgba(0, 0, 0, 0.95)'
            },
            text: {
                primary: '#00ff00',
                secondary: '#00cc00',
                tertiary: '#008800',
                inverse: '#000000'
            },
            accent: {
                primary: '#00ff00',
                secondary: '#00cc00',
                success: '#00ff00',
                warning: '#ffff00',
                error: '#ff0000',
                info: '#00ffff'
            },
            border: {
                primary: '#00ff00',
                secondary: '#00cc00',
                focus: '#00ff00'
            },
            glow: {
                primary: 'rgba(0, 255, 0, 0.5)',
                secondary: 'rgba(0, 204, 0, 0.5)',
                accent: 'rgba(0, 255, 0, 0.7)'
            }
        },
        effects: {
            blur: 'backdrop-blur-sm',
            shadow: '0 0 20px rgba(0, 255, 0, 0.4)',
            glow: '0 0 30px rgba(0, 255, 0, 0.8)',
            gradient: 'linear-gradient(135deg, #00ff00 0%, #00cc00 100%)',
            hover: {
                scale: 'scale-105',
                glow: '0 0 40px rgba(0, 255, 0, 1)',
                shadow: '0 10px 30px -5px rgba(0, 255, 0, 0.6)'
            }
        }
    }
};
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ThemeProvider({ children }) {
    const [currentTheme, setCurrentTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('light');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const savedTheme = localStorage.getItem('expense-tracker-theme');
        if (savedTheme && themes[savedTheme]) {
            setCurrentTheme(savedTheme);
        }
    }, []);
    const setTheme = (theme)=>{
        setCurrentTheme(theme);
        localStorage.setItem('expense-tracker-theme', theme);
        // Apply CSS custom properties
        const root = document.documentElement;
        const themeColors = themes[theme].colors;
        // Set CSS variables
        root.style.setProperty('--bg-primary', themeColors.bg.primary);
        root.style.setProperty('--bg-secondary', themeColors.bg.secondary);
        root.style.setProperty('--bg-tertiary', themeColors.bg.tertiary);
        root.style.setProperty('--bg-card', themeColors.bg.card);
        root.style.setProperty('--bg-modal', themeColors.bg.modal);
        root.style.setProperty('--text-primary', themeColors.text.primary);
        root.style.setProperty('--text-secondary', themeColors.text.secondary);
        root.style.setProperty('--text-tertiary', themeColors.text.tertiary);
        root.style.setProperty('--text-inverse', themeColors.text.inverse);
        root.style.setProperty('--accent-primary', themeColors.accent.primary);
        root.style.setProperty('--accent-secondary', themeColors.accent.secondary);
        root.style.setProperty('--accent-success', themeColors.accent.success);
        root.style.setProperty('--accent-warning', themeColors.accent.warning);
        root.style.setProperty('--accent-error', themeColors.accent.error);
        root.style.setProperty('--accent-info', themeColors.accent.info);
        root.style.setProperty('--border-primary', themeColors.border.primary);
        root.style.setProperty('--border-secondary', themeColors.border.secondary);
        root.style.setProperty('--border-focus', themeColors.border.focus);
        root.style.setProperty('--glow-primary', themeColors.glow.primary);
        root.style.setProperty('--glow-secondary', themeColors.glow.secondary);
        root.style.setProperty('--glow-accent', themeColors.glow.accent);
    };
    const toggleTheme = ()=>{
        const themeOrder = [
            'light',
            'dark',
            'cyber',
            'neon',
            'matrix'
        ];
        const currentIndex = themeOrder.indexOf(currentTheme);
        const nextIndex = (currentIndex + 1) % themeOrder.length;
        setTheme(themeOrder[nextIndex]);
    };
    const value = {
        currentTheme,
        theme: themes[currentTheme],
        setTheme,
        toggleTheme
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ThemeContext.tsx",
        lineNumber: 380,
        columnNumber: 5
    }, this);
}
function useTheme() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
}),
"[project]/src/contexts/LanguageContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "LanguageProvider": ()=>LanguageProvider,
    "SUPPORTED_LANGUAGES": ()=>SUPPORTED_LANGUAGES,
    "useBatchTranslate": ()=>useBatchTranslate,
    "useLanguage": ()=>useLanguage,
    "useTranslate": ()=>useTranslate
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const SUPPORTED_LANGUAGES = {
    en: {
        code: 'en',
        name: 'English',
        nativeName: 'English',
        flag: '🇺🇸',
        rtl: false
    },
    zh: {
        code: 'zh',
        name: 'Chinese',
        nativeName: '中文',
        flag: '🇨🇳',
        rtl: false
    },
    hi: {
        code: 'hi',
        name: 'Hindi',
        nativeName: 'हिन्दी',
        flag: '🇮🇳',
        rtl: false
    },
    es: {
        code: 'es',
        name: 'Spanish',
        nativeName: 'Español',
        flag: '🇪🇸',
        rtl: false
    },
    ar: {
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        flag: '🇸🇦',
        rtl: true
    },
    bn: {
        code: 'bn',
        name: 'Bengali',
        nativeName: 'বাংলা',
        flag: '🇧🇩',
        rtl: false
    },
    fr: {
        code: 'fr',
        name: 'French',
        nativeName: 'Français',
        flag: '🇫🇷',
        rtl: false
    },
    ru: {
        code: 'ru',
        name: 'Russian',
        nativeName: 'Русский',
        flag: '🇷🇺',
        rtl: false
    },
    pt: {
        code: 'pt',
        name: 'Portuguese',
        nativeName: 'Português',
        flag: '🇧🇷',
        rtl: false
    },
    ur: {
        code: 'ur',
        name: 'Urdu',
        nativeName: 'اردو',
        flag: '🇵🇰',
        rtl: true
    },
    id: {
        code: 'id',
        name: 'Indonesian',
        nativeName: 'Bahasa Indonesia',
        flag: '🇮🇩',
        rtl: false
    },
    sw: {
        code: 'sw',
        name: 'Swahili',
        nativeName: 'Kiswahili',
        flag: '🇰🇪',
        rtl: false
    },
    pa: {
        code: 'pa',
        name: 'Punjabi',
        nativeName: 'ਪੰਜਾਬੀ',
        flag: '🇮🇳',
        rtl: false
    },
    ko: {
        code: 'ko',
        name: 'Korean',
        nativeName: '한국어',
        flag: '🇰🇷',
        rtl: false
    },
    de: {
        code: 'de',
        name: 'German',
        nativeName: 'Deutsch',
        flag: '🇩🇪',
        rtl: false
    },
    ja: {
        code: 'ja',
        name: 'Japanese',
        nativeName: '日本語',
        flag: '🇯🇵',
        rtl: false
    }
};
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Translation cache to avoid repeated API calls
const translationCache = new Map();
// Detect browser language
function detectBrowserLanguage() {
    if ("TURBOPACK compile-time truthy", 1) return 'en';
    //TURBOPACK unreachable
    ;
    const browserLang = undefined;
}
// Auto-translate function using Google Translate API (free tier)
async function translateText(text, targetLang) {
    if (targetLang === 'en') return text; // No translation needed for English
    const cacheKey = `${text}:${targetLang}`;
    // Check cache first
    if (translationCache.has(cacheKey)) {
        return translationCache.get(cacheKey);
    }
    try {
        // Using Google Translate API (you can replace with other services)
        const response = await fetch(`https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=${targetLang}&dt=t&q=${encodeURIComponent(text)}`);
        if (!response.ok) {
            throw new Error('Translation failed');
        }
        const data = await response.json();
        const translatedText = data[0]?.[0]?.[0] || text;
        // Cache the translation
        translationCache.set(cacheKey, translatedText);
        return translatedText;
    } catch (error) {
        console.warn('Translation failed, using original text:', error);
        return text;
    }
}
function LanguageProvider({ children }) {
    const [currentLanguage, setCurrentLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('en');
    const [isTranslating, setIsTranslating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Initialize language from localStorage or browser detection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const savedLanguage = localStorage.getItem('expense-tracker-language');
        const initialLanguage = savedLanguage || detectBrowserLanguage();
        if (SUPPORTED_LANGUAGES[initialLanguage]) {
            setCurrentLanguage(initialLanguage);
        }
    }, []);
    // Clear translation cache when language changes to force re-translation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        translationCache.clear();
    }, [
        currentLanguage
    ]);
    // Update document direction for RTL languages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const isRTL = SUPPORTED_LANGUAGES[currentLanguage].rtl;
        document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
        document.documentElement.lang = currentLanguage;
        // Add RTL class to body for styling
        if (isRTL) {
            document.body.classList.add('rtl');
        } else {
            document.body.classList.remove('rtl');
        }
    }, [
        currentLanguage
    ]);
    const setLanguage = (language)=>{
        setCurrentLanguage(language);
        localStorage.setItem('expense-tracker-language', language);
    };
    // Auto-translate function with caching
    const t = (text)=>{
        if (currentLanguage === 'en') {
            return text; // No translation needed for English
        }
        const cacheKey = `${text}:${currentLanguage}`;
        // Check cache first
        if (translationCache.has(cacheKey)) {
            return translationCache.get(cacheKey);
        }
        // If not in cache, start translation and return original text temporarily
        setIsTranslating(true);
        translateText(text, currentLanguage).then((translated)=>{
            setIsTranslating(false);
        // Force re-render by updating a dummy state or using a callback
        // This is a simplified approach - in production, you might want to use a more sophisticated state management
        }).catch(()=>{
            setIsTranslating(false);
        });
        return text; // Return original text while translating
    };
    const value = {
        currentLanguage,
        setLanguage,
        t,
        isRTL: SUPPORTED_LANGUAGES[currentLanguage].rtl,
        languageData: SUPPORTED_LANGUAGES[currentLanguage],
        isTranslating
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/LanguageContext.tsx",
        lineNumber: 259,
        columnNumber: 5
    }, this);
}
function useLanguage() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
    if (context === undefined) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
}
function useTranslate(text) {
    const { currentLanguage } = useLanguage();
    const [translatedText, setTranslatedText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(text);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (currentLanguage === 'en') {
            setTranslatedText(text);
            return;
        }
        const cacheKey = `${text}:${currentLanguage}`;
        // Check cache first
        if (translationCache.has(cacheKey)) {
            setTranslatedText(translationCache.get(cacheKey));
            return;
        }
        // Translate if not in cache
        translateText(text, currentLanguage).then((translated)=>{
            setTranslatedText(translated);
        }).catch(()=>{
            setTranslatedText(text);
        });
    }, [
        text,
        currentLanguage
    ]);
    return translatedText;
}
function useBatchTranslate(texts) {
    const { currentLanguage } = useLanguage();
    const [translatedTexts, setTranslatedTexts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(texts);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (currentLanguage === 'en') {
            setTranslatedTexts(texts);
            return;
        }
        const translateBatch = async ()=>{
            const results = await Promise.all(texts.map((text)=>translateText(text, currentLanguage)));
            setTranslatedTexts(results);
        };
        translateBatch();
    }, [
        texts,
        currentLanguage
    ]);
    return translatedTexts;
}
}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else //TURBOPACK unreachable
            ;
        } else //TURBOPACK unreachable
        ;
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__155558e7._.js.map