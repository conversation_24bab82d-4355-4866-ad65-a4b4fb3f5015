{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\nexport type ThemeType = 'light' | 'dark' | 'cyber' | 'neon' | 'matrix';\n\nexport interface Theme {\n  id: ThemeType;\n  name: string;\n  description: string;\n  colors: {\n    // Background colors\n    bg: {\n      primary: string;\n      secondary: string;\n      tertiary: string;\n      card: string;\n      modal: string;\n    };\n    // Text colors\n    text: {\n      primary: string;\n      secondary: string;\n      tertiary: string;\n      inverse: string;\n    };\n    // Accent colors\n    accent: {\n      primary: string;\n      secondary: string;\n      success: string;\n      warning: string;\n      error: string;\n      info: string;\n    };\n    // Border colors\n    border: {\n      primary: string;\n      secondary: string;\n      focus: string;\n    };\n    // Special effects\n    glow: {\n      primary: string;\n      secondary: string;\n      accent: string;\n    };\n  };\n  effects: {\n    blur: string;\n    shadow: string;\n    glow: string;\n    gradient: string;\n    hover: {\n      scale: string;\n      glow: string;\n      shadow: string;\n    };\n  };\n}\n\nexport const themes: Record<ThemeType, Theme> = {\n  light: {\n    id: 'light',\n    name: 'Light Mode',\n    description: 'Clean and bright interface',\n    colors: {\n      bg: {\n        primary: '#ffffff',\n        secondary: '#f8fafc',\n        tertiary: '#f1f5f9',\n        card: '#ffffff',\n        modal: '#ffffff',\n      },\n      text: {\n        primary: '#1f2937',\n        secondary: '#6b7280',\n        tertiary: '#9ca3af',\n        inverse: '#ffffff',\n      },\n      accent: {\n        primary: '#3b82f6',\n        secondary: '#6366f1',\n        success: '#10b981',\n        warning: '#f59e0b',\n        error: '#ef4444',\n        info: '#06b6d4',\n      },\n      border: {\n        primary: '#e5e7eb',\n        secondary: '#d1d5db',\n        focus: '#3b82f6',\n      },\n      glow: {\n        primary: 'rgba(59, 130, 246, 0.3)',\n        secondary: 'rgba(99, 102, 241, 0.3)',\n        accent: 'rgba(16, 185, 129, 0.3)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-sm',\n      shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n      glow: '0 0 20px rgba(59, 130, 246, 0.3)',\n      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      hover: {\n        scale: 'scale-105',\n        glow: '0 0 25px rgba(59, 130, 246, 0.4)',\n        shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.2)',\n      },\n    },\n  },\n  dark: {\n    id: 'dark',\n    name: 'Dark Mode',\n    description: 'Easy on the eyes',\n    colors: {\n      bg: {\n        primary: '#111827',\n        secondary: '#1f2937',\n        tertiary: '#374151',\n        card: '#1f2937',\n        modal: '#111827',\n      },\n      text: {\n        primary: '#f9fafb',\n        secondary: '#d1d5db',\n        tertiary: '#9ca3af',\n        inverse: '#111827',\n      },\n      accent: {\n        primary: '#60a5fa',\n        secondary: '#818cf8',\n        success: '#34d399',\n        warning: '#fbbf24',\n        error: '#f87171',\n        info: '#22d3ee',\n      },\n      border: {\n        primary: '#374151',\n        secondary: '#4b5563',\n        focus: '#60a5fa',\n      },\n      glow: {\n        primary: 'rgba(96, 165, 250, 0.3)',\n        secondary: 'rgba(129, 140, 248, 0.3)',\n        accent: 'rgba(52, 211, 153, 0.3)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-sm',\n      shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',\n      glow: '0 0 20px rgba(96, 165, 250, 0.3)',\n      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      hover: {\n        scale: 'scale-105',\n        glow: '0 0 25px rgba(96, 165, 250, 0.4)',\n        shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.4)',\n      },\n    },\n  },\n  cyber: {\n    id: 'cyber',\n    name: 'Cyber Black',\n    description: 'Futuristic super black with neon accents',\n    colors: {\n      bg: {\n        primary: '#000000',\n        secondary: '#0a0a0a',\n        tertiary: '#111111',\n        card: 'rgba(0, 0, 0, 0.9)',\n        modal: 'rgba(0, 0, 0, 0.95)',\n      },\n      text: {\n        primary: '#00ff88',\n        secondary: '#00ccff',\n        tertiary: '#888888',\n        inverse: '#000000',\n      },\n      accent: {\n        primary: '#00ff88',\n        secondary: '#00ccff',\n        success: '#00ff00',\n        warning: '#ffaa00',\n        error: '#ff0055',\n        info: '#0088ff',\n      },\n      border: {\n        primary: '#00ff88',\n        secondary: '#00ccff',\n        focus: '#00ff88',\n      },\n      glow: {\n        primary: 'rgba(0, 255, 136, 0.5)',\n        secondary: 'rgba(0, 204, 255, 0.5)',\n        accent: 'rgba(0, 255, 0, 0.5)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-md',\n      shadow: '0 0 30px rgba(0, 255, 136, 0.3)',\n      glow: '0 0 40px rgba(0, 255, 136, 0.6)',\n      gradient: 'linear-gradient(135deg, #00ff88 0%, #00ccff 100%)',\n      hover: {\n        scale: 'scale-110',\n        glow: '0 0 50px rgba(0, 255, 136, 0.8)',\n        shadow: '0 20px 40px -10px rgba(0, 255, 136, 0.4)',\n      },\n    },\n  },\n  neon: {\n    id: 'neon',\n    name: 'Neon Dreams',\n    description: 'Vibrant neon with dark background',\n    colors: {\n      bg: {\n        primary: '#0d1117',\n        secondary: '#161b22',\n        tertiary: '#21262d',\n        card: 'rgba(13, 17, 23, 0.9)',\n        modal: 'rgba(13, 17, 23, 0.95)',\n      },\n      text: {\n        primary: '#ff006e',\n        secondary: '#8338ec',\n        tertiary: '#aaaaaa',\n        inverse: '#0d1117',\n      },\n      accent: {\n        primary: '#ff006e',\n        secondary: '#8338ec',\n        success: '#06ffa5',\n        warning: '#ffbe0b',\n        error: '#fb5607',\n        info: '#3a86ff',\n      },\n      border: {\n        primary: '#ff006e',\n        secondary: '#8338ec',\n        focus: '#ff006e',\n      },\n      glow: {\n        primary: 'rgba(255, 0, 110, 0.5)',\n        secondary: 'rgba(131, 56, 236, 0.5)',\n        accent: 'rgba(6, 255, 165, 0.5)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-lg',\n      shadow: '0 0 25px rgba(255, 0, 110, 0.4)',\n      glow: '0 0 35px rgba(255, 0, 110, 0.7)',\n      gradient: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',\n      hover: {\n        scale: 'scale-108',\n        glow: '0 0 45px rgba(255, 0, 110, 0.9)',\n        shadow: '0 15px 35px -8px rgba(255, 0, 110, 0.5)',\n      },\n    },\n  },\n  matrix: {\n    id: 'matrix',\n    name: 'Matrix Code',\n    description: 'Green matrix-style interface',\n    colors: {\n      bg: {\n        primary: '#000000',\n        secondary: '#001100',\n        tertiary: '#002200',\n        card: 'rgba(0, 17, 0, 0.9)',\n        modal: 'rgba(0, 0, 0, 0.95)',\n      },\n      text: {\n        primary: '#00ff00',\n        secondary: '#00cc00',\n        tertiary: '#008800',\n        inverse: '#000000',\n      },\n      accent: {\n        primary: '#00ff00',\n        secondary: '#00cc00',\n        success: '#00ff00',\n        warning: '#ffff00',\n        error: '#ff0000',\n        info: '#00ffff',\n      },\n      border: {\n        primary: '#00ff00',\n        secondary: '#00cc00',\n        focus: '#00ff00',\n      },\n      glow: {\n        primary: 'rgba(0, 255, 0, 0.5)',\n        secondary: 'rgba(0, 204, 0, 0.5)',\n        accent: 'rgba(0, 255, 0, 0.7)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-sm',\n      shadow: '0 0 20px rgba(0, 255, 0, 0.4)',\n      glow: '0 0 30px rgba(0, 255, 0, 0.8)',\n      gradient: 'linear-gradient(135deg, #00ff00 0%, #00cc00 100%)',\n      hover: {\n        scale: 'scale-105',\n        glow: '0 0 40px rgba(0, 255, 0, 1)',\n        shadow: '0 10px 30px -5px rgba(0, 255, 0, 0.6)',\n      },\n    },\n  },\n};\n\ninterface ThemeContextType {\n  currentTheme: ThemeType;\n  theme: Theme;\n  setTheme: (theme: ThemeType) => void;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [currentTheme, setCurrentTheme] = useState<ThemeType>('light');\n\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('expense-tracker-theme') as ThemeType;\n    if (savedTheme && themes[savedTheme]) {\n      setCurrentTheme(savedTheme);\n    }\n  }, []);\n\n  const setTheme = (theme: ThemeType) => {\n    setCurrentTheme(theme);\n    localStorage.setItem('expense-tracker-theme', theme);\n    \n    // Apply CSS custom properties\n    const root = document.documentElement;\n    const themeColors = themes[theme].colors;\n    \n    // Set CSS variables\n    root.style.setProperty('--bg-primary', themeColors.bg.primary);\n    root.style.setProperty('--bg-secondary', themeColors.bg.secondary);\n    root.style.setProperty('--bg-tertiary', themeColors.bg.tertiary);\n    root.style.setProperty('--bg-card', themeColors.bg.card);\n    root.style.setProperty('--bg-modal', themeColors.bg.modal);\n    \n    root.style.setProperty('--text-primary', themeColors.text.primary);\n    root.style.setProperty('--text-secondary', themeColors.text.secondary);\n    root.style.setProperty('--text-tertiary', themeColors.text.tertiary);\n    root.style.setProperty('--text-inverse', themeColors.text.inverse);\n    \n    root.style.setProperty('--accent-primary', themeColors.accent.primary);\n    root.style.setProperty('--accent-secondary', themeColors.accent.secondary);\n    root.style.setProperty('--accent-success', themeColors.accent.success);\n    root.style.setProperty('--accent-warning', themeColors.accent.warning);\n    root.style.setProperty('--accent-error', themeColors.accent.error);\n    root.style.setProperty('--accent-info', themeColors.accent.info);\n    \n    root.style.setProperty('--border-primary', themeColors.border.primary);\n    root.style.setProperty('--border-secondary', themeColors.border.secondary);\n    root.style.setProperty('--border-focus', themeColors.border.focus);\n    \n    root.style.setProperty('--glow-primary', themeColors.glow.primary);\n    root.style.setProperty('--glow-secondary', themeColors.glow.secondary);\n    root.style.setProperty('--glow-accent', themeColors.glow.accent);\n  };\n\n  const toggleTheme = () => {\n    const themeOrder: ThemeType[] = ['light', 'dark', 'cyber', 'neon', 'matrix'];\n    const currentIndex = themeOrder.indexOf(currentTheme);\n    const nextIndex = (currentIndex + 1) % themeOrder.length;\n    setTheme(themeOrder[nextIndex]);\n  };\n\n  const value = {\n    currentTheme,\n    theme: themes[currentTheme],\n    setTheme,\n    toggleTheme,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AA6DO,MAAM,SAAmC;IAC9C,OAAO;QACL,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IACA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IACA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;AACF;AASA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,cAAc,MAAM,CAAC,WAAW,EAAE;YACpC,gBAAgB;QAClB;IACF,GAAG,EAAE;IAEL,MAAM,WAAW,CAAC;QAChB,gBAAgB;QAChB,aAAa,OAAO,CAAC,yBAAyB;QAE9C,8BAA8B;QAC9B,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,cAAc,MAAM,CAAC,MAAM,CAAC,MAAM;QAExC,oBAAoB;QACpB,KAAK,KAAK,CAAC,WAAW,CAAC,gBAAgB,YAAY,EAAE,CAAC,OAAO;QAC7D,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,EAAE,CAAC,SAAS;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,iBAAiB,YAAY,EAAE,CAAC,QAAQ;QAC/D,KAAK,KAAK,CAAC,WAAW,CAAC,aAAa,YAAY,EAAE,CAAC,IAAI;QACvD,KAAK,KAAK,CAAC,WAAW,CAAC,cAAc,YAAY,EAAE,CAAC,KAAK;QAEzD,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,IAAI,CAAC,OAAO;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,IAAI,CAAC,SAAS;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB,YAAY,IAAI,CAAC,QAAQ;QACnE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,IAAI,CAAC,OAAO;QAEjE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,sBAAsB,YAAY,MAAM,CAAC,SAAS;QACzE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,MAAM,CAAC,KAAK;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,iBAAiB,YAAY,MAAM,CAAC,IAAI;QAE/D,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,sBAAsB,YAAY,MAAM,CAAC,SAAS;QACzE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,MAAM,CAAC,KAAK;QAEjE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,IAAI,CAAC,OAAO;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,IAAI,CAAC,SAAS;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,iBAAiB,YAAY,IAAI,CAAC,MAAM;IACjE;IAEA,MAAM,cAAc;QAClB,MAAM,aAA0B;YAAC;YAAS;YAAQ;YAAS;YAAQ;SAAS;QAC5E,MAAM,eAAe,WAAW,OAAO,CAAC;QACxC,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,WAAW,MAAM;QACxD,SAAS,UAAU,CAAC,UAAU;IAChC;IAEA,MAAM,QAAQ;QACZ;QACA,OAAO,MAAM,CAAC,aAAa;QAC3B;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\n// Supported languages with their metadata\nexport const SUPPORTED_LANGUAGES = {\n  en: {\n    code: 'en',\n    name: 'English',\n    nativeName: 'English',\n    flag: '🇺🇸',\n    rtl: false,\n  },\n  zh: {\n    code: 'zh',\n    name: 'Chinese',\n    nativeName: '中文',\n    flag: '🇨🇳',\n    rtl: false,\n  },\n  hi: {\n    code: 'hi',\n    name: 'Hindi',\n    nativeName: 'हिन्दी',\n    flag: '🇮🇳',\n    rtl: false,\n  },\n  es: {\n    code: 'es',\n    name: 'Spanish',\n    nativeName: 'Español',\n    flag: '🇪🇸',\n    rtl: false,\n  },\n  ar: {\n    code: 'ar',\n    name: 'Arabic',\n    nativeName: 'العربية',\n    flag: '🇸🇦',\n    rtl: true,\n  },\n  bn: {\n    code: 'bn',\n    name: 'Bengali',\n    nativeName: 'বাংলা',\n    flag: '🇧🇩',\n    rtl: false,\n  },\n  fr: {\n    code: 'fr',\n    name: 'French',\n    nativeName: 'Français',\n    flag: '🇫🇷',\n    rtl: false,\n  },\n  ru: {\n    code: 'ru',\n    name: 'Russian',\n    nativeName: 'Русский',\n    flag: '🇷🇺',\n    rtl: false,\n  },\n  pt: {\n    code: 'pt',\n    name: 'Portuguese',\n    nativeName: 'Português',\n    flag: '🇧🇷',\n    rtl: false,\n  },\n  ur: {\n    code: 'ur',\n    name: 'Urdu',\n    nativeName: 'اردو',\n    flag: '🇵🇰',\n    rtl: true,\n  },\n  id: {\n    code: 'id',\n    name: 'Indonesian',\n    nativeName: 'Bahasa Indonesia',\n    flag: '🇮🇩',\n    rtl: false,\n  },\n  sw: {\n    code: 'sw',\n    name: 'Swahili',\n    nativeName: 'Kiswahili',\n    flag: '🇰🇪',\n    rtl: false,\n  },\n  pa: {\n    code: 'pa',\n    name: 'Punjabi',\n    nativeName: 'ਪੰਜਾਬੀ',\n    flag: '🇮🇳',\n    rtl: false,\n  },\n  ko: {\n    code: 'ko',\n    name: 'Korean',\n    nativeName: '한국어',\n    flag: '🇰🇷',\n    rtl: false,\n  },\n  de: {\n    code: 'de',\n    name: 'German',\n    nativeName: 'Deutsch',\n    flag: '🇩🇪',\n    rtl: false,\n  },\n  ja: {\n    code: 'ja',\n    name: 'Japanese',\n    nativeName: '日本語',\n    flag: '🇯🇵',\n    rtl: false,\n  },\n} as const;\n\nexport type LanguageCode = keyof typeof SUPPORTED_LANGUAGES;\n\ninterface LanguageContextType {\n  currentLanguage: LanguageCode;\n  setLanguage: (language: LanguageCode) => void;\n  t: (text: string) => string;\n  isRTL: boolean;\n  languageData: typeof SUPPORTED_LANGUAGES[LanguageCode];\n  isTranslating: boolean;\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\ninterface LanguageProviderProps {\n  children: ReactNode;\n}\n\n// Translation cache to avoid repeated API calls\nconst translationCache = new Map<string, string>();\n\n// Detect browser language\nfunction detectBrowserLanguage(): LanguageCode {\n  if (typeof window === 'undefined') return 'en';\n\n  const browserLang = navigator.language.split('-')[0] as LanguageCode;\n  return SUPPORTED_LANGUAGES[browserLang] ? browserLang : 'en';\n}\n\n// Auto-translate function using Google Translate API (free tier)\nasync function translateText(text: string, targetLang: LanguageCode): Promise<string> {\n  if (targetLang === 'en') return text; // No translation needed for English\n\n  const cacheKey = `${text}:${targetLang}`;\n\n  // Check cache first\n  if (translationCache.has(cacheKey)) {\n    return translationCache.get(cacheKey)!;\n  }\n\n  try {\n    // Using Google Translate API (you can replace with other services)\n    const response = await fetch(\n      `https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=${targetLang}&dt=t&q=${encodeURIComponent(text)}`\n    );\n\n    if (!response.ok) {\n      throw new Error('Translation failed');\n    }\n\n    const data = await response.json();\n    const translatedText = data[0]?.[0]?.[0] || text;\n\n    // Cache the translation\n    translationCache.set(cacheKey, translatedText);\n\n    return translatedText;\n  } catch (error) {\n    console.warn('Translation failed, using original text:', error);\n    return text;\n  }\n}\n\nexport function LanguageProvider({ children }: LanguageProviderProps) {\n  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>('en');\n  const [isTranslating, setIsTranslating] = useState(false);\n\n  // Initialize language from localStorage or browser detection\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('expense-tracker-language') as LanguageCode;\n    const initialLanguage = savedLanguage || detectBrowserLanguage();\n    \n    if (SUPPORTED_LANGUAGES[initialLanguage]) {\n      setCurrentLanguage(initialLanguage);\n    }\n  }, []);\n\n  // Clear translation cache when language changes to force re-translation\n  useEffect(() => {\n    translationCache.clear();\n  }, [currentLanguage]);\n\n  // Update document direction for RTL languages\n  useEffect(() => {\n    const isRTL = SUPPORTED_LANGUAGES[currentLanguage].rtl;\n    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';\n    document.documentElement.lang = currentLanguage;\n\n    // Add RTL class to body for styling\n    if (isRTL) {\n      document.body.classList.add('rtl');\n    } else {\n      document.body.classList.remove('rtl');\n    }\n  }, [currentLanguage]);\n\n  const setLanguage = (language: LanguageCode) => {\n    setCurrentLanguage(language);\n    localStorage.setItem('expense-tracker-language', language);\n  };\n\n  // Auto-translate function with caching\n  const t = (text: string): string => {\n    if (currentLanguage === 'en') {\n      return text; // No translation needed for English\n    }\n\n    const cacheKey = `${text}:${currentLanguage}`;\n\n    // Check cache first\n    if (translationCache.has(cacheKey)) {\n      return translationCache.get(cacheKey)!;\n    }\n\n    // If not in cache, start translation and return original text temporarily\n    setIsTranslating(true);\n    translateText(text, currentLanguage)\n      .then((translated) => {\n        setIsTranslating(false);\n        // Force re-render by updating a dummy state or using a callback\n        // This is a simplified approach - in production, you might want to use a more sophisticated state management\n      })\n      .catch(() => {\n        setIsTranslating(false);\n      });\n\n    return text; // Return original text while translating\n  };\n\n  const value: LanguageContextType = {\n    currentLanguage,\n    setLanguage,\n    t,\n    isRTL: SUPPORTED_LANGUAGES[currentLanguage].rtl,\n    languageData: SUPPORTED_LANGUAGES[currentLanguage],\n    isTranslating,\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n}\n\n// Custom hook for auto-translation with real-time updates\nexport function useTranslate(text: string): string {\n  const { currentLanguage } = useLanguage();\n  const [translatedText, setTranslatedText] = useState(text);\n\n  useEffect(() => {\n    if (currentLanguage === 'en') {\n      setTranslatedText(text);\n      return;\n    }\n\n    const cacheKey = `${text}:${currentLanguage}`;\n\n    // Check cache first\n    if (translationCache.has(cacheKey)) {\n      setTranslatedText(translationCache.get(cacheKey)!);\n      return;\n    }\n\n    // Translate if not in cache\n    translateText(text, currentLanguage)\n      .then((translated) => {\n        setTranslatedText(translated);\n      })\n      .catch(() => {\n        setTranslatedText(text);\n      });\n  }, [text, currentLanguage]);\n\n  return translatedText;\n}\n\n// Batch translation hook for multiple texts\nexport function useBatchTranslate(texts: string[]): string[] {\n  const { currentLanguage } = useLanguage();\n  const [translatedTexts, setTranslatedTexts] = useState(texts);\n\n  useEffect(() => {\n    if (currentLanguage === 'en') {\n      setTranslatedTexts(texts);\n      return;\n    }\n\n    const translateBatch = async () => {\n      const results = await Promise.all(\n        texts.map(text => translateText(text, currentLanguage))\n      );\n      setTranslatedTexts(results);\n    };\n\n    translateBatch();\n  }, [texts, currentLanguage]);\n\n  return translatedTexts;\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAFA;;;AAKO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;AACF;AAaA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAMvE,gDAAgD;AAChD,MAAM,mBAAmB,IAAI;AAE7B,0BAA0B;AAC1B,SAAS;IACP,wCAAmC,OAAO;;;IAE1C,MAAM;AAER;AAEA,iEAAiE;AACjE,eAAe,cAAc,IAAY,EAAE,UAAwB;IACjE,IAAI,eAAe,MAAM,OAAO,MAAM,oCAAoC;IAE1E,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,YAAY;IAExC,oBAAoB;IACpB,IAAI,iBAAiB,GAAG,CAAC,WAAW;QAClC,OAAO,iBAAiB,GAAG,CAAC;IAC9B;IAEA,IAAI;QACF,mEAAmE;QACnE,MAAM,WAAW,MAAM,MACrB,CAAC,wEAAwE,EAAE,WAAW,QAAQ,EAAE,mBAAmB,OAAO;QAG5H,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,iBAAiB,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI;QAE5C,wBAAwB;QACxB,iBAAiB,GAAG,CAAC,UAAU;QAE/B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4CAA4C;QACzD,OAAO;IACT;AACF;AAEO,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,MAAM,kBAAkB,iBAAiB;QAEzC,IAAI,mBAAmB,CAAC,gBAAgB,EAAE;YACxC,mBAAmB;QACrB;IACF,GAAG,EAAE;IAEL,wEAAwE;IACxE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB,KAAK;IACxB,GAAG;QAAC;KAAgB;IAEpB,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,mBAAmB,CAAC,gBAAgB,CAAC,GAAG;QACtD,SAAS,eAAe,CAAC,GAAG,GAAG,QAAQ,QAAQ;QAC/C,SAAS,eAAe,CAAC,IAAI,GAAG;QAEhC,oCAAoC;QACpC,IAAI,OAAO;YACT,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC9B,OAAO;YACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAC;QACnB,mBAAmB;QACnB,aAAa,OAAO,CAAC,4BAA4B;IACnD;IAEA,uCAAuC;IACvC,MAAM,IAAI,CAAC;QACT,IAAI,oBAAoB,MAAM;YAC5B,OAAO,MAAM,oCAAoC;QACnD;QAEA,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,iBAAiB;QAE7C,oBAAoB;QACpB,IAAI,iBAAiB,GAAG,CAAC,WAAW;YAClC,OAAO,iBAAiB,GAAG,CAAC;QAC9B;QAEA,0EAA0E;QAC1E,iBAAiB;QACjB,cAAc,MAAM,iBACjB,IAAI,CAAC,CAAC;YACL,iBAAiB;QACjB,gEAAgE;QAChE,6GAA6G;QAC/G,GACC,KAAK,CAAC;YACL,iBAAiB;QACnB;QAEF,OAAO,MAAM,yCAAyC;IACxD;IAEA,MAAM,QAA6B;QACjC;QACA;QACA;QACA,OAAO,mBAAmB,CAAC,gBAAgB,CAAC,GAAG;QAC/C,cAAc,mBAAmB,CAAC,gBAAgB;QAClD;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,aAAa,IAAY;IACvC,MAAM,EAAE,eAAe,EAAE,GAAG;IAC5B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,oBAAoB,MAAM;YAC5B,kBAAkB;YAClB;QACF;QAEA,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,iBAAiB;QAE7C,oBAAoB;QACpB,IAAI,iBAAiB,GAAG,CAAC,WAAW;YAClC,kBAAkB,iBAAiB,GAAG,CAAC;YACvC;QACF;QAEA,4BAA4B;QAC5B,cAAc,MAAM,iBACjB,IAAI,CAAC,CAAC;YACL,kBAAkB;QACpB,GACC,KAAK,CAAC;YACL,kBAAkB;QACpB;IACJ,GAAG;QAAC;QAAM;KAAgB;IAE1B,OAAO;AACT;AAGO,SAAS,kBAAkB,KAAe;IAC/C,MAAM,EAAE,eAAe,EAAE,GAAG;IAC5B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,oBAAoB,MAAM;YAC5B,mBAAmB;YACnB;QACF;QAEA,MAAM,iBAAiB;YACrB,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,MAAM,GAAG,CAAC,CAAA,OAAQ,cAAc,MAAM;YAExC,mBAAmB;QACrB;QAEA;IACF,GAAG;QAAC;QAAO;KAAgB;IAE3B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}