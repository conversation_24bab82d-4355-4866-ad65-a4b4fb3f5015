{"version": 3, "sources": [], "sections": [{"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/lib/ocr.ts"], "sourcesContent": ["import Tesseract from 'tesseract.js';\n\nexport interface OCRResult {\n  text: string;\n  confidence: number;\n  words: Array<{\n    text: string;\n    confidence: number;\n    bbox: {\n      x0: number;\n      y0: number;\n      x1: number;\n      y1: number;\n    };\n  }>;\n}\n\nexport interface ProcessedImage {\n  canvas: HTMLCanvasElement;\n  dataUrl: string;\n}\n\nexport interface ImageProcessingOptions {\n  contrast: number;\n  brightness: number;\n  sharpen: boolean;\n  denoise: boolean;\n  autoRotate: boolean;\n}\n\n/**\n * Advanced image preprocessing for optimal OCR results\n */\nexport const preprocessImage = (\n  file: File,\n  options: ImageProcessingOptions = {\n    contrast: 1.5,\n    brightness: 1.1,\n    sharpen: true,\n    denoise: true,\n    autoRotate: true\n  }\n): Promise<ProcessedImage> => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n\n    if (!ctx) {\n      reject(new Error('Could not get canvas context'));\n      return;\n    }\n\n    img.onload = () => {\n      // Auto-detect and correct rotation\n      let { width, height } = img;\n      let rotation = 0;\n\n      if (options.autoRotate) {\n        // Simple heuristic: if width > height significantly, might need rotation\n        if (width > height * 1.5) {\n          rotation = 0; // Landscape, likely correct\n        } else if (height > width * 1.5) {\n          rotation = 90; // Portrait, might need rotation for receipt\n        }\n      }\n\n      // Set canvas size accounting for rotation\n      if (rotation === 90 || rotation === 270) {\n        canvas.width = height;\n        canvas.height = width;\n      } else {\n        canvas.width = width;\n        canvas.height = height;\n      }\n\n      // Apply rotation if needed\n      if (rotation !== 0) {\n        ctx.translate(canvas.width / 2, canvas.height / 2);\n        ctx.rotate((rotation * Math.PI) / 180);\n        ctx.translate(-width / 2, -height / 2);\n      }\n\n      // Draw original image\n      ctx.drawImage(img, 0, 0);\n\n      // Get image data for processing\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // Advanced image processing\n      for (let i = 0; i < data.length; i += 4) {\n        // Convert to grayscale using luminance formula\n        let gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);\n\n        // Apply brightness\n        gray = Math.min(255, Math.max(0, gray * options.brightness));\n\n        // Apply contrast\n        const factor = (259 * (options.contrast * 255 + 255)) / (255 * (259 - options.contrast * 255));\n        gray = Math.min(255, Math.max(0, factor * (gray - 128) + 128));\n\n        // Apply to all channels\n        data[i] = gray;     // Red\n        data[i + 1] = gray; // Green\n        data[i + 2] = gray; // Blue\n        // Alpha channel (data[i + 3]) remains unchanged\n      }\n\n      // Apply sharpening filter if enabled\n      if (options.sharpen) {\n        applySharpenFilter(data, canvas.width, canvas.height);\n      }\n\n      // Apply denoising if enabled\n      if (options.denoise) {\n        applyDenoiseFilter(data, canvas.width, canvas.height);\n      }\n\n      // Put processed image data back\n      ctx.putImageData(imageData, 0, 0);\n\n      resolve({\n        canvas,\n        dataUrl: canvas.toDataURL('image/png')\n      });\n    };\n\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n\n    // Create object URL from file\n    const objectUrl = URL.createObjectURL(file);\n    img.src = objectUrl;\n  });\n};\n\n/**\n * Apply sharpening filter to image data\n */\nconst applySharpenFilter = (data: Uint8ClampedArray, width: number, height: number) => {\n  const kernel = [\n    0, -1, 0,\n    -1, 5, -1,\n    0, -1, 0\n  ];\n\n  const output = new Uint8ClampedArray(data);\n\n  for (let y = 1; y < height - 1; y++) {\n    for (let x = 1; x < width - 1; x++) {\n      let sum = 0;\n      for (let ky = -1; ky <= 1; ky++) {\n        for (let kx = -1; kx <= 1; kx++) {\n          const idx = ((y + ky) * width + (x + kx)) * 4;\n          sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];\n        }\n      }\n      const idx = (y * width + x) * 4;\n      const value = Math.min(255, Math.max(0, sum));\n      output[idx] = value;\n      output[idx + 1] = value;\n      output[idx + 2] = value;\n    }\n  }\n\n  data.set(output);\n};\n\n/**\n * Apply simple denoising filter\n */\nconst applyDenoiseFilter = (data: Uint8ClampedArray, width: number, height: number) => {\n  const output = new Uint8ClampedArray(data);\n\n  for (let y = 1; y < height - 1; y++) {\n    for (let x = 1; x < width - 1; x++) {\n      let sum = 0;\n      let count = 0;\n\n      // 3x3 median filter\n      for (let ky = -1; ky <= 1; ky++) {\n        for (let kx = -1; kx <= 1; kx++) {\n          const idx = ((y + ky) * width + (x + kx)) * 4;\n          sum += data[idx];\n          count++;\n        }\n      }\n\n      const idx = (y * width + x) * 4;\n      const value = Math.round(sum / count);\n      output[idx] = value;\n      output[idx + 1] = value;\n      output[idx + 2] = value;\n    }\n  }\n\n  data.set(output);\n};\n\n/**\n * Extract text from image using advanced Tesseract.js configuration\n */\nexport const extractTextFromImage = async (\n  imageSource: string | File | HTMLCanvasElement,\n  options?: {\n    language?: string;\n    logger?: (info: any) => void;\n    psm?: number;\n    oem?: number;\n  }\n): Promise<OCRResult> => {\n  try {\n    const {\n      language = 'eng',\n      logger,\n      psm = 6, // Assume a single uniform block of text\n      oem = 3  // Default OCR Engine Mode\n    } = options || {};\n\n    // Advanced Tesseract configuration for receipts\n    const result = await Tesseract.recognize(\n      imageSource,\n      language,\n      {\n        logger: logger || (() => {}),\n        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/$-:()# \\n\\t',\n        tessedit_pageseg_mode: psm,\n        tessedit_ocr_engine_mode: oem,\n        preserve_interword_spaces: '1',\n        user_defined_dpi: '300',\n        // Receipt-specific optimizations\n        textord_heavy_nr: '1',\n        textord_noise_normratio: '2',\n        textord_noise_sizelimit: '0.5',\n        // Improve number recognition\n        classify_enable_learning: '0',\n        classify_enable_adaptive_matcher: '0',\n      }\n    );\n\n    // Extract word-level data with confidence scores\n    const words = result.data.words.map(word => ({\n      text: word.text,\n      confidence: word.confidence,\n      bbox: {\n        x0: word.bbox.x0,\n        y0: word.bbox.y0,\n        x1: word.bbox.x1,\n        y1: word.bbox.y1,\n      }\n    }));\n\n    return {\n      text: result.data.text,\n      confidence: result.data.confidence,\n      words\n    };\n  } catch (error) {\n    console.error('OCR Error:', error);\n    throw new Error('Failed to extract text from image');\n  }\n};\n\n/**\n * Multi-pass OCR with different configurations for better accuracy\n */\nexport const extractTextMultiPass = async (\n  imageSource: string | File | HTMLCanvasElement,\n  onProgress?: (progress: number, pass: string) => void\n): Promise<OCRResult> => {\n  const passes = [\n    { name: 'Standard', psm: 6, oem: 3 },\n    { name: 'Single Block', psm: 8, oem: 3 },\n    { name: 'Single Line', psm: 7, oem: 3 },\n    { name: 'Sparse Text', psm: 11, oem: 3 }\n  ];\n\n  let bestResult: OCRResult | null = null;\n  let bestConfidence = 0;\n\n  for (let i = 0; i < passes.length; i++) {\n    const pass = passes[i];\n    onProgress?.(((i + 1) / passes.length) * 100, pass.name);\n\n    try {\n      const result = await extractTextFromImage(imageSource, {\n        psm: pass.psm,\n        oem: pass.oem,\n        logger: (info) => {\n          if (info.status === 'recognizing text') {\n            const passProgress = (i / passes.length) * 100;\n            const currentProgress = passProgress + (info.progress * (100 / passes.length));\n            onProgress?.(currentProgress, `${pass.name} - ${Math.round(info.progress * 100)}%`);\n          }\n        }\n      });\n\n      if (result.confidence > bestConfidence) {\n        bestResult = result;\n        bestConfidence = result.confidence;\n      }\n    } catch (error) {\n      console.warn(`OCR pass ${pass.name} failed:`, error);\n    }\n  }\n\n  if (!bestResult) {\n    throw new Error('All OCR passes failed');\n  }\n\n  return bestResult;\n};\n\n/**\n * Advanced receipt processing with multiple enhancement techniques\n */\nexport const processReceiptImage = async (\n  file: File,\n  onProgress?: (progress: number, status: string) => void\n): Promise<OCRResult> => {\n  try {\n    onProgress?.(5, 'Analyzing image...');\n\n    // Step 1: Preprocess image with optimal settings for receipts\n    const processedImage = await preprocessImage(file, {\n      contrast: 1.8,\n      brightness: 1.2,\n      sharpen: true,\n      denoise: true,\n      autoRotate: true\n    });\n\n    onProgress?.(20, 'Image enhanced, starting OCR...');\n\n    // Step 2: Multi-pass OCR for best results\n    const result = await extractTextMultiPass(\n      processedImage.canvas,\n      (progress, pass) => {\n        const overallProgress = 20 + (progress * 0.8);\n        onProgress?.(Math.round(overallProgress), `OCR Pass: ${pass}`);\n      }\n    );\n\n    onProgress?.(100, 'Processing complete!');\n    return result;\n  } catch (error) {\n    console.error('Receipt processing error:', error);\n    throw error;\n  }\n};\n\n/**\n * Process receipt with fallback strategies\n */\nexport const processReceiptWithFallback = async (\n  file: File,\n  onProgress?: (progress: number, status: string) => void\n): Promise<OCRResult> => {\n  const strategies = [\n    {\n      name: 'High Quality',\n      options: { contrast: 1.8, brightness: 1.2, sharpen: true, denoise: true, autoRotate: true }\n    },\n    {\n      name: 'High Contrast',\n      options: { contrast: 2.2, brightness: 1.0, sharpen: true, denoise: false, autoRotate: true }\n    },\n    {\n      name: 'Minimal Processing',\n      options: { contrast: 1.2, brightness: 1.1, sharpen: false, denoise: false, autoRotate: false }\n    }\n  ];\n\n  let bestResult: OCRResult | null = null;\n  let bestConfidence = 0;\n\n  for (let i = 0; i < strategies.length; i++) {\n    const strategy = strategies[i];\n\n    try {\n      onProgress?.(\n        (i / strategies.length) * 100,\n        `Trying ${strategy.name} processing...`\n      );\n\n      const processedImage = await preprocessImage(file, strategy.options);\n      const result = await extractTextFromImage(processedImage.canvas, {\n        logger: (info) => {\n          if (info.status === 'recognizing text') {\n            const strategyProgress = (i / strategies.length) * 100;\n            const currentProgress = strategyProgress + (info.progress * (100 / strategies.length));\n            onProgress?.(currentProgress, `${strategy.name}: ${Math.round(info.progress * 100)}%`);\n          }\n        }\n      });\n\n      if (result.confidence > bestConfidence) {\n        bestResult = result;\n        bestConfidence = result.confidence;\n      }\n\n      // If we get good confidence, use it\n      if (result.confidence > 80) {\n        break;\n      }\n    } catch (error) {\n      console.warn(`Strategy ${strategy.name} failed:`, error);\n    }\n  }\n\n  if (!bestResult) {\n    throw new Error('All processing strategies failed');\n  }\n\n  onProgress?.(100, `Best result: ${bestResult.confidence.toFixed(1)}% confidence`);\n  return bestResult;\n};\n\n/**\n * Validate image file\n */\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\n  const maxSize = 10 * 1024 * 1024; // 10MB\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n  if (!allowedTypes.includes(file.type)) {\n    return {\n      valid: false,\n      error: 'Please upload a valid image file (JPEG, PNG, or WebP)'\n    };\n  }\n\n  if (file.size > maxSize) {\n    return {\n      valid: false,\n      error: 'Image file size must be less than 10MB'\n    };\n  }\n\n  return { valid: true };\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAiCO,MAAM,kBAAkB,CAC7B,MACA,UAAkC;IAChC,UAAU;IACV,YAAY;IACZ,SAAS;IACT,SAAS;IACT,YAAY;AACd,CAAC;IAED,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,CAAC,KAAK;YACR,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,IAAI,MAAM,GAAG;YACX,mCAAmC;YACnC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;YACxB,IAAI,WAAW;YAEf,IAAI,QAAQ,UAAU,EAAE;gBACtB,yEAAyE;gBACzE,IAAI,QAAQ,SAAS,KAAK;oBACxB,WAAW,GAAG,4BAA4B;gBAC5C,OAAO,IAAI,SAAS,QAAQ,KAAK;oBAC/B,WAAW,IAAI,4CAA4C;gBAC7D;YACF;YAEA,0CAA0C;YAC1C,IAAI,aAAa,MAAM,aAAa,KAAK;gBACvC,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;YAClB,OAAO;gBACL,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;YAClB;YAEA,2BAA2B;YAC3B,IAAI,aAAa,GAAG;gBAClB,IAAI,SAAS,CAAC,OAAO,KAAK,GAAG,GAAG,OAAO,MAAM,GAAG;gBAChD,IAAI,MAAM,CAAC,AAAC,WAAW,KAAK,EAAE,GAAI;gBAClC,IAAI,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,SAAS;YACtC;YAEA,sBAAsB;YACtB,IAAI,SAAS,CAAC,KAAK,GAAG;YAEtB,gCAAgC;YAChC,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YACpE,MAAM,OAAO,UAAU,IAAI;YAE3B,4BAA4B;YAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;gBACvC,+CAA+C;gBAC/C,IAAI,OAAO,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;gBAEjF,mBAAmB;gBACnB,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,OAAO,QAAQ,UAAU;gBAE1D,iBAAiB;gBACjB,MAAM,SAAS,AAAC,MAAM,CAAC,QAAQ,QAAQ,GAAG,MAAM,GAAG,IAAK,CAAC,MAAM,CAAC,MAAM,QAAQ,QAAQ,GAAG,GAAG,CAAC;gBAC7F,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,IAAI;gBAEzD,wBAAwB;gBACxB,IAAI,CAAC,EAAE,GAAG,MAAU,MAAM;gBAC1B,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,QAAQ;gBAC5B,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,OAAO;YAC3B,gDAAgD;YAClD;YAEA,qCAAqC;YACrC,IAAI,QAAQ,OAAO,EAAE;gBACnB,mBAAmB,MAAM,OAAO,KAAK,EAAE,OAAO,MAAM;YACtD;YAEA,6BAA6B;YAC7B,IAAI,QAAQ,OAAO,EAAE;gBACnB,mBAAmB,MAAM,OAAO,KAAK,EAAE,OAAO,MAAM;YACtD;YAEA,gCAAgC;YAChC,IAAI,YAAY,CAAC,WAAW,GAAG;YAE/B,QAAQ;gBACN;gBACA,SAAS,OAAO,SAAS,CAAC;YAC5B;QACF;QAEA,IAAI,OAAO,GAAG;YACZ,OAAO,IAAI,MAAM;QACnB;QAEA,8BAA8B;QAC9B,MAAM,YAAY,IAAI,eAAe,CAAC;QACtC,IAAI,GAAG,GAAG;IACZ;AACF;AAEA;;CAEC,GACD,MAAM,qBAAqB,CAAC,MAAyB,OAAe;IAClE,MAAM,SAAS;QACb;QAAG,CAAC;QAAG;QACP,CAAC;QAAG;QAAG,CAAC;QACR;QAAG,CAAC;QAAG;KACR;IAED,MAAM,SAAS,IAAI,kBAAkB;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAAK;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;YAClC,IAAI,MAAM;YACV,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAC/B,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;oBAC/B,MAAM,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI;oBAC5C,OAAO,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;gBACpD;YACF;YACA,MAAM,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI;YAC9B,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;YACxC,MAAM,CAAC,IAAI,GAAG;YACd,MAAM,CAAC,MAAM,EAAE,GAAG;YAClB,MAAM,CAAC,MAAM,EAAE,GAAG;QACpB;IACF;IAEA,KAAK,GAAG,CAAC;AACX;AAEA;;CAEC,GACD,MAAM,qBAAqB,CAAC,MAAyB,OAAe;IAClE,MAAM,SAAS,IAAI,kBAAkB;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAAK;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;YAClC,IAAI,MAAM;YACV,IAAI,QAAQ;YAEZ,oBAAoB;YACpB,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAC/B,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;oBAC/B,MAAM,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI;oBAC5C,OAAO,IAAI,CAAC,IAAI;oBAChB;gBACF;YACF;YAEA,MAAM,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI;YAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM;YAC/B,MAAM,CAAC,IAAI,GAAG;YACd,MAAM,CAAC,MAAM,EAAE,GAAG;YAClB,MAAM,CAAC,MAAM,EAAE,GAAG;QACpB;IACF;IAEA,KAAK,GAAG,CAAC;AACX;AAKO,MAAM,uBAAuB,OAClC,aACA;IAOA,IAAI;QACF,MAAM,EACJ,WAAW,KAAK,EAChB,MAAM,EACN,MAAM,CAAC,EACP,MAAM,EAAG,0BAA0B;QAA5B,EACR,GAAG,WAAW,CAAC;QAEhB,gDAAgD;QAChD,MAAM,SAAS,MAAM,+IAAA,CAAA,UAAS,CAAC,SAAS,CACtC,aACA,UACA;YACE,QAAQ,UAAU,CAAC,KAAO,CAAC;YAC3B,yBAAyB;YACzB,uBAAuB;YACvB,0BAA0B;YAC1B,2BAA2B;YAC3B,kBAAkB;YAClB,iCAAiC;YACjC,kBAAkB;YAClB,yBAAyB;YACzB,yBAAyB;YACzB,6BAA6B;YAC7B,0BAA0B;YAC1B,kCAAkC;QACpC;QAGF,iDAAiD;QACjD,MAAM,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC3C,MAAM,KAAK,IAAI;gBACf,YAAY,KAAK,UAAU;gBAC3B,MAAM;oBACJ,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,IAAI,KAAK,IAAI,CAAC,EAAE;gBAClB;YACF,CAAC;QAED,OAAO;YACL,MAAM,OAAO,IAAI,CAAC,IAAI;YACtB,YAAY,OAAO,IAAI,CAAC,UAAU;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,uBAAuB,OAClC,aACA;IAEA,MAAM,SAAS;QACb;YAAE,MAAM;YAAY,KAAK;YAAG,KAAK;QAAE;QACnC;YAAE,MAAM;YAAgB,KAAK;YAAG,KAAK;QAAE;QACvC;YAAE,MAAM;YAAe,KAAK;YAAG,KAAK;QAAE;QACtC;YAAE,MAAM;YAAe,KAAK;YAAI,KAAK;QAAE;KACxC;IAED,IAAI,aAA+B;IACnC,IAAI,iBAAiB;IAErB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,aAAa,AAAC,CAAC,IAAI,CAAC,IAAI,OAAO,MAAM,GAAI,KAAK,KAAK,IAAI;QAEvD,IAAI;YACF,MAAM,SAAS,MAAM,qBAAqB,aAAa;gBACrD,KAAK,KAAK,GAAG;gBACb,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAC;oBACP,IAAI,KAAK,MAAM,KAAK,oBAAoB;wBACtC,MAAM,eAAe,AAAC,IAAI,OAAO,MAAM,GAAI;wBAC3C,MAAM,kBAAkB,eAAgB,KAAK,QAAQ,GAAG,CAAC,MAAM,OAAO,MAAM;wBAC5E,aAAa,iBAAiB,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,GAAG,KAAK,CAAC,CAAC;oBACpF;gBACF;YACF;YAEA,IAAI,OAAO,UAAU,GAAG,gBAAgB;gBACtC,aAAa;gBACb,iBAAiB,OAAO,UAAU;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE;QAChD;IACF;IAEA,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAKO,MAAM,sBAAsB,OACjC,MACA;IAEA,IAAI;QACF,aAAa,GAAG;QAEhB,8DAA8D;QAC9D,MAAM,iBAAiB,MAAM,gBAAgB,MAAM;YACjD,UAAU;YACV,YAAY;YACZ,SAAS;YACT,SAAS;YACT,YAAY;QACd;QAEA,aAAa,IAAI;QAEjB,0CAA0C;QAC1C,MAAM,SAAS,MAAM,qBACnB,eAAe,MAAM,EACrB,CAAC,UAAU;YACT,MAAM,kBAAkB,KAAM,WAAW;YACzC,aAAa,KAAK,KAAK,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM;QAC/D;QAGF,aAAa,KAAK;QAClB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAKO,MAAM,6BAA6B,OACxC,MACA;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,SAAS;gBAAE,UAAU;gBAAK,YAAY;gBAAK,SAAS;gBAAM,SAAS;gBAAM,YAAY;YAAK;QAC5F;QACA;YACE,MAAM;YACN,SAAS;gBAAE,UAAU;gBAAK,YAAY;gBAAK,SAAS;gBAAM,SAAS;gBAAO,YAAY;YAAK;QAC7F;QACA;YACE,MAAM;YACN,SAAS;gBAAE,UAAU;gBAAK,YAAY;gBAAK,SAAS;gBAAO,SAAS;gBAAO,YAAY;YAAM;QAC/F;KACD;IAED,IAAI,aAA+B;IACnC,IAAI,iBAAiB;IAErB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,MAAM,WAAW,UAAU,CAAC,EAAE;QAE9B,IAAI;YACF,aACE,AAAC,IAAI,WAAW,MAAM,GAAI,KAC1B,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC;YAGzC,MAAM,iBAAiB,MAAM,gBAAgB,MAAM,SAAS,OAAO;YACnE,MAAM,SAAS,MAAM,qBAAqB,eAAe,MAAM,EAAE;gBAC/D,QAAQ,CAAC;oBACP,IAAI,KAAK,MAAM,KAAK,oBAAoB;wBACtC,MAAM,mBAAmB,AAAC,IAAI,WAAW,MAAM,GAAI;wBACnD,MAAM,kBAAkB,mBAAoB,KAAK,QAAQ,GAAG,CAAC,MAAM,WAAW,MAAM;wBACpF,aAAa,iBAAiB,GAAG,SAAS,IAAI,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,GAAG,KAAK,CAAC,CAAC;oBACvF;gBACF;YACF;YAEA,IAAI,OAAO,UAAU,GAAG,gBAAgB;gBACtC,aAAa;gBACb,iBAAiB,OAAO,UAAU;YACpC;YAEA,oCAAoC;YACpC,IAAI,OAAO,UAAU,GAAG,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE;QACpD;IACF;IAEA,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,aAAa,KAAK,CAAC,aAAa,EAAE,WAAW,UAAU,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC;IAChF,OAAO;AACT;AAKO,MAAM,oBAAoB,CAAC;IAChC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;IACzC,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAE3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/ReceiptUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { Upload, X, Loader2, CheckCircle, AlertCircle, Camera, Zap } from 'lucide-react';\nimport { processReceiptWithFallback } from '@/lib/ocr';\n\n// Simple receipt parser\nconst parseReceiptText = (text: string) => {\n  const lines = text.split('\\n').map(line => line.trim()).filter(line => line.length > 0);\n\n  // Extract merchant (usually first meaningful line)\n  let merchant = 'Unknown Merchant';\n  for (const line of lines.slice(0, 3)) {\n    if (line.length > 3 && !line.match(/^\\d+/) && !line.includes('$')) {\n      merchant = line;\n      break;\n    }\n  }\n\n  // Extract amount (look for TOTAL)\n  let amount = 0;\n  for (const line of lines) {\n    if (line.toUpperCase().includes('TOTAL')) {\n      const match = line.match(/\\$?(\\d+\\.?\\d*)/);\n      if (match) {\n        amount = parseFloat(match[1]);\n        break;\n      }\n    }\n  }\n\n  // If no total found, look for any dollar amount\n  if (amount === 0) {\n    for (const line of lines.reverse()) {\n      const match = line.match(/\\$(\\d+\\.\\d{2})/);\n      if (match) {\n        amount = parseFloat(match[1]);\n        break;\n      }\n    }\n  }\n\n  // Extract date\n  let date = new Date();\n  for (const line of lines) {\n    const dateMatch = line.match(/(\\d{1,2}\\/\\d{1,2}\\/\\d{4})/);\n    if (dateMatch) {\n      date = new Date(dateMatch[1]);\n      break;\n    }\n  }\n\n  // Suggest category based on merchant\n  let suggestedCategory = 'Other';\n  const merchantLower = merchant.toLowerCase();\n  if (merchantLower.includes('walmart') || merchantLower.includes('target') || merchantLower.includes('store')) {\n    suggestedCategory = 'Shopping';\n  } else if (merchantLower.includes('starbucks') || merchantLower.includes('restaurant') || merchantLower.includes('cafe')) {\n    suggestedCategory = 'Food & Dining';\n  } else if (merchantLower.includes('shell') || merchantLower.includes('gas') || merchantLower.includes('fuel')) {\n    suggestedCategory = 'Transportation';\n  }\n\n  return {\n    merchant,\n    amount,\n    date,\n    suggestedCategory,\n    rawText: text\n  };\n};\n\ninterface ReceiptUploadProps {\n  onDataExtracted: (data: any) => void;\n  onClose: () => void;\n}\n\nexport default function ReceiptUpload({ onDataExtracted, onClose }: ReceiptUploadProps) {\n  const [file, setFile] = useState<File | null>(null);\n  const [preview, setPreview] = useState<string | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [extractedData, setExtractedData] = useState<any>(null);\n  const [processingStatus, setProcessingStatus] = useState<string>('');\n  const [processingProgress, setProcessingProgress] = useState<number>(0);\n  const [useRealOCR, setUseRealOCR] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const validateImageFile = (file: File) => {\n    const maxSize = 10 * 1024 * 1024; // 10MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return { valid: false, error: 'Please upload a valid image file (JPEG, PNG, or WebP)' };\n    }\n\n    if (file.size > maxSize) {\n      return { valid: false, error: 'Image file size must be less than 10MB' };\n    }\n\n    return { valid: true };\n  };\n\n  const handleFileSelect = (selectedFile: File) => {\n    const validation = validateImageFile(selectedFile);\n    if (!validation.valid) {\n      setError(validation.error || 'Invalid file');\n      return;\n    }\n\n    setFile(selectedFile);\n    setError(null);\n    setExtractedData(null);\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n    };\n    reader.readAsDataURL(selectedFile);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    const droppedFile = e.dataTransfer.files[0];\n    if (droppedFile) {\n      handleFileSelect(droppedFile);\n    }\n  };\n\n  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = e.target.files?.[0];\n    if (selectedFile) {\n      handleFileSelect(selectedFile);\n    }\n  };\n\n  const processReceipt = async () => {\n    if (!file) return;\n\n    setIsProcessing(true);\n    setError(null);\n    setProcessingProgress(0);\n    setProcessingStatus('Starting...');\n\n    try {\n      let ocrText: string;\n\n      if (useRealOCR) {\n        // Use real OCR with Tesseract.js\n        const result = await processReceiptWithFallback(\n          file,\n          (progress, status) => {\n            setProcessingProgress(progress);\n            setProcessingStatus(status);\n          }\n        );\n        ocrText = result.text;\n      } else {\n        // Use simulated OCR for demo\n        setProcessingStatus('Simulating OCR processing...');\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        const sampleReceipts = [\n          `WALMART SUPERCENTER\n123 MAIN ST\nANYTOWN, ST 12345\n\nDate: ${new Date().toLocaleDateString()}\nTime: 14:30\n\nGROCERIES\nMILK                 $3.99\nBREAD                $2.49\nEGGS                 $4.29\n\nSUBTOTAL            $10.77\nTAX                  $0.86\nTOTAL               $11.63\n\nTHANK YOU FOR SHOPPING`,\n\n          `STARBUCKS COFFEE\n\n${new Date().toLocaleDateString()} 10:45 AM\n\nLATTE GRANDE         $5.25\nCROISSANT            $3.50\n\nTOTAL                $8.75\n\nTHANK YOU`,\n\n          `SHELL STATION #12345\n\nFUEL PURCHASE\nGRADE: REGULAR\nGALLONS: 12.456\nPRICE/GAL: $3.299\n\nFUEL TOTAL          $41.08\n\nDATE: ${new Date().toLocaleDateString()}\nTIME: 08:15`\n        ];\n\n        ocrText = sampleReceipts[Math.floor(Math.random() * sampleReceipts.length)];\n        setProcessingProgress(100);\n      }\n\n      // Parse the extracted text\n      const parsedData = parseReceiptText(ocrText);\n\n      setExtractedData(parsedData);\n      setProcessingStatus('Processing complete!');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to process receipt');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleConfirm = () => {\n    if (extractedData) {\n      onDataExtracted(extractedData);\n      onClose();\n    }\n  };\n\n  const reset = () => {\n    setFile(null);\n    setPreview(null);\n    setExtractedData(null);\n    setError(null);\n    setProgress(0);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <div>\n            <h2 className=\"text-xl font-semibold text-gray-900\">Smart Receipt Scanner</h2>\n            <p className=\"text-sm text-gray-500 mt-1\">Upload a receipt image for automatic data extraction</p>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6 space-y-6\">\n          {/* OCR Mode Toggle */}\n          <div className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-center space-x-3\">\n              <Zap className=\"h-5 w-5 text-blue-600\" />\n              <div>\n                <p className=\"text-sm font-medium text-blue-900\">\n                  {useRealOCR ? 'Real OCR Mode' : 'Demo Mode'}\n                </p>\n                <p className=\"text-xs text-blue-700\">\n                  {useRealOCR ? 'Using Tesseract.js for actual text recognition' : 'Using simulated OCR for demonstration'}\n                </p>\n              </div>\n            </div>\n            <button\n              onClick={() => setUseRealOCR(!useRealOCR)}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                useRealOCR ? 'bg-blue-600' : 'bg-gray-200'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  useRealOCR ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n\n          {!file && (\n            <div\n              className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors cursor-pointer\"\n              onDrop={handleDrop}\n              onDragOver={(e) => e.preventDefault()}\n              onClick={() => fileInputRef.current?.click()}\n            >\n              <div className=\"flex flex-col items-center\">\n                <Camera className=\"h-12 w-12 text-gray-400 mb-4\" />\n                <Upload className=\"h-8 w-8 text-gray-300 mb-4\" />\n              </div>\n              <p className=\"text-lg font-medium text-gray-900 mb-2\">\n                Drop your receipt here or click to browse\n              </p>\n              <p className=\"text-sm text-gray-500 mb-4\">\n                Supports JPEG, PNG, and WebP files up to 10MB\n              </p>\n              <div className=\"flex items-center justify-center space-x-4 text-xs text-gray-400\">\n                <span>📱 Mobile photos</span>\n                <span>•</span>\n                <span>🖼️ Scanned images</span>\n                <span>•</span>\n                <span>📄 PDF receipts</span>\n              </div>\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileInput}\n                className=\"hidden\"\n              />\n            </div>\n          )}\n\n          {file && preview && (\n            <div className=\"space-y-4\">\n              <div className=\"relative\">\n                <img\n                  src={preview}\n                  alt=\"Receipt preview\"\n                  className=\"w-full max-h-64 object-contain rounded-lg border\"\n                />\n                <button\n                  onClick={reset}\n                  className=\"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n\n              {!isProcessing && !extractedData && (\n                <button\n                  onClick={processReceipt}\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  Process Receipt\n                </button>\n              )}\n\n              {isProcessing && (\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-center space-x-2\">\n                    <Loader2 className=\"h-5 w-5 animate-spin text-blue-600\" />\n                    <span className=\"text-sm text-gray-600\">{processingStatus}</span>\n                  </div>\n                  {useRealOCR && (\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${processingProgress}%` }}\n                      />\n                    </div>\n                  )}\n                  <div className=\"text-xs text-center text-gray-500\">\n                    {useRealOCR ? 'Real OCR processing may take 10-30 seconds' : 'Demo processing...'}\n                  </div>\n                </div>\n              )}\n\n              {error && (\n                <div className=\"flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg\">\n                  <AlertCircle className=\"h-5 w-5\" />\n                  <span className=\"text-sm\">{error}</span>\n                </div>\n              )}\n\n              {extractedData && (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg\">\n                    <CheckCircle className=\"h-5 w-5\" />\n                    <span className=\"text-sm\">Receipt processed successfully!</span>\n                  </div>\n\n                  <div className=\"bg-gray-50 p-4 rounded-lg space-y-3\">\n                    <h3 className=\"font-medium text-gray-900\">Extracted Information</h3>\n                    \n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Merchant:</span>\n                        <p className=\"font-medium\">{extractedData.merchant}</p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Amount:</span>\n                        <p className=\"font-medium\">${extractedData.amount.toFixed(2)}</p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Date:</span>\n                        <p className=\"font-medium\">\n                          {extractedData.date ? extractedData.date.toLocaleDateString() : 'Not found'}\n                        </p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Category:</span>\n                        <p className=\"font-medium\">{extractedData.suggestedCategory}</p>\n                      </div>\n                    </div>\n\n                    <div className=\"text-xs text-gray-500\">\n                      <p>Data extracted from receipt image using OCR simulation</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={handleConfirm}\n                      className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors\"\n                    >\n                      Use This Data\n                    </button>\n                    <button\n                      onClick={reset}\n                      className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors\"\n                    >\n                      Try Again\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,wBAAwB;AACxB,MAAM,mBAAmB,CAAC;IACxB,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAErF,mDAAmD;IACnD,IAAI,WAAW;IACf,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,GAAI;QACpC,IAAI,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC,MAAM;YACjE,WAAW;YACX;QACF;IACF;IAEA,kCAAkC;IAClC,IAAI,SAAS;IACb,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;YACxC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,OAAO;gBACT,SAAS,WAAW,KAAK,CAAC,EAAE;gBAC5B;YACF;QACF;IACF;IAEA,gDAAgD;IAChD,IAAI,WAAW,GAAG;QAChB,KAAK,MAAM,QAAQ,MAAM,OAAO,GAAI;YAClC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,OAAO;gBACT,SAAS,WAAW,KAAK,CAAC,EAAE;gBAC5B;YACF;QACF;IACF;IAEA,eAAe;IACf,IAAI,OAAO,IAAI;IACf,KAAK,MAAM,QAAQ,MAAO;QACxB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,IAAI,WAAW;YACb,OAAO,IAAI,KAAK,SAAS,CAAC,EAAE;YAC5B;QACF;IACF;IAEA,qCAAqC;IACrC,IAAI,oBAAoB;IACxB,MAAM,gBAAgB,SAAS,WAAW;IAC1C,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,UAAU;QAC5G,oBAAoB;IACtB,OAAO,IAAI,cAAc,QAAQ,CAAC,gBAAgB,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,SAAS;QACxH,oBAAoB;IACtB,OAAO,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,UAAU,cAAc,QAAQ,CAAC,SAAS;QAC7G,oBAAoB;IACtB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,SAAS;IACX;AACF;AAOe,SAAS,cAAc,EAAE,eAAe,EAAE,OAAO,EAAsB;IACpF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;QACzC,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAa;QAE3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAAwD;QACxF;QAEA,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAAyC;QACzE;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,kBAAkB;QACrC,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,SAAS,WAAW,KAAK,IAAI;YAC7B;QACF;QAEA,QAAQ;QACR,SAAS;QACT,iBAAiB;QAEjB,iBAAiB;QACjB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,WAAW,EAAE,MAAM,EAAE;QACvB;QACA,OAAO,aAAa,CAAC;IACvB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,MAAM,cAAc,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QAC3C,IAAI,aAAa;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,eAAe,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM;QAEX,gBAAgB;QAChB,SAAS;QACT,sBAAsB;QACtB,oBAAoB;QAEpB,IAAI;YACF,IAAI;YAEJ,IAAI,YAAY;gBACd,iCAAiC;gBACjC,MAAM,SAAS,MAAM,CAAA,GAAA,iHAAA,CAAA,6BAA0B,AAAD,EAC5C,MACA,CAAC,UAAU;oBACT,sBAAsB;oBACtB,oBAAoB;gBACtB;gBAEF,UAAU,OAAO,IAAI;YACvB,OAAO;gBACL,6BAA6B;gBAC7B,oBAAoB;gBACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,MAAM,iBAAiB;oBACrB,CAAC;;;;MAIL,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;;;;;;;;;sBAYlB,CAAC;oBAEb,CAAC;;AAEX,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;;;;SAOzB,CAAC;oBAEA,CAAC;;;;;;;;;MASL,EAAE,IAAI,OAAO,kBAAkB,GAAG;WAC7B,CAAC;iBACH;gBAED,UAAU,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;gBAC3E,sBAAsB;YACxB;YAEA,2BAA2B;YAC3B,MAAM,aAAa,iBAAiB;YAEpC,iBAAiB;YACjB,oBAAoB;QACtB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe;YACjB,gBAAgB;YAChB;QACF;IACF;IAEA,MAAM,QAAQ;QACZ,QAAQ;QACR,WAAW;QACX,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAE5C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DACV,aAAa,kBAAkB;;;;;;8DAElC,8OAAC;oDAAE,WAAU;8DACV,aAAa,mDAAmD;;;;;;;;;;;;;;;;;;8CAIvE,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAW,CAAC,0EAA0E,EACpF,aAAa,gBAAgB,eAC7B;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,aAAa,kBAAkB,iBAC/B;;;;;;;;;;;;;;;;;wBAKP,CAAC,sBACA,8OAAC;4BACC,WAAU;4BACV,QAAQ;4BACR,YAAY,CAAC,IAAM,EAAE,cAAc;4BACnC,SAAS,IAAM,aAAa,OAAO,EAAE;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;;;;;;;wBAKf,QAAQ,yBACP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,KAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAIhB,CAAC,gBAAgB,CAAC,+BACjB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;gCAKF,8BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;wCAE1C,4BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;gDAAC;;;;;;;;;;;sDAI/C,8OAAC;4CAAI,WAAU;sDACZ,aAAa,+CAA+C;;;;;;;;;;;;gCAKlE,uBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;gCAI9B,+BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAE,WAAU;8EAAe,cAAc,QAAQ;;;;;;;;;;;;sEAEpD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAE,WAAU;;wEAAc;wEAAE,cAAc,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;sEAE5D,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAE,WAAU;8EACV,cAAc,IAAI,GAAG,cAAc,IAAI,CAAC,kBAAkB,KAAK;;;;;;;;;;;;sEAGpE,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAE,WAAU;8EAAe,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;8DAI/D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAE;;;;;;;;;;;;;;;;;sDAIP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/Charts.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler,\n} from 'chart.js';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n);\n\ninterface SpendingTrendData {\n  labels: string[];\n  amounts: number[];\n}\n\ninterface CategoryData {\n  category: string;\n  amount: number;\n  color: string;\n}\n\ninterface MonthlyComparisonData {\n  month: string;\n  currentYear: number;\n  previousYear: number;\n}\n\n// Spending Trend Chart\nexport function SpendingTrendChart({ data }: { data: SpendingTrendData }) {\n  const chartData = {\n    labels: data.labels,\n    datasets: [\n      {\n        label: 'Daily Spending',\n        data: data.amounts,\n        borderColor: 'rgb(59, 130, 246)',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        borderWidth: 2,\n        fill: true,\n        tension: 0.4,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Spending Trend (Last 30 Days)',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '$' + value.toFixed(0);\n          },\n        },\n      },\n      x: {\n        grid: {\n          display: false,\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Line data={chartData} options={options} />\n    </div>\n  );\n}\n\n// Category Breakdown Chart\nexport function CategoryBreakdownChart({ data }: { data: CategoryData[] }) {\n  const chartData = {\n    labels: data.map(item => item.category),\n    datasets: [\n      {\n        data: data.map(item => item.amount),\n        backgroundColor: data.map(item => item.color),\n        borderWidth: 0,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'right' as const,\n        labels: {\n          usePointStyle: true,\n          padding: 20,\n        },\n      },\n      title: {\n        display: true,\n        text: 'Spending by Category',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);\n            const percentage = ((context.parsed / total) * 100).toFixed(1);\n            return `${context.label}: $${context.parsed.toFixed(2)} (${percentage}%)`;\n          },\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Doughnut data={chartData} options={options} />\n    </div>\n  );\n}\n\n// Monthly Comparison Chart\nexport function MonthlyComparisonChart({ data }: { data: MonthlyComparisonData[] }) {\n  const chartData = {\n    labels: data.map(item => item.month),\n    datasets: [\n      {\n        label: '2024',\n        data: data.map(item => item.currentYear),\n        backgroundColor: 'rgba(59, 130, 246, 0.8)',\n        borderColor: 'rgb(59, 130, 246)',\n        borderWidth: 1,\n      },\n      {\n        label: '2023',\n        data: data.map(item => item.previousYear),\n        backgroundColor: 'rgba(156, 163, 175, 0.8)',\n        borderColor: 'rgb(156, 163, 175)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: 'Monthly Spending Comparison',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            return `${context.dataset.label}: $${context.parsed.y.toFixed(2)}`;\n          },\n        },\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '$' + value.toFixed(0);\n          },\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Bar data={chartData} options={options} />\n    </div>\n  );\n}\n\n// Budget Progress Chart\nexport function BudgetProgressChart({ \n  budgets \n}: { \n  budgets: Array<{\n    category: string;\n    budgetAmount: number;\n    spentAmount: number;\n    color: string;\n  }> \n}) {\n  const chartData = {\n    labels: budgets.map(b => b.category),\n    datasets: [\n      {\n        label: 'Budget',\n        data: budgets.map(b => b.budgetAmount),\n        backgroundColor: 'rgba(229, 231, 235, 0.8)',\n        borderColor: 'rgb(229, 231, 235)',\n        borderWidth: 1,\n      },\n      {\n        label: 'Spent',\n        data: budgets.map(b => b.spentAmount),\n        backgroundColor: budgets.map(b => b.color),\n        borderColor: budgets.map(b => b.color),\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: 'Budget vs Actual Spending',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            return `${context.dataset.label}: $${context.parsed.y.toFixed(2)}`;\n          },\n        },\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '$' + value.toFixed(0);\n          },\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Bar data={chartData} options={options} />\n    </div>\n  );\n}\n\n// Savings Goal Progress Chart\nexport function SavingsGoalChart({ \n  goals \n}: { \n  goals: Array<{\n    name: string;\n    targetAmount: number;\n    currentAmount: number;\n    color: string;\n  }> \n}) {\n  const chartData = {\n    labels: goals.map(g => g.name),\n    datasets: [\n      {\n        label: 'Progress',\n        data: goals.map(g => (g.currentAmount / g.targetAmount) * 100),\n        backgroundColor: goals.map(g => g.color),\n        borderColor: goals.map(g => g.color),\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    indexAxis: 'y' as const,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Savings Goals Progress',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            const goal = goals[context.dataIndex];\n            return `${context.parsed.x.toFixed(1)}% ($${goal.currentAmount.toFixed(2)} / $${goal.targetAmount.toFixed(2)})`;\n          },\n        },\n      },\n    },\n    scales: {\n      x: {\n        beginAtZero: true,\n        max: 100,\n        ticks: {\n          callback: function(value: any) {\n            return value + '%';\n          },\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Bar data={chartData} options={options} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAGA;AAaA;AAhBA;;;;AAkBA,+BAA+B;AAC/B,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,eAAY,EACZ,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM,EACN,4JAAA,CAAA,SAAM;AAqBD,SAAS,mBAAmB,EAAE,IAAI,EAA+B;IACtE,MAAM,YAAY;QAChB,QAAQ,KAAK,MAAM;QACnB,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,KAAK,OAAO;gBAClB,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,MAAM;gBACN,SAAS;YACX;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,MAAM,MAAM,OAAO,CAAC;oBAC7B;gBACF;YACF;YACA,GAAG;gBACD,MAAM;oBACJ,SAAS;gBACX;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,OAAI;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGtC;AAGO,SAAS,uBAAuB,EAAE,IAAI,EAA4B;IACvE,MAAM,YAAY;QAChB,QAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;QACtC,UAAU;YACR;gBACE,MAAM,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;gBAClC,iBAAiB,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;gBAC5C,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;gBACV,QAAQ;oBACN,eAAe;oBACf,SAAS;gBACX;YACF;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAW,IAAc,IAAI,GAAG;wBAC3E,MAAM,aAAa,CAAC,AAAC,QAAQ,MAAM,GAAG,QAAS,GAAG,EAAE,OAAO,CAAC;wBAC5D,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC;oBAC3E;gBACF;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,WAAQ;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAG1C;AAGO,SAAS,uBAAuB,EAAE,IAAI,EAAqC;IAChF,MAAM,YAAY;QAChB,QAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;QACnC,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,WAAW;gBACvC,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;YACA;gBACE,OAAO;gBACP,MAAM,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,YAAY;gBACxC,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI;oBACpE;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,MAAM,MAAM,OAAO,CAAC;oBAC7B;gBACF;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,MAAG;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGrC;AAGO,SAAS,oBAAoB,EAClC,OAAO,EAQR;IACC,MAAM,YAAY;QAChB,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;QACnC,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY;gBACrC,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;YACA;gBACE,OAAO;gBACP,MAAM,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;gBACpC,iBAAiB,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACzC,aAAa,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACrC,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI;oBACpE;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,MAAM,MAAM,OAAO,CAAC;oBAC7B;gBACF;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,MAAG;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGrC;AAGO,SAAS,iBAAiB,EAC/B,KAAK,EAQN;IACC,MAAM,YAAY;QAChB,QAAQ,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAC7B,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,MAAM,GAAG,CAAC,CAAA,IAAK,AAAC,EAAE,aAAa,GAAG,EAAE,YAAY,GAAI;gBAC1D,iBAAiB,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACvC,aAAa,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACnC,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,WAAW;QACX,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,MAAM,OAAO,KAAK,CAAC,QAAQ,SAAS,CAAC;wBACrC,OAAO,GAAG,QAAQ,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,KAAK,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACjH;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,KAAK;gBACL,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,QAAQ;oBACjB;gBACF;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,MAAG;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGrC", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/lib/ai-insights.ts"], "sourcesContent": ["import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, subMonths, differenceInDays } from 'date-fns';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\nexport interface SpendingInsight {\n  type: 'warning' | 'info' | 'success' | 'alert';\n  title: string;\n  description: string;\n  action?: string;\n  priority: 'low' | 'medium' | 'high';\n  category?: string;\n  amount?: number;\n}\n\nexport interface SpendingPattern {\n  pattern: string;\n  frequency: number;\n  averageAmount: number;\n  category: string;\n  confidence: number;\n}\n\nexport interface AnomalyDetection {\n  isAnomaly: boolean;\n  type: 'amount' | 'frequency' | 'category' | 'merchant';\n  description: string;\n  severity: 'low' | 'medium' | 'high';\n  expectedValue: number;\n  actualValue: number;\n}\n\n/**\n * Generate AI-powered spending insights\n */\nexport const generateSpendingInsights = (expenses: Expense[]): SpendingInsight[] => {\n  const insights: SpendingInsight[] = [];\n  \n  if (expenses.length === 0) {\n    return [{\n      type: 'info',\n      title: 'Start Tracking Your Expenses',\n      description: 'Add some expenses to get personalized insights and recommendations.',\n      priority: 'low'\n    }];\n  }\n\n  // Analyze spending patterns\n  const patterns = analyzeSpendingPatterns(expenses);\n  const anomalies = detectAnomalies(expenses);\n  const trends = analyzeTrends(expenses);\n  \n  // Generate insights based on patterns\n  patterns.forEach(pattern => {\n    if (pattern.confidence > 0.7) {\n      insights.push({\n        type: 'info',\n        title: `Regular ${pattern.category} Spending`,\n        description: `You spend an average of $${pattern.averageAmount.toFixed(2)} on ${pattern.category} ${pattern.pattern}. This accounts for ${((pattern.averageAmount * pattern.frequency) / getTotalSpending(expenses) * 100).toFixed(1)}% of your total spending.`,\n        priority: 'low',\n        category: pattern.category,\n        amount: pattern.averageAmount\n      });\n    }\n  });\n\n  // Generate anomaly alerts\n  anomalies.forEach(anomaly => {\n    if (anomaly.isAnomaly && anomaly.severity !== 'low') {\n      insights.push({\n        type: anomaly.severity === 'high' ? 'alert' : 'warning',\n        title: `Unusual ${anomaly.type} Detected`,\n        description: anomaly.description,\n        priority: anomaly.severity === 'high' ? 'high' : 'medium'\n      });\n    }\n  });\n\n  // Generate trend insights\n  if (trends.monthlyGrowth > 20) {\n    insights.push({\n      type: 'warning',\n      title: 'Spending Increase Alert',\n      description: `Your spending has increased by ${trends.monthlyGrowth.toFixed(1)}% compared to last month. Consider reviewing your budget.`,\n      priority: 'high',\n      action: 'Review recent expenses and identify areas to cut back'\n    });\n  } else if (trends.monthlyGrowth < -10) {\n    insights.push({\n      type: 'success',\n      title: 'Great Job Saving!',\n      description: `You've reduced your spending by ${Math.abs(trends.monthlyGrowth).toFixed(1)}% this month. Keep up the good work!`,\n      priority: 'low'\n    });\n  }\n\n  // Category-specific insights\n  const categoryInsights = generateCategoryInsights(expenses);\n  insights.push(...categoryInsights);\n\n  // Predictive insights\n  const predictions = generatePredictiveInsights(expenses);\n  insights.push(...predictions);\n\n  return insights.sort((a, b) => {\n    const priorityOrder = { high: 3, medium: 2, low: 1 };\n    return priorityOrder[b.priority] - priorityOrder[a.priority];\n  });\n};\n\n/**\n * Analyze spending patterns\n */\nconst analyzeSpendingPatterns = (expenses: Expense[]): SpendingPattern[] => {\n  const patterns: SpendingPattern[] = [];\n  const categoryGroups = groupByCategory(expenses);\n\n  Object.entries(categoryGroups).forEach(([category, categoryExpenses]) => {\n    // Analyze weekly patterns\n    const weeklySpending = analyzeWeeklyPattern(categoryExpenses);\n    if (weeklySpending.frequency > 0.5) {\n      patterns.push({\n        pattern: 'weekly',\n        frequency: weeklySpending.frequency,\n        averageAmount: weeklySpending.average,\n        category,\n        confidence: weeklySpending.frequency\n      });\n    }\n\n    // Analyze daily patterns\n    const dailySpending = analyzeDailyPattern(categoryExpenses);\n    if (dailySpending.frequency > 0.3) {\n      patterns.push({\n        pattern: 'daily',\n        frequency: dailySpending.frequency,\n        averageAmount: dailySpending.average,\n        category,\n        confidence: dailySpending.frequency\n      });\n    }\n  });\n\n  return patterns;\n};\n\n/**\n * Detect spending anomalies\n */\nconst detectAnomalies = (expenses: Expense[]): AnomalyDetection[] => {\n  const anomalies: AnomalyDetection[] = [];\n  \n  // Amount anomalies\n  const amounts = expenses.map(e => e.amount);\n  const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;\n  const stdDev = Math.sqrt(amounts.reduce((sum, amt) => sum + Math.pow(amt - avgAmount, 2), 0) / amounts.length);\n  \n  expenses.forEach(expense => {\n    const zScore = Math.abs((expense.amount - avgAmount) / stdDev);\n    if (zScore > 2) { // More than 2 standard deviations\n      anomalies.push({\n        isAnomaly: true,\n        type: 'amount',\n        description: `Unusually ${expense.amount > avgAmount ? 'high' : 'low'} expense of $${expense.amount.toFixed(2)} at ${expense.merchant}`,\n        severity: zScore > 3 ? 'high' : 'medium',\n        expectedValue: avgAmount,\n        actualValue: expense.amount\n      });\n    }\n  });\n\n  // Frequency anomalies\n  const categoryFrequency = analyzeFrequencyAnomalies(expenses);\n  anomalies.push(...categoryFrequency);\n\n  return anomalies;\n};\n\n/**\n * Analyze spending trends\n */\nconst analyzeTrends = (expenses: Expense[]) => {\n  const now = new Date();\n  const thisMonth = expenses.filter(e => {\n    const expenseDate = new Date(e.date);\n    return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();\n  });\n  \n  const lastMonth = expenses.filter(e => {\n    const expenseDate = new Date(e.date);\n    const lastMonthDate = subMonths(now, 1);\n    return expenseDate.getMonth() === lastMonthDate.getMonth() && expenseDate.getFullYear() === lastMonthDate.getFullYear();\n  });\n\n  const thisMonthTotal = thisMonth.reduce((sum, e) => sum + e.amount, 0);\n  const lastMonthTotal = lastMonth.reduce((sum, e) => sum + e.amount, 0);\n  \n  const monthlyGrowth = lastMonthTotal > 0 ? ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100 : 0;\n\n  return {\n    monthlyGrowth,\n    thisMonthTotal,\n    lastMonthTotal\n  };\n};\n\n/**\n * Generate category-specific insights\n */\nconst generateCategoryInsights = (expenses: Expense[]): SpendingInsight[] => {\n  const insights: SpendingInsight[] = [];\n  const categoryTotals = groupByCategory(expenses);\n  const totalSpending = getTotalSpending(expenses);\n\n  Object.entries(categoryTotals).forEach(([category, categoryExpenses]) => {\n    const categoryTotal = categoryExpenses.reduce((sum, e) => sum + e.amount, 0);\n    const percentage = (categoryTotal / totalSpending) * 100;\n\n    if (percentage > 40) {\n      insights.push({\n        type: 'warning',\n        title: `High ${category} Spending`,\n        description: `${category} accounts for ${percentage.toFixed(1)}% of your total spending ($${categoryTotal.toFixed(2)}). Consider if this aligns with your priorities.`,\n        priority: 'medium',\n        category,\n        amount: categoryTotal\n      });\n    }\n\n    // Detect frequent small purchases\n    const smallPurchases = categoryExpenses.filter(e => e.amount < 10);\n    if (smallPurchases.length > 10) {\n      const smallTotal = smallPurchases.reduce((sum, e) => sum + e.amount, 0);\n      insights.push({\n        type: 'info',\n        title: `Frequent Small ${category} Purchases`,\n        description: `You made ${smallPurchases.length} small purchases in ${category} totaling $${smallTotal.toFixed(2)}. These add up over time!`,\n        priority: 'low',\n        category,\n        amount: smallTotal\n      });\n    }\n  });\n\n  return insights;\n};\n\n/**\n * Generate predictive insights\n */\nconst generatePredictiveInsights = (expenses: Expense[]): SpendingInsight[] => {\n  const insights: SpendingInsight[] = [];\n  \n  // Predict monthly spending based on current trend\n  const now = new Date();\n  const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();\n  const daysPassed = now.getDate();\n  \n  const thisMonthExpenses = expenses.filter(e => {\n    const expenseDate = new Date(e.date);\n    return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();\n  });\n  \n  const currentMonthSpending = thisMonthExpenses.reduce((sum, e) => sum + e.amount, 0);\n  const dailyAverage = currentMonthSpending / daysPassed;\n  const projectedMonthlySpending = dailyAverage * daysInMonth;\n  \n  if (daysPassed > 7) { // Only predict after a week of data\n    insights.push({\n      type: 'info',\n      title: 'Monthly Spending Projection',\n      description: `Based on your current spending pattern, you're projected to spend $${projectedMonthlySpending.toFixed(2)} this month.`,\n      priority: 'low',\n      amount: projectedMonthlySpending\n    });\n  }\n\n  return insights;\n};\n\n// Helper functions\nconst groupByCategory = (expenses: Expense[]) => {\n  return expenses.reduce((groups, expense) => {\n    const category = expense.category;\n    if (!groups[category]) {\n      groups[category] = [];\n    }\n    groups[category].push(expense);\n    return groups;\n  }, {} as Record<string, Expense[]>);\n};\n\nconst getTotalSpending = (expenses: Expense[]) => {\n  return expenses.reduce((sum, expense) => sum + expense.amount, 0);\n};\n\nconst analyzeWeeklyPattern = (expenses: Expense[]) => {\n  // Simplified weekly pattern analysis\n  const weeklyTotals: number[] = [];\n  // Implementation would analyze weekly spending patterns\n  return { frequency: 0.5, average: 50 }; // Placeholder\n};\n\nconst analyzeDailyPattern = (expenses: Expense[]) => {\n  // Simplified daily pattern analysis\n  return { frequency: 0.3, average: 15 }; // Placeholder\n};\n\nconst analyzeFrequencyAnomalies = (expenses: Expense[]): AnomalyDetection[] => {\n  // Simplified frequency anomaly detection\n  return []; // Placeholder\n};\n"], "names": [], "mappings": ";;;AAAA;;AAwCO,MAAM,2BAA2B,CAAC;IACvC,MAAM,WAA8B,EAAE;IAEtC,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;YAAC;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;YACZ;SAAE;IACJ;IAEA,4BAA4B;IAC5B,MAAM,WAAW,wBAAwB;IACzC,MAAM,YAAY,gBAAgB;IAClC,MAAM,SAAS,cAAc;IAE7B,sCAAsC;IACtC,SAAS,OAAO,CAAC,CAAA;QACf,IAAI,QAAQ,UAAU,GAAG,KAAK;YAC5B,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,SAAS,CAAC;gBAC7C,aAAa,CAAC,yBAAyB,EAAE,QAAQ,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,oBAAoB,EAAE,CAAC,AAAC,QAAQ,aAAa,GAAG,QAAQ,SAAS,GAAI,iBAAiB,YAAY,GAAG,EAAE,OAAO,CAAC,GAAG,yBAAyB,CAAC;gBAChQ,UAAU;gBACV,UAAU,QAAQ,QAAQ;gBAC1B,QAAQ,QAAQ,aAAa;YAC/B;QACF;IACF;IAEA,0BAA0B;IAC1B,UAAU,OAAO,CAAC,CAAA;QAChB,IAAI,QAAQ,SAAS,IAAI,QAAQ,QAAQ,KAAK,OAAO;YACnD,SAAS,IAAI,CAAC;gBACZ,MAAM,QAAQ,QAAQ,KAAK,SAAS,UAAU;gBAC9C,OAAO,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC;gBACzC,aAAa,QAAQ,WAAW;gBAChC,UAAU,QAAQ,QAAQ,KAAK,SAAS,SAAS;YACnD;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,OAAO,aAAa,GAAG,IAAI;QAC7B,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,OAAO;YACP,aAAa,CAAC,+BAA+B,EAAE,OAAO,aAAa,CAAC,OAAO,CAAC,GAAG,yDAAyD,CAAC;YACzI,UAAU;YACV,QAAQ;QACV;IACF,OAAO,IAAI,OAAO,aAAa,GAAG,CAAC,IAAI;QACrC,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,OAAO;YACP,aAAa,CAAC,gCAAgC,EAAE,KAAK,GAAG,CAAC,OAAO,aAAa,EAAE,OAAO,CAAC,GAAG,oCAAoC,CAAC;YAC/H,UAAU;QACZ;IACF;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB,yBAAyB;IAClD,SAAS,IAAI,IAAI;IAEjB,sBAAsB;IACtB,MAAM,cAAc,2BAA2B;IAC/C,SAAS,IAAI,IAAI;IAEjB,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG;QACvB,MAAM,gBAAgB;YAAE,MAAM;YAAG,QAAQ;YAAG,KAAK;QAAE;QACnD,OAAO,aAAa,CAAC,EAAE,QAAQ,CAAC,GAAG,aAAa,CAAC,EAAE,QAAQ,CAAC;IAC9D;AACF;AAEA;;CAEC,GACD,MAAM,0BAA0B,CAAC;IAC/B,MAAM,WAA8B,EAAE;IACtC,MAAM,iBAAiB,gBAAgB;IAEvC,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,UAAU,iBAAiB;QAClE,0BAA0B;QAC1B,MAAM,iBAAiB,qBAAqB;QAC5C,IAAI,eAAe,SAAS,GAAG,KAAK;YAClC,SAAS,IAAI,CAAC;gBACZ,SAAS;gBACT,WAAW,eAAe,SAAS;gBACnC,eAAe,eAAe,OAAO;gBACrC;gBACA,YAAY,eAAe,SAAS;YACtC;QACF;QAEA,yBAAyB;QACzB,MAAM,gBAAgB,oBAAoB;QAC1C,IAAI,cAAc,SAAS,GAAG,KAAK;YACjC,SAAS,IAAI,CAAC;gBACZ,SAAS;gBACT,WAAW,cAAc,SAAS;gBAClC,eAAe,cAAc,OAAO;gBACpC;gBACA,YAAY,cAAc,SAAS;YACrC;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,kBAAkB,CAAC;IACvB,MAAM,YAAgC,EAAE;IAExC,mBAAmB;IACnB,MAAM,UAAU,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;IAC1C,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM;IAC7E,MAAM,SAAS,KAAK,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,WAAW,IAAI,KAAK,QAAQ,MAAM;IAE7G,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,SAAS,IAAI;QACvD,IAAI,SAAS,GAAG;YACd,UAAU,IAAI,CAAC;gBACb,WAAW;gBACX,MAAM;gBACN,aAAa,CAAC,UAAU,EAAE,QAAQ,MAAM,GAAG,YAAY,SAAS,MAAM,aAAa,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,QAAQ,QAAQ,EAAE;gBACvI,UAAU,SAAS,IAAI,SAAS;gBAChC,eAAe;gBACf,aAAa,QAAQ,MAAM;YAC7B;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,0BAA0B;IACpD,UAAU,IAAI,IAAI;IAElB,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,gBAAgB,CAAC;IACrB,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA;QAChC,MAAM,cAAc,IAAI,KAAK,EAAE,IAAI;QACnC,OAAO,YAAY,QAAQ,OAAO,IAAI,QAAQ,MAAM,YAAY,WAAW,OAAO,IAAI,WAAW;IACnG;IAEA,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA;QAChC,MAAM,cAAc,IAAI,KAAK,EAAE,IAAI;QACnC,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QACrC,OAAO,YAAY,QAAQ,OAAO,cAAc,QAAQ,MAAM,YAAY,WAAW,OAAO,cAAc,WAAW;IACvH;IAEA,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IACpE,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEpE,MAAM,gBAAgB,iBAAiB,IAAI,AAAC,CAAC,iBAAiB,cAAc,IAAI,iBAAkB,MAAM;IAExG,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA;;CAEC,GACD,MAAM,2BAA2B,CAAC;IAChC,MAAM,WAA8B,EAAE;IACtC,MAAM,iBAAiB,gBAAgB;IACvC,MAAM,gBAAgB,iBAAiB;IAEvC,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,UAAU,iBAAiB;QAClE,MAAM,gBAAgB,iBAAiB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAC1E,MAAM,aAAa,AAAC,gBAAgB,gBAAiB;QAErD,IAAI,aAAa,IAAI;YACnB,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO,CAAC,KAAK,EAAE,SAAS,SAAS,CAAC;gBAClC,aAAa,GAAG,SAAS,cAAc,EAAE,WAAW,OAAO,CAAC,GAAG,2BAA2B,EAAE,cAAc,OAAO,CAAC,GAAG,gDAAgD,CAAC;gBACtK,UAAU;gBACV;gBACA,QAAQ;YACV;QACF;QAEA,kCAAkC;QAClC,MAAM,iBAAiB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG;QAC/D,IAAI,eAAe,MAAM,GAAG,IAAI;YAC9B,MAAM,aAAa,eAAe,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YACrE,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO,CAAC,eAAe,EAAE,SAAS,UAAU,CAAC;gBAC7C,aAAa,CAAC,SAAS,EAAE,eAAe,MAAM,CAAC,oBAAoB,EAAE,SAAS,WAAW,EAAE,WAAW,OAAO,CAAC,GAAG,yBAAyB,CAAC;gBAC3I,UAAU;gBACV;gBACA,QAAQ;YACV;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,6BAA6B,CAAC;IAClC,MAAM,WAA8B,EAAE;IAEtC,kDAAkD;IAClD,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,KAAK,GAAG,GAAG,OAAO;IAC9E,MAAM,aAAa,IAAI,OAAO;IAE9B,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA;QACxC,MAAM,cAAc,IAAI,KAAK,EAAE,IAAI;QACnC,OAAO,YAAY,QAAQ,OAAO,IAAI,QAAQ,MAAM,YAAY,WAAW,OAAO,IAAI,WAAW;IACnG;IAEA,MAAM,uBAAuB,kBAAkB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAClF,MAAM,eAAe,uBAAuB;IAC5C,MAAM,2BAA2B,eAAe;IAEhD,IAAI,aAAa,GAAG;QAClB,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,OAAO;YACP,aAAa,CAAC,mEAAmE,EAAE,yBAAyB,OAAO,CAAC,GAAG,YAAY,CAAC;YACpI,UAAU;YACV,QAAQ;QACV;IACF;IAEA,OAAO;AACT;AAEA,mBAAmB;AACnB,MAAM,kBAAkB,CAAC;IACvB,OAAO,SAAS,MAAM,CAAC,CAAC,QAAQ;QAC9B,MAAM,WAAW,QAAQ,QAAQ;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACrB,MAAM,CAAC,SAAS,GAAG,EAAE;QACvB;QACA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;AACN;AAEA,MAAM,mBAAmB,CAAC;IACxB,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;AACjE;AAEA,MAAM,uBAAuB,CAAC;IAC5B,qCAAqC;IACrC,MAAM,eAAyB,EAAE;IACjC,wDAAwD;IACxD,OAAO;QAAE,WAAW;QAAK,SAAS;IAAG,GAAG,cAAc;AACxD;AAEA,MAAM,sBAAsB,CAAC;IAC3B,oCAAoC;IACpC,OAAO;QAAE,WAAW;QAAK,SAAS;IAAG,GAAG,cAAc;AACxD;AAEA,MAAM,4BAA4B,CAAC;IACjC,yCAAyC;IACzC,OAAO,EAAE,EAAE,cAAc;AAC3B", "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/ThemeComponents.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useTheme } from '@/contexts/ThemeContext';\n\ninterface ThemedCardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover3D?: boolean;\n  glow?: boolean;\n}\n\nexport function ThemedCard({ children, className = '', hover3D = false, glow = false }: ThemedCardProps) {\n  const { theme, currentTheme } = useTheme();\n  \n  const baseClasses = `\n    rounded-lg transition-all duration-300 ease-out\n    ${hover3D ? 'transform-gpu perspective-1000' : ''}\n    ${glow ? 'shadow-glow' : 'shadow-lg'}\n  `;\n  \n  const themeClasses = {\n    light: 'bg-white border border-gray-200 shadow-sm hover:shadow-md',\n    dark: 'bg-gray-800 border border-gray-700 shadow-xl hover:shadow-2xl',\n    cyber: `\n      bg-black/90 border border-green-400/50 shadow-[0_0_20px_rgba(0,255,136,0.3)]\n      hover:border-green-400 hover:shadow-[0_0_40px_rgba(0,255,136,0.6)]\n      backdrop-blur-md relative overflow-hidden\n      ${hover3D ? 'hover:scale-105 hover:rotateX-2 hover:rotateY-2' : ''}\n    `,\n    neon: `\n      bg-gray-900/90 border border-pink-500/50 shadow-[0_0_25px_rgba(255,0,110,0.4)]\n      hover:border-pink-500 hover:shadow-[0_0_45px_rgba(255,0,110,0.7)]\n      backdrop-blur-lg relative overflow-hidden\n      ${hover3D ? 'hover:scale-108 hover:rotateX-3 hover:rotateY-3' : ''}\n    `,\n    matrix: `\n      bg-black/90 border border-green-500/50 shadow-[0_0_20px_rgba(0,255,0,0.4)]\n      hover:border-green-500 hover:shadow-[0_0_40px_rgba(0,255,0,0.8)]\n      backdrop-blur-sm relative overflow-hidden\n      ${hover3D ? 'hover:scale-105 hover:rotateX-2 hover:rotateY-2' : ''}\n    `,\n  };\n\n  return (\n    <div className={`${baseClasses} ${themeClasses[currentTheme]} ${className}`}>\n      {(currentTheme === 'cyber' || currentTheme === 'neon' || currentTheme === 'matrix') && (\n        <div className=\"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white/5 pointer-events-none\" />\n      )}\n      {children}\n    </div>\n  );\n}\n\ninterface ThemedButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  disabled?: boolean;\n  cyber3D?: boolean;\n}\n\nexport function ThemedButton({ \n  children, \n  onClick, \n  variant = 'primary', \n  size = 'md', \n  className = '',\n  disabled = false,\n  cyber3D = false\n}: ThemedButtonProps) {\n  const { theme, currentTheme } = useTheme();\n  \n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n  };\n  \n  const baseClasses = `\n    rounded-lg font-medium transition-all duration-300 ease-out\n    transform-gpu active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed\n    ${cyber3D ? 'perspective-1000' : ''}\n    ${sizeClasses[size]}\n  `;\n  \n  const getVariantClasses = () => {\n    const variants = {\n      light: {\n        primary: 'bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg',\n        secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 shadow-sm hover:shadow-md',\n        success: 'bg-green-600 text-white hover:bg-green-700 shadow-md hover:shadow-lg',\n        warning: 'bg-yellow-600 text-white hover:bg-yellow-700 shadow-md hover:shadow-lg',\n        error: 'bg-red-600 text-white hover:bg-red-700 shadow-md hover:shadow-lg',\n      },\n      dark: {\n        primary: 'bg-blue-600 text-white hover:bg-blue-500 shadow-lg hover:shadow-xl',\n        secondary: 'bg-gray-700 text-gray-100 hover:bg-gray-600 shadow-md hover:shadow-lg',\n        success: 'bg-green-600 text-white hover:bg-green-500 shadow-lg hover:shadow-xl',\n        warning: 'bg-yellow-600 text-white hover:bg-yellow-500 shadow-lg hover:shadow-xl',\n        error: 'bg-red-600 text-white hover:bg-red-500 shadow-lg hover:shadow-xl',\n      },\n      cyber: {\n        primary: `\n          bg-black border-2 border-green-400 text-green-400 \n          hover:bg-green-400/10 hover:text-green-300 hover:border-green-300\n          shadow-[0_0_20px_rgba(0,255,136,0.3)] hover:shadow-[0_0_40px_rgba(0,255,136,0.6)]\n          ${cyber3D ? 'hover:scale-110 hover:rotateX-5 hover:rotateY-5' : 'hover:scale-105'}\n          relative overflow-hidden\n        `,\n        secondary: `\n          bg-black border-2 border-cyan-400 text-cyan-400 \n          hover:bg-cyan-400/10 hover:text-cyan-300 hover:border-cyan-300\n          shadow-[0_0_20px_rgba(0,204,255,0.3)] hover:shadow-[0_0_40px_rgba(0,204,255,0.6)]\n          ${cyber3D ? 'hover:scale-110 hover:rotateX-5 hover:rotateY-5' : 'hover:scale-105'}\n        `,\n        success: `\n          bg-black border-2 border-green-500 text-green-500 \n          hover:bg-green-500/10 hover:text-green-400 hover:border-green-400\n          shadow-[0_0_20px_rgba(0,255,0,0.3)] hover:shadow-[0_0_40px_rgba(0,255,0,0.6)]\n          ${cyber3D ? 'hover:scale-110 hover:rotateX-5 hover:rotateY-5' : 'hover:scale-105'}\n        `,\n        warning: `\n          bg-black border-2 border-yellow-400 text-yellow-400 \n          hover:bg-yellow-400/10 hover:text-yellow-300 hover:border-yellow-300\n          shadow-[0_0_20px_rgba(255,170,0,0.3)] hover:shadow-[0_0_40px_rgba(255,170,0,0.6)]\n          ${cyber3D ? 'hover:scale-110 hover:rotateX-5 hover:rotateY-5' : 'hover:scale-105'}\n        `,\n        error: `\n          bg-black border-2 border-red-400 text-red-400 \n          hover:bg-red-400/10 hover:text-red-300 hover:border-red-300\n          shadow-[0_0_20px_rgba(255,0,85,0.3)] hover:shadow-[0_0_40px_rgba(255,0,85,0.6)]\n          ${cyber3D ? 'hover:scale-110 hover:rotateX-5 hover:rotateY-5' : 'hover:scale-105'}\n        `,\n      },\n      neon: {\n        primary: `\n          bg-gray-900 border-2 border-pink-500 text-pink-500 \n          hover:bg-pink-500/10 hover:text-pink-400 hover:border-pink-400\n          shadow-[0_0_25px_rgba(255,0,110,0.4)] hover:shadow-[0_0_45px_rgba(255,0,110,0.7)]\n          ${cyber3D ? 'hover:scale-108 hover:rotateX-4 hover:rotateY-4' : 'hover:scale-105'}\n        `,\n        secondary: `\n          bg-gray-900 border-2 border-purple-500 text-purple-500 \n          hover:bg-purple-500/10 hover:text-purple-400 hover:border-purple-400\n          shadow-[0_0_25px_rgba(131,56,236,0.4)] hover:shadow-[0_0_45px_rgba(131,56,236,0.7)]\n          ${cyber3D ? 'hover:scale-108 hover:rotateX-4 hover:rotateY-4' : 'hover:scale-105'}\n        `,\n        success: `\n          bg-gray-900 border-2 border-green-400 text-green-400 \n          hover:bg-green-400/10 hover:text-green-300 hover:border-green-300\n          shadow-[0_0_25px_rgba(6,255,165,0.4)] hover:shadow-[0_0_45px_rgba(6,255,165,0.7)]\n          ${cyber3D ? 'hover:scale-108 hover:rotateX-4 hover:rotateY-4' : 'hover:scale-105'}\n        `,\n        warning: `\n          bg-gray-900 border-2 border-yellow-400 text-yellow-400 \n          hover:bg-yellow-400/10 hover:text-yellow-300 hover:border-yellow-300\n          shadow-[0_0_25px_rgba(255,190,11,0.4)] hover:shadow-[0_0_45px_rgba(255,190,11,0.7)]\n          ${cyber3D ? 'hover:scale-108 hover:rotateX-4 hover:rotateY-4' : 'hover:scale-105'}\n        `,\n        error: `\n          bg-gray-900 border-2 border-red-500 text-red-500 \n          hover:bg-red-500/10 hover:text-red-400 hover:border-red-400\n          shadow-[0_0_25px_rgba(251,86,7,0.4)] hover:shadow-[0_0_45px_rgba(251,86,7,0.7)]\n          ${cyber3D ? 'hover:scale-108 hover:rotateX-4 hover:rotateY-4' : 'hover:scale-105'}\n        `,\n      },\n      matrix: {\n        primary: `\n          bg-black border-2 border-green-500 text-green-500 \n          hover:bg-green-500/10 hover:text-green-400 hover:border-green-400\n          shadow-[0_0_20px_rgba(0,255,0,0.4)] hover:shadow-[0_0_40px_rgba(0,255,0,0.8)]\n          ${cyber3D ? 'hover:scale-105 hover:rotateX-3 hover:rotateY-3' : 'hover:scale-105'}\n        `,\n        secondary: `\n          bg-black border-2 border-green-600 text-green-600 \n          hover:bg-green-600/10 hover:text-green-500 hover:border-green-500\n          shadow-[0_0_20px_rgba(0,204,0,0.4)] hover:shadow-[0_0_40px_rgba(0,204,0,0.8)]\n          ${cyber3D ? 'hover:scale-105 hover:rotateX-3 hover:rotateY-3' : 'hover:scale-105'}\n        `,\n        success: `\n          bg-black border-2 border-green-400 text-green-400 \n          hover:bg-green-400/10 hover:text-green-300 hover:border-green-300\n          shadow-[0_0_20px_rgba(0,255,0,0.4)] hover:shadow-[0_0_40px_rgba(0,255,0,1)]\n          ${cyber3D ? 'hover:scale-105 hover:rotateX-3 hover:rotateY-3' : 'hover:scale-105'}\n        `,\n        warning: `\n          bg-black border-2 border-yellow-500 text-yellow-500 \n          hover:bg-yellow-500/10 hover:text-yellow-400 hover:border-yellow-400\n          shadow-[0_0_20px_rgba(255,255,0,0.4)] hover:shadow-[0_0_40px_rgba(255,255,0,0.8)]\n          ${cyber3D ? 'hover:scale-105 hover:rotateX-3 hover:rotateY-3' : 'hover:scale-105'}\n        `,\n        error: `\n          bg-black border-2 border-red-500 text-red-500 \n          hover:bg-red-500/10 hover:text-red-400 hover:border-red-400\n          shadow-[0_0_20px_rgba(255,0,0,0.4)] hover:shadow-[0_0_40px_rgba(255,0,0,0.8)]\n          ${cyber3D ? 'hover:scale-105 hover:rotateX-3 hover:rotateY-3' : 'hover:scale-105'}\n        `,\n      },\n    };\n    \n    return variants[currentTheme][variant];\n  };\n\n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled}\n      className={`${baseClasses} ${getVariantClasses()} ${className}`}\n    >\n      {(currentTheme === 'cyber' || currentTheme === 'neon') && cyber3D && (\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300\" />\n      )}\n      <span className=\"relative z-10\">{children}</span>\n    </button>\n  );\n}\n\ninterface ThemedInputProps {\n  type?: string;\n  placeholder?: string;\n  value?: string;\n  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  className?: string;\n  disabled?: boolean;\n}\n\nexport function ThemedInput({ \n  type = 'text', \n  placeholder, \n  value, \n  onChange, \n  className = '',\n  disabled = false \n}: ThemedInputProps) {\n  const { currentTheme } = useTheme();\n  \n  const baseClasses = `\n    w-full px-4 py-2 rounded-lg transition-all duration-300 ease-out\n    focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed\n  `;\n  \n  const themeClasses = {\n    light: 'bg-white border border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500/20',\n    dark: 'bg-gray-800 border border-gray-600 text-gray-100 focus:border-blue-400 focus:ring-blue-400/20',\n    cyber: `\n      bg-black/80 border-2 border-green-400/50 text-green-400 placeholder-green-400/50\n      focus:border-green-400 focus:ring-green-400/30 focus:shadow-[0_0_20px_rgba(0,255,136,0.3)]\n      backdrop-blur-sm\n    `,\n    neon: `\n      bg-gray-900/80 border-2 border-pink-500/50 text-pink-500 placeholder-pink-500/50\n      focus:border-pink-500 focus:ring-pink-500/30 focus:shadow-[0_0_25px_rgba(255,0,110,0.4)]\n      backdrop-blur-lg\n    `,\n    matrix: `\n      bg-black/80 border-2 border-green-500/50 text-green-500 placeholder-green-500/50\n      focus:border-green-500 focus:ring-green-500/30 focus:shadow-[0_0_20px_rgba(0,255,0,0.4)]\n      backdrop-blur-sm\n    `,\n  };\n\n  return (\n    <input\n      type={type}\n      placeholder={placeholder}\n      value={value}\n      onChange={onChange}\n      disabled={disabled}\n      className={`${baseClasses} ${themeClasses[currentTheme]} ${className}`}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AAHA;;;AAYO,SAAS,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,UAAU,KAAK,EAAE,OAAO,KAAK,EAAmB;IACrG,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEvC,MAAM,cAAc,CAAC;;IAEnB,EAAE,UAAU,mCAAmC,GAAG;IAClD,EAAE,OAAO,gBAAgB,YAAY;EACvC,CAAC;IAED,MAAM,eAAe;QACnB,OAAO;QACP,MAAM;QACN,OAAO,CAAC;;;;MAIN,EAAE,UAAU,oDAAoD,GAAG;IACrE,CAAC;QACD,MAAM,CAAC;;;;MAIL,EAAE,UAAU,oDAAoD,GAAG;IACrE,CAAC;QACD,QAAQ,CAAC;;;;MAIP,EAAE,UAAU,oDAAoD,GAAG;IACrE,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,YAAY,CAAC,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW;;YACxE,CAAC,iBAAiB,WAAW,iBAAiB,UAAU,iBAAiB,QAAQ,mBAChF,8OAAC;gBAAI,WAAU;;;;;;YAEhB;;;;;;;AAGP;AAYO,SAAS,aAAa,EAC3B,QAAQ,EACR,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACG;IAClB,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEvC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;;;IAGnB,EAAE,UAAU,qBAAqB,GAAG;IACpC,EAAE,WAAW,CAAC,KAAK,CAAC;EACtB,CAAC;IAED,MAAM,oBAAoB;QACxB,MAAM,WAAW;YACf,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;YACT;YACA,OAAO;gBACL,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;;QAEpF,CAAC;gBACD,WAAW,CAAC;;;;UAIV,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,OAAO,CAAC;;;;UAIN,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;YACH;YACA,MAAM;gBACJ,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,WAAW,CAAC;;;;UAIV,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,OAAO,CAAC;;;;UAIN,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;YACH;YACA,QAAQ;gBACN,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,WAAW,CAAC;;;;UAIV,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,SAAS,CAAC;;;;UAIR,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;gBACD,OAAO,CAAC;;;;UAIN,EAAE,UAAU,oDAAoD,kBAAkB;QACpF,CAAC;YACH;QACF;QAEA,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ;IACxC;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW,GAAG,YAAY,CAAC,EAAE,oBAAoB,CAAC,EAAE,WAAW;;YAE9D,CAAC,iBAAiB,WAAW,iBAAiB,MAAM,KAAK,yBACxD,8OAAC;gBAAI,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAiB;;;;;;;;;;;;AAGvC;AAWO,SAAS,YAAY,EAC1B,OAAO,MAAM,EACb,WAAW,EACX,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACd,WAAW,KAAK,EACC;IACjB,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEhC,MAAM,cAAc,CAAC;;;EAGrB,CAAC;IAED,MAAM,eAAe;QACnB,OAAO;QACP,MAAM;QACN,OAAO,CAAC;;;;IAIR,CAAC;QACD,MAAM,CAAC;;;;IAIP,CAAC;QACD,QAAQ,CAAC;;;;IAIT,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;QACV,WAAW,GAAG,YAAY,CAAC,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW;;;;;;AAG5E", "debugId": null}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/InsightsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { Brain, TrendingUp, AlertTriangle, CheckCircle, Info, Lightbulb, Zap } from 'lucide-react';\nimport { generateSpendingInsights, type SpendingInsight } from '@/lib/ai-insights';\nimport { ThemedCard } from './ThemeComponents';\nimport { useTheme } from '@/contexts/ThemeContext';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\ninterface InsightsPanelProps {\n  expenses: Expense[];\n}\n\nexport default function InsightsPanel({ expenses }: InsightsPanelProps) {\n  const { currentTheme, theme } = useTheme();\n\n  const insights = useMemo(() => {\n    return generateSpendingInsights(expenses);\n  }, [expenses]);\n\n  const getInsightIcon = (type: SpendingInsight['type']) => {\n    switch (type) {\n      case 'alert':\n        return <AlertTriangle className=\"h-5 w-5 text-red-500\" />;\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />;\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />;\n      default:\n        return <Lightbulb className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getInsightBorderColor = (type: SpendingInsight['type']) => {\n    switch (type) {\n      case 'alert':\n        return 'border-l-red-500 bg-red-50';\n      case 'warning':\n        return 'border-l-yellow-500 bg-yellow-50';\n      case 'success':\n        return 'border-l-green-500 bg-green-50';\n      case 'info':\n        return 'border-l-blue-500 bg-blue-50';\n      default:\n        return 'border-l-gray-500 bg-gray-50';\n    }\n  };\n\n  const getPriorityBadge = (priority: SpendingInsight['priority']) => {\n    const colors = {\n      high: 'bg-red-100 text-red-800',\n      medium: 'bg-yellow-100 text-yellow-800',\n      low: 'bg-blue-100 text-blue-800'\n    };\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[priority]}`}>\n        {priority.charAt(0).toUpperCase() + priority.slice(1)}\n      </span>\n    );\n  };\n\n  const highPriorityInsights = insights.filter(insight => insight.priority === 'high');\n  const otherInsights = insights.filter(insight => insight.priority !== 'high');\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-3\">\n        <div\n          className=\"p-2 rounded-lg\"\n          style={{\n            backgroundColor: `${theme.colors.accent.secondary}20`,\n            color: theme.colors.accent.secondary\n          }}\n        >\n          <Brain className=\"h-6 w-6\" />\n        </div>\n        <div>\n          <h2\n            className={`text-xl font-semibold ${\n              currentTheme === 'cyber' ? 'text-cyber' :\n              currentTheme === 'neon' ? 'text-neon' :\n              currentTheme === 'matrix' ? 'text-matrix' : ''\n            }`}\n            style={{ color: theme.colors.text.primary }}\n          >\n            AI Insights\n          </h2>\n          <p\n            className=\"text-sm\"\n            style={{ color: theme.colors.text.secondary }}\n          >\n            Personalized spending analysis and recommendations\n          </p>\n        </div>\n      </div>\n\n      {/* High Priority Alerts */}\n      {highPriorityInsights.length > 0 && (\n        <ThemedCard\n          className=\"border-2\"\n          style={{ borderColor: theme.colors.accent.error }}\n          hover3D={currentTheme === 'cyber'}\n        >\n          <div\n            className=\"px-4 py-3 border-b\"\n            style={{\n              borderColor: theme.colors.accent.error,\n              backgroundColor: `${theme.colors.accent.error}20`\n            }}\n          >\n            <div className=\"flex items-center space-x-2\">\n              <Zap\n                className=\"h-4 w-4\"\n                style={{ color: theme.colors.accent.error }}\n              />\n              <h3\n                className=\"text-sm font-medium\"\n                style={{ color: theme.colors.accent.error }}\n              >\n                Urgent Attention Required\n              </h3>\n            </div>\n          </div>\n          <div className=\"p-4 space-y-3\">\n            {highPriorityInsights.map((insight, index) => (\n              <InsightCard key={index} insight={insight} />\n            ))}\n          </div>\n        </ThemedCard>\n      )}\n\n      {/* All Insights */}\n      <ThemedCard hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n        <div\n          className=\"px-6 py-4 border-b\"\n          style={{ borderColor: theme.colors.border.primary }}\n        >\n          <div className=\"flex items-center justify-between\">\n            <h3\n              className=\"text-lg font-medium\"\n              style={{ color: theme.colors.text.primary }}\n            >\n              Smart Recommendations\n            </h3>\n            <span\n              className=\"text-sm\"\n              style={{ color: theme.colors.text.secondary }}\n            >\n              {insights.length} insights\n            </span>\n          </div>\n        </div>\n        <div\n          className=\"divide-y\"\n          style={{ borderColor: theme.colors.border.primary }}\n        >\n          {insights.length === 0 ? (\n            <div className=\"p-8 text-center\">\n              <Brain\n                className=\"h-12 w-12 mx-auto mb-4\"\n                style={{ color: theme.colors.text.tertiary }}\n              />\n              <p style={{ color: theme.colors.text.secondary }}>\n                Add more expenses to get AI-powered insights\n              </p>\n            </div>\n          ) : (\n            insights.map((insight, index) => (\n              <div key={index} className=\"p-6\">\n                <InsightCard insight={insight} />\n              </div>\n            ))\n          )}\n        </div>\n      </ThemedCard>\n\n      {/* Insights Summary */}\n      {insights.length > 0 && (\n        <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6\">\n          <div className=\"flex items-start space-x-4\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <TrendingUp className=\"h-5 w-5 text-purple-600\" />\n            </div>\n            <div className=\"flex-1\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Insights Summary</h4>\n              <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-500\">High Priority:</span>\n                  <span className=\"ml-2 font-medium text-red-600\">\n                    {insights.filter(i => i.priority === 'high').length}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-500\">Opportunities:</span>\n                  <span className=\"ml-2 font-medium text-green-600\">\n                    {insights.filter(i => i.type === 'success').length}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-500\">Recommendations:</span>\n                  <span className=\"ml-2 font-medium text-blue-600\">\n                    {insights.filter(i => i.action).length}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\ninterface InsightCardProps {\n  insight: SpendingInsight;\n}\n\nfunction InsightCard({ insight }: InsightCardProps) {\n  const getInsightIcon = (type: SpendingInsight['type']) => {\n    switch (type) {\n      case 'alert':\n        return <AlertTriangle className=\"h-5 w-5 text-red-500\" />;\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />;\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />;\n      default:\n        return <Lightbulb className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getInsightBorderColor = (type: SpendingInsight['type']) => {\n    switch (type) {\n      case 'alert':\n        return 'border-l-red-500 bg-red-50';\n      case 'warning':\n        return 'border-l-yellow-500 bg-yellow-50';\n      case 'success':\n        return 'border-l-green-500 bg-green-50';\n      case 'info':\n        return 'border-l-blue-500 bg-blue-50';\n      default:\n        return 'border-l-gray-500 bg-gray-50';\n    }\n  };\n\n  const getPriorityBadge = (priority: SpendingInsight['priority']) => {\n    const colors = {\n      high: 'bg-red-100 text-red-800',\n      medium: 'bg-yellow-100 text-yellow-800',\n      low: 'bg-blue-100 text-blue-800'\n    };\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[priority]}`}>\n        {priority.charAt(0).toUpperCase() + priority.slice(1)}\n      </span>\n    );\n  };\n\n  return (\n    <div className={`border-l-4 p-4 rounded-r-lg ${getInsightBorderColor(insight.type)}`}>\n      <div className=\"flex items-start space-x-3\">\n        <div className=\"flex-shrink-0 mt-0.5\">\n          {getInsightIcon(insight.type)}\n        </div>\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center justify-between mb-1\">\n            <h4 className=\"text-sm font-medium text-gray-900\">{insight.title}</h4>\n            {getPriorityBadge(insight.priority)}\n          </div>\n          <p className=\"text-sm text-gray-700 mb-2\">{insight.description}</p>\n          \n          {insight.action && (\n            <div className=\"mt-3 p-3 bg-white rounded-md border border-gray-200\">\n              <div className=\"flex items-start space-x-2\">\n                <Lightbulb className=\"h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0\" />\n                <p className=\"text-xs text-gray-600\">{insight.action}</p>\n              </div>\n            </div>\n          )}\n          \n          {(insight.category || insight.amount) && (\n            <div className=\"mt-2 flex items-center space-x-4 text-xs text-gray-500\">\n              {insight.category && (\n                <span className=\"inline-flex items-center\">\n                  Category: <span className=\"ml-1 font-medium\">{insight.category}</span>\n                </span>\n              )}\n              {insight.amount && (\n                <span className=\"inline-flex items-center\">\n                  Amount: <span className=\"ml-1 font-medium\">${insight.amount.toFixed(2)}</span>\n                </span>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAoBe,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEvC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,CAAA,GAAA,4HAAA,CAAA,2BAAwB,AAAD,EAAE;IAClC,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,4MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,MAAM,CAAC,SAAS,EAAE;sBAC3G,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;;;;;;IAGzD;IAEA,MAAM,uBAAuB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC7E,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEtE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;4BACrD,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS;wBACtC;kCAEA,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,8OAAC;;0CACC,8OAAC;gCACC,WAAW,CAAC,sBAAsB,EAChC,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;gCACF,OAAO;oCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gCAAC;0CAC3C;;;;;;0CAGD,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;gCAAC;0CAC7C;;;;;;;;;;;;;;;;;;YAOJ,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC,qIAAA,CAAA,aAAU;gBACT,WAAU;gBACV,OAAO;oBAAE,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK;gBAAC;gBAChD,SAAS,iBAAiB;;kCAE1B,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK;4BACtC,iBAAiB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;wBACnD;kCAEA,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCACF,WAAU;oCACV,OAAO;wCAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK;oCAAC;;;;;;8CAE5C,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK;oCAAC;8CAC3C;;;;;;;;;;;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;kCACZ,qBAAqB,GAAG,CAAC,CAAC,SAAS,sBAClC,8OAAC;gCAAwB,SAAS;+BAAhB;;;;;;;;;;;;;;;;0BAO1B,8OAAC,qIAAA,CAAA,aAAU;gBAAC,SAAS,iBAAiB;gBAAS,MAAM,iBAAiB;;kCACpE,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wBAAC;kCAElD,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;oCAAC;8CAC3C;;;;;;8CAGD,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;oCAAC;;wCAE3C,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAIvB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wBAAC;kCAEjD,SAAS,MAAM,KAAK,kBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,OAAO;wCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ;oCAAC;;;;;;8CAE7C,8OAAC;oCAAE,OAAO;wCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;oCAAC;8CAAG;;;;;;;;;;;mCAKpD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACrB,8OAAC;gCAAgB,WAAU;0CACzB,cAAA,8OAAC;oCAAY,SAAS;;;;;;+BADd;;;;;;;;;;;;;;;;YASjB,SAAS,MAAM,GAAG,mBACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAExB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;;;;;;;;;;;;sDAGvD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;;;;;;;;;;;;sDAGtD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D;AAMA,SAAS,YAAY,EAAE,OAAO,EAAoB;IAChD,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,4MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,MAAM,CAAC,SAAS,EAAE;sBAC3G,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;;;;;;IAGzD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,sBAAsB,QAAQ,IAAI,GAAG;kBAClF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,eAAe,QAAQ,IAAI;;;;;;8BAE9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC,QAAQ,KAAK;;;;;;gCAC/D,iBAAiB,QAAQ,QAAQ;;;;;;;sCAEpC,8OAAC;4BAAE,WAAU;sCAA8B,QAAQ,WAAW;;;;;;wBAE7D,QAAQ,MAAM,kBACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAE,WAAU;kDAAyB,QAAQ,MAAM;;;;;;;;;;;;;;;;;wBAKzD,CAAC,QAAQ,QAAQ,IAAI,QAAQ,MAAM,mBAClC,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,QAAQ,kBACf,8OAAC;oCAAK,WAAU;;wCAA2B;sDAC/B,8OAAC;4CAAK,WAAU;sDAAoB,QAAQ,QAAQ;;;;;;;;;;;;gCAGjE,QAAQ,MAAM,kBACb,8OAAC;oCAAK,WAAU;;wCAA2B;sDACjC,8OAAC;4CAAK,WAAU;;gDAAmB;gDAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStF", "debugId": null}}, {"offset": {"line": 2804, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { TrendingUp, TrendingDown, DollarSign, ShoppingCart, Calendar, Target } from 'lucide-react';\nimport { SpendingTrendChart, CategoryBreakdownChart, MonthlyComparisonChart } from './Charts';\nimport InsightsPanel from './InsightsPanel';\nimport { ThemedCard } from './ThemeComponents';\nimport { useTheme } from '@/contexts/ThemeContext';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\ninterface DashboardProps {\n  expenses: Expense[];\n}\n\nexport default function Dashboard({ expenses }: DashboardProps) {\n  const { currentTheme, theme } = useTheme();\n\n  // Calculate analytics\n  const analytics = useMemo(() => {\n    const now = new Date();\n    const thisMonth = now.getMonth();\n    const thisYear = now.getFullYear();\n    const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;\n    const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;\n\n    // This month expenses\n    const thisMonthExpenses = expenses.filter(expense => {\n      const expenseDate = new Date(expense.date);\n      return expenseDate.getMonth() === thisMonth && expenseDate.getFullYear() === thisYear;\n    });\n\n    // Last month expenses\n    const lastMonthExpenses = expenses.filter(expense => {\n      const expenseDate = new Date(expense.date);\n      return expenseDate.getMonth() === lastMonth && expenseDate.getFullYear() === lastMonthYear;\n    });\n\n    // Today's expenses\n    const today = now.toDateString();\n    const todayExpenses = expenses.filter(expense => \n      new Date(expense.date).toDateString() === today\n    );\n\n    // This week expenses\n    const weekStart = new Date(now);\n    weekStart.setDate(now.getDate() - now.getDay());\n    const thisWeekExpenses = expenses.filter(expense => {\n      const expenseDate = new Date(expense.date);\n      return expenseDate >= weekStart;\n    });\n\n    // Category breakdown\n    const categoryTotals = expenses.reduce((acc, expense) => {\n      acc[expense.category] = (acc[expense.category] || 0) + expense.amount;\n      return acc;\n    }, {} as Record<string, number>);\n\n    // Spending trend (last 30 days)\n    const last30Days = Array.from({ length: 30 }, (_, i) => {\n      const date = new Date();\n      date.setDate(date.getDate() - (29 - i));\n      return date.toDateString();\n    });\n\n    const dailySpending = last30Days.map(dateStr => {\n      const dayExpenses = expenses.filter(expense => \n        new Date(expense.date).toDateString() === dateStr\n      );\n      return dayExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    });\n\n    // Calculate totals\n    const thisMonthTotal = thisMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    const lastMonthTotal = lastMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    const todayTotal = todayExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    const thisWeekTotal = thisWeekExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);\n\n    // Calculate changes\n    const monthlyChange = lastMonthTotal > 0 ? ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100 : 0;\n    const avgDailySpending = thisMonthExpenses.length > 0 ? thisMonthTotal / new Date().getDate() : 0;\n\n    return {\n      thisMonthTotal,\n      lastMonthTotal,\n      todayTotal,\n      thisWeekTotal,\n      totalExpenses,\n      monthlyChange,\n      avgDailySpending,\n      categoryTotals,\n      dailySpending,\n      last30Days,\n      expenseCount: expenses.length,\n      avgExpenseAmount: expenses.length > 0 ? totalExpenses / expenses.length : 0\n    };\n  }, [expenses]);\n\n  // Prepare chart data\n  const spendingTrendData = {\n    labels: analytics.last30Days.map(date => {\n      const d = new Date(date);\n      return `${d.getMonth() + 1}/${d.getDate()}`;\n    }),\n    amounts: analytics.dailySpending\n  };\n\n  const categoryData = Object.entries(analytics.categoryTotals).map(([category, amount]) => ({\n    category,\n    amount,\n    color: getCategoryColor(category)\n  }));\n\n  const monthlyComparisonData = [\n    {\n      month: 'Last Month',\n      currentYear: analytics.lastMonthTotal,\n      previousYear: 0 // Could calculate year-over-year if we had more data\n    },\n    {\n      month: 'This Month',\n      currentYear: analytics.thisMonthTotal,\n      previousYear: 0\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <MetricCard\n          title=\"This Month\"\n          value={`$${analytics.thisMonthTotal.toFixed(2)}`}\n          change={analytics.monthlyChange}\n          icon={<DollarSign className=\"h-6 w-6\" />}\n          color=\"blue\"\n        />\n        <MetricCard\n          title=\"This Week\"\n          value={`$${analytics.thisWeekTotal.toFixed(2)}`}\n          subtitle={`${Math.ceil(analytics.thisWeekTotal / 7)} avg/day`}\n          icon={<Calendar className=\"h-6 w-6\" />}\n          color=\"green\"\n        />\n        <MetricCard\n          title=\"Today\"\n          value={`$${analytics.todayTotal.toFixed(2)}`}\n          subtitle={`vs $${analytics.avgDailySpending.toFixed(2)} avg`}\n          icon={<ShoppingCart className=\"h-6 w-6\" />}\n          color=\"purple\"\n        />\n        <MetricCard\n          title=\"Total Expenses\"\n          value={analytics.expenseCount.toString()}\n          subtitle={`$${analytics.avgExpenseAmount.toFixed(2)} avg`}\n          icon={<Target className=\"h-6 w-6\" />}\n          color=\"orange\"\n        />\n      </div>\n\n      {/* AI Insights */}\n      <InsightsPanel expenses={expenses} />\n\n      {/* Charts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <ThemedCard className=\"p-6\" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n          <h3\n            className=\"text-lg font-medium mb-4\"\n            style={{ color: theme.colors.text.primary }}\n          >\n            Spending Trend (30 Days)\n          </h3>\n          <SpendingTrendChart data={spendingTrendData} />\n        </ThemedCard>\n\n        <ThemedCard className=\"p-6\" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n          <h3\n            className=\"text-lg font-medium mb-4\"\n            style={{ color: theme.colors.text.primary }}\n          >\n            Category Breakdown\n          </h3>\n          {categoryData.length > 0 ? (\n            <CategoryBreakdownChart data={categoryData} />\n          ) : (\n            <div\n              className=\"h-64 flex items-center justify-center\"\n              style={{ color: theme.colors.text.secondary }}\n            >\n              No expense data available\n            </div>\n          )}\n        </ThemedCard>\n      </div>\n\n      {/* Monthly Comparison */}\n      <ThemedCard className=\"p-6\" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n        <h3\n          className=\"text-lg font-medium mb-4\"\n          style={{ color: theme.colors.text.primary }}\n        >\n          Monthly Comparison\n        </h3>\n        <MonthlyComparisonChart data={monthlyComparisonData} />\n      </ThemedCard>\n\n      {/* Top Categories */}\n      <ThemedCard className=\"p-6\" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n        <h3\n          className=\"text-lg font-medium mb-4\"\n          style={{ color: theme.colors.text.primary }}\n        >\n          Top Spending Categories\n        </h3>\n        <div className=\"space-y-4\">\n          {Object.entries(analytics.categoryTotals)\n            .sort(([,a], [,b]) => b - a)\n            .slice(0, 5)\n            .map(([category, amount]) => {\n              const percentage = (amount / analytics.totalExpenses) * 100;\n              return (\n                <div key={category} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div\n                      className=\"w-4 h-4 rounded-full\"\n                      style={{ backgroundColor: getCategoryColor(category) }}\n                    />\n                    <span\n                      className=\"text-sm font-medium\"\n                      style={{ color: theme.colors.text.primary }}\n                    >\n                      {category}\n                    </span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div\n                      className=\"text-sm font-medium\"\n                      style={{ color: theme.colors.text.primary }}\n                    >\n                      ${amount.toFixed(2)}\n                    </div>\n                    <div\n                      className=\"text-xs\"\n                      style={{ color: theme.colors.text.secondary }}\n                    >\n                      {percentage.toFixed(1)}%\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n        </div>\n      </ThemedCard>\n    </div>\n  );\n}\n\ninterface MetricCardProps {\n  title: string;\n  value: string;\n  change?: number;\n  subtitle?: string;\n  icon: React.ReactNode;\n  color: 'blue' | 'green' | 'purple' | 'orange';\n}\n\nfunction MetricCard({ title, value, change, subtitle, icon, color }: MetricCardProps) {\n  const { currentTheme, theme } = useTheme();\n\n  const getColorClasses = () => {\n    const baseColors = {\n      blue: theme.colors.accent.primary,\n      green: theme.colors.accent.success,\n      purple: theme.colors.accent.secondary,\n      orange: theme.colors.accent.warning\n    };\n\n    return {\n      backgroundColor: `${baseColors[color]}20`,\n      color: baseColors[color]\n    };\n  };\n\n  return (\n    <ThemedCard className=\"p-6\" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p\n            className=\"text-sm font-medium\"\n            style={{ color: theme.colors.text.secondary }}\n          >\n            {title}\n          </p>\n          <p\n            className={`text-2xl font-semibold mt-1 ${\n              currentTheme === 'cyber' ? 'text-cyber' :\n              currentTheme === 'neon' ? 'text-neon' :\n              currentTheme === 'matrix' ? 'text-matrix' : ''\n            }`}\n            style={{ color: theme.colors.text.primary }}\n          >\n            {value}\n          </p>\n          {change !== undefined && (\n            <div className=\"flex items-center mt-2\">\n              {change >= 0 ? (\n                <TrendingUp\n                  className=\"h-4 w-4 mr-1\"\n                  style={{ color: theme.colors.accent.success }}\n                />\n              ) : (\n                <TrendingDown\n                  className=\"h-4 w-4 mr-1\"\n                  style={{ color: theme.colors.accent.error }}\n                />\n              )}\n              <span\n                className=\"text-sm\"\n                style={{ color: change >= 0 ? theme.colors.accent.success : theme.colors.accent.error }}\n              >\n                {Math.abs(change).toFixed(1)}%\n              </span>\n            </div>\n          )}\n          {subtitle && (\n            <p\n              className=\"text-xs mt-1\"\n              style={{ color: theme.colors.text.tertiary }}\n            >\n              {subtitle}\n            </p>\n          )}\n        </div>\n        <div\n          className=\"p-3 rounded-lg\"\n          style={getColorClasses()}\n        >\n          {icon}\n        </div>\n      </div>\n    </ThemedCard>\n  );\n}\n\nfunction getCategoryColor(category: string): string {\n  const colors: Record<string, string> = {\n    'Food & Dining': '#EF4444',\n    'Transportation': '#3B82F6',\n    'Shopping': '#8B5CF6',\n    'Entertainment': '#F59E0B',\n    'Bills & Utilities': '#10B981',\n    'Healthcare': '#EC4899',\n    'Education': '#6366F1',\n    'Travel': '#14B8A6',\n    'Other': '#6B7280'\n  };\n  return colors[category] || '#6B7280';\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAqBe,SAAS,UAAU,EAAE,QAAQ,EAAkB;IAC5D,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEvC,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,IAAI,QAAQ;QAC9B,MAAM,WAAW,IAAI,WAAW;QAChC,MAAM,YAAY,cAAc,IAAI,KAAK,YAAY;QACrD,MAAM,gBAAgB,cAAc,IAAI,WAAW,IAAI;QAEvD,sBAAsB;QACtB,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA;YACxC,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;YACzC,OAAO,YAAY,QAAQ,OAAO,aAAa,YAAY,WAAW,OAAO;QAC/E;QAEA,sBAAsB;QACtB,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA;YACxC,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;YACzC,OAAO,YAAY,QAAQ,OAAO,aAAa,YAAY,WAAW,OAAO;QAC/E;QAEA,mBAAmB;QACnB,MAAM,QAAQ,IAAI,YAAY;QAC9B,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UACpC,IAAI,KAAK,QAAQ,IAAI,EAAE,YAAY,OAAO;QAG5C,qBAAqB;QACrB,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,IAAI,OAAO,KAAK,IAAI,MAAM;QAC5C,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;YACvC,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;YACzC,OAAO,eAAe;QACxB;QAEA,qBAAqB;QACrB,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK;YAC3C,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,MAAM;YACrE,OAAO;QACT,GAAG,CAAC;QAEJ,gCAAgC;QAChC,MAAM,aAAa,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,CAAC,GAAG;YAChD,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,CAAC,KAAK,CAAC;YACrC,OAAO,KAAK,YAAY;QAC1B;QAEA,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA;YACnC,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,UAClC,IAAI,KAAK,QAAQ,IAAI,EAAE,YAAY,OAAO;YAE5C,OAAO,YAAY,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QACpE;QAEA,mBAAmB;QACnB,MAAM,iBAAiB,kBAAkB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QACxF,MAAM,iBAAiB,kBAAkB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QACxF,MAAM,aAAa,cAAc,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QAChF,MAAM,gBAAgB,iBAAiB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QACtF,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QAE9E,oBAAoB;QACpB,MAAM,gBAAgB,iBAAiB,IAAI,AAAC,CAAC,iBAAiB,cAAc,IAAI,iBAAkB,MAAM;QACxG,MAAM,mBAAmB,kBAAkB,MAAM,GAAG,IAAI,iBAAiB,IAAI,OAAO,OAAO,KAAK;QAEhG,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,cAAc,SAAS,MAAM;YAC7B,kBAAkB,SAAS,MAAM,GAAG,IAAI,gBAAgB,SAAS,MAAM,GAAG;QAC5E;IACF,GAAG;QAAC;KAAS;IAEb,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ,UAAU,UAAU,CAAC,GAAG,CAAC,CAAA;YAC/B,MAAM,IAAI,IAAI,KAAK;YACnB,OAAO,GAAG,EAAE,QAAQ,KAAK,EAAE,CAAC,EAAE,EAAE,OAAO,IAAI;QAC7C;QACA,SAAS,UAAU,aAAa;IAClC;IAEA,MAAM,eAAe,OAAO,OAAO,CAAC,UAAU,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,GAAK,CAAC;YACzF;YACA;YACA,OAAO,iBAAiB;QAC1B,CAAC;IAED,MAAM,wBAAwB;QAC5B;YACE,OAAO;YACP,aAAa,UAAU,cAAc;YACrC,cAAc,EAAE,qDAAqD;QACvE;QACA;YACE,OAAO;YACP,aAAa,UAAU,cAAc;YACrC,cAAc;QAChB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAC,CAAC,EAAE,UAAU,cAAc,CAAC,OAAO,CAAC,IAAI;wBAChD,QAAQ,UAAU,aAAa;wBAC/B,oBAAM,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBAC5B,OAAM;;;;;;kCAER,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAC,CAAC,EAAE,UAAU,aAAa,CAAC,OAAO,CAAC,IAAI;wBAC/C,UAAU,GAAG,KAAK,IAAI,CAAC,UAAU,aAAa,GAAG,GAAG,QAAQ,CAAC;wBAC7D,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAM;;;;;;kCAER,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,OAAO,CAAC,IAAI;wBAC5C,UAAU,CAAC,IAAI,EAAE,UAAU,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;wBAC5D,oBAAM,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC9B,OAAM;;;;;;kCAER,8OAAC;wBACC,OAAM;wBACN,OAAO,UAAU,YAAY,CAAC,QAAQ;wBACtC,UAAU,CAAC,CAAC,EAAE,UAAU,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;wBACzD,oBAAM,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACxB,OAAM;;;;;;;;;;;;0BAKV,8OAAC,mIAAA,CAAA,UAAa;gBAAC,UAAU;;;;;;0BAGzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,qIAAA,CAAA,aAAU;wBAAC,WAAU;wBAAM,SAAS,iBAAiB;wBAAS,MAAM,iBAAiB;;0CACpF,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gCAAC;0CAC3C;;;;;;0CAGD,8OAAC,4HAAA,CAAA,qBAAkB;gCAAC,MAAM;;;;;;;;;;;;kCAG5B,8OAAC,qIAAA,CAAA,aAAU;wBAAC,WAAU;wBAAM,SAAS,iBAAiB;wBAAS,MAAM,iBAAiB;;0CACpF,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gCAAC;0CAC3C;;;;;;4BAGA,aAAa,MAAM,GAAG,kBACrB,8OAAC,4HAAA,CAAA,yBAAsB;gCAAC,MAAM;;;;;qDAE9B,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;gCAAC;0CAC7C;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,qIAAA,CAAA,aAAU;gBAAC,WAAU;gBAAM,SAAS,iBAAiB;gBAAS,MAAM,iBAAiB;;kCACpF,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wBAAC;kCAC3C;;;;;;kCAGD,8OAAC,4HAAA,CAAA,yBAAsB;wBAAC,MAAM;;;;;;;;;;;;0BAIhC,8OAAC,qIAAA,CAAA,aAAU;gBAAC,WAAU;gBAAM,SAAS,iBAAiB;gBAAS,MAAM,iBAAiB;;kCACpF,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wBAAC;kCAC3C;;;;;;kCAGD,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,UAAU,cAAc,EACrC,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,IAAI,GACzB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO;4BACtB,MAAM,aAAa,AAAC,SAAS,UAAU,aAAa,GAAI;4BACxD,qBACE,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,iBAAiB;gDAAU;;;;;;0DAEvD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gDAAC;0DAEzC;;;;;;;;;;;;kDAGL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gDAAC;;oDAC3C;oDACG,OAAO,OAAO,CAAC;;;;;;;0DAEnB,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;gDAAC;;oDAE3C,WAAW,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;+BAxBnB;;;;;wBA6Bd;;;;;;;;;;;;;;;;;;AAKZ;AAWA,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAmB;IAClF,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEvC,MAAM,kBAAkB;QACtB,MAAM,aAAa;YACjB,MAAM,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;YACjC,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;YAClC,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS;YACrC,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;QACrC;QAEA,OAAO;YACL,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,OAAO,UAAU,CAAC,MAAM;QAC1B;IACF;IAEA,qBACE,8OAAC,qIAAA,CAAA,aAAU;QAAC,WAAU;QAAM,SAAS,iBAAiB;QAAS,MAAM,iBAAiB;kBACpF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;4BAAC;sCAE3C;;;;;;sCAEH,8OAAC;4BACC,WAAW,CAAC,4BAA4B,EACtC,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;4BACF,OAAO;gCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;4BAAC;sCAEzC;;;;;;wBAEF,WAAW,2BACV,8OAAC;4BAAI,WAAU;;gCACZ,UAAU,kBACT,8OAAC,kNAAA,CAAA,aAAU;oCACT,WAAU;oCACV,OAAO;wCAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;oCAAC;;;;;yDAG9C,8OAAC,sNAAA,CAAA,eAAY;oCACX,WAAU;oCACV,OAAO;wCAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK;oCAAC;;;;;;8CAG9C,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,UAAU,IAAI,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK;oCAAC;;wCAErF,KAAK,GAAG,CAAC,QAAQ,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAIlC,0BACC,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ;4BAAC;sCAE1C;;;;;;;;;;;;8BAIP,8OAAC;oBACC,WAAU;oBACV,OAAO;8BAEN;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,iBAAiB,QAAgB;IACxC,MAAM,SAAiC;QACrC,iBAAiB;QACjB,kBAAkB;QAClB,YAAY;QACZ,iBAAiB;QACjB,qBAAqB;QACrB,cAAc;QACd,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA,OAAO,MAAM,CAAC,SAAS,IAAI;AAC7B", "debugId": null}}, {"offset": {"line": 3376, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/CameraCapture.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useCallback } from 'react';\nimport { Camera, X, RotateCcw, Check, Zap } from 'lucide-react';\n\ninterface CameraCaptureProps {\n  onCapture: (file: File) => void;\n  onClose: () => void;\n}\n\nexport default function CameraCapture({ onCapture, onClose }: CameraCaptureProps) {\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [capturedImage, setCapturedImage] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const streamRef = useRef<MediaStream | null>(null);\n\n  const startCamera = useCallback(async () => {\n    try {\n      setError(null);\n      \n      // Request camera access with optimal settings for document capture\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment', // Use back camera on mobile\n          width: { ideal: 1920 },\n          height: { ideal: 1080 },\n          aspectRatio: { ideal: 16/9 }\n        }\n      });\n\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        streamRef.current = stream;\n        setIsStreaming(true);\n      }\n    } catch (err) {\n      console.error('Camera access error:', err);\n      setError('Unable to access camera. Please check permissions and try again.');\n    }\n  }, []);\n\n  const stopCamera = useCallback(() => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n      streamRef.current = null;\n    }\n    setIsStreaming(false);\n  }, []);\n\n  const capturePhoto = useCallback(() => {\n    if (!videoRef.current || !canvasRef.current) return;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const context = canvas.getContext('2d');\n\n    if (!context) return;\n\n    // Set canvas dimensions to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw the video frame to canvas\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Apply image enhancements for better OCR\n    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);\n    enhanceImageForOCR(imageData);\n    context.putImageData(imageData, 0, 0);\n\n    // Convert to data URL\n    const dataURL = canvas.toDataURL('image/jpeg', 0.9);\n    setCapturedImage(dataURL);\n    stopCamera();\n  }, [stopCamera]);\n\n  const retakePhoto = useCallback(() => {\n    setCapturedImage(null);\n    startCamera();\n  }, [startCamera]);\n\n  const confirmCapture = useCallback(() => {\n    if (!capturedImage) return;\n\n    // Convert data URL to File\n    fetch(capturedImage)\n      .then(res => res.blob())\n      .then(blob => {\n        const file = new File([blob], `receipt-${Date.now()}.jpg`, { type: 'image/jpeg' });\n        onCapture(file);\n        onClose();\n      })\n      .catch(err => {\n        console.error('Error converting image:', err);\n        setError('Failed to process captured image');\n      });\n  }, [capturedImage, onCapture, onClose]);\n\n  // Cleanup on unmount\n  React.useEffect(() => {\n    return () => {\n      stopCamera();\n    };\n  }, [stopCamera]);\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 flex flex-col\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 bg-black text-white\">\n        <div className=\"flex items-center space-x-3\">\n          <Camera className=\"h-6 w-6\" />\n          <div>\n            <h2 className=\"text-lg font-semibold\">Capture Receipt</h2>\n            <p className=\"text-sm text-gray-300\">Position receipt in frame and tap capture</p>\n          </div>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"p-2 rounded-full bg-gray-800 hover:bg-gray-700\"\n        >\n          <X className=\"h-6 w-6\" />\n        </button>\n      </div>\n\n      {/* Camera View */}\n      <div className=\"flex-1 relative overflow-hidden\">\n        {error && (\n          <div className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 z-10\">\n            <div className=\"bg-red-600 text-white p-6 rounded-lg max-w-sm mx-4 text-center\">\n              <p className=\"mb-4\">{error}</p>\n              <button\n                onClick={() => setError(null)}\n                className=\"bg-white text-red-600 px-4 py-2 rounded-lg font-medium\"\n              >\n                Try Again\n              </button>\n            </div>\n          </div>\n        )}\n\n        {capturedImage ? (\n          // Show captured image\n          <div className=\"relative w-full h-full flex items-center justify-center bg-black\">\n            <img\n              src={capturedImage}\n              alt=\"Captured receipt\"\n              className=\"max-w-full max-h-full object-contain\"\n            />\n            \n            {/* Overlay with enhancement indicator */}\n            <div className=\"absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm flex items-center space-x-2\">\n              <Zap className=\"h-4 w-4\" />\n              <span>Enhanced for OCR</span>\n            </div>\n          </div>\n        ) : (\n          // Show camera stream\n          <div className=\"relative w-full h-full\">\n            <video\n              ref={videoRef}\n              autoPlay\n              playsInline\n              muted\n              className=\"w-full h-full object-cover\"\n              onLoadedMetadata={startCamera}\n            />\n            \n            {/* Camera overlay guides */}\n            <div className=\"absolute inset-0 pointer-events-none\">\n              {/* Corner guides */}\n              <div className=\"absolute top-8 left-8 w-8 h-8 border-l-2 border-t-2 border-white opacity-75\"></div>\n              <div className=\"absolute top-8 right-8 w-8 h-8 border-r-2 border-t-2 border-white opacity-75\"></div>\n              <div className=\"absolute bottom-8 left-8 w-8 h-8 border-l-2 border-b-2 border-white opacity-75\"></div>\n              <div className=\"absolute bottom-8 right-8 w-8 h-8 border-r-2 border-b-2 border-white opacity-75\"></div>\n              \n              {/* Center guide */}\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"border-2 border-dashed border-white opacity-50 w-80 h-60 rounded-lg\"></div>\n              </div>\n              \n              {/* Instructions */}\n              <div className=\"absolute bottom-32 left-0 right-0 text-center\">\n                <div className=\"bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg mx-4\">\n                  <p className=\"text-sm\">Position receipt within the frame</p>\n                  <p className=\"text-xs text-gray-300 mt-1\">Ensure good lighting and avoid shadows</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Hidden canvas for image processing */}\n        <canvas ref={canvasRef} className=\"hidden\" />\n      </div>\n\n      {/* Controls */}\n      <div className=\"bg-black p-6\">\n        {capturedImage ? (\n          // Captured image controls\n          <div className=\"flex items-center justify-center space-x-6\">\n            <button\n              onClick={retakePhoto}\n              className=\"flex items-center space-x-2 bg-gray-700 text-white px-6 py-3 rounded-full hover:bg-gray-600\"\n            >\n              <RotateCcw className=\"h-5 w-5\" />\n              <span>Retake</span>\n            </button>\n            <button\n              onClick={confirmCapture}\n              className=\"flex items-center space-x-2 bg-green-600 text-white px-8 py-3 rounded-full hover:bg-green-700\"\n            >\n              <Check className=\"h-5 w-5\" />\n              <span>Use Photo</span>\n            </button>\n          </div>\n        ) : (\n          // Camera controls\n          <div className=\"flex items-center justify-center\">\n            {isStreaming ? (\n              <button\n                onClick={capturePhoto}\n                className=\"w-20 h-20 bg-white rounded-full border-4 border-gray-300 hover:border-blue-500 transition-colors flex items-center justify-center\"\n              >\n                <div className=\"w-16 h-16 bg-white rounded-full shadow-lg\"></div>\n              </button>\n            ) : (\n              <button\n                onClick={startCamera}\n                className=\"flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-full hover:bg-blue-700\"\n              >\n                <Camera className=\"h-5 w-5\" />\n                <span>Start Camera</span>\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Enhance image for better OCR results\n */\nfunction enhanceImageForOCR(imageData: ImageData) {\n  const data = imageData.data;\n  \n  for (let i = 0; i < data.length; i += 4) {\n    // Convert to grayscale\n    const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);\n    \n    // Increase contrast\n    const contrast = 1.5;\n    const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255));\n    const enhanced = Math.min(255, Math.max(0, factor * (gray - 128) + 128));\n    \n    // Apply to all channels\n    data[i] = enhanced;     // Red\n    data[i + 1] = enhanced; // Green\n    data[i + 2] = enhanced; // Blue\n    // Alpha channel remains unchanged\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAUe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAE7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,SAAS;YAET,mEAAmE;YACnE,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBACL,YAAY;oBACZ,OAAO;wBAAE,OAAO;oBAAK;oBACrB,QAAQ;wBAAE,OAAO;oBAAK;oBACtB,aAAa;wBAAE,OAAO,KAAG;oBAAE;gBAC7B;YACF;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,UAAU,OAAO,GAAG;gBACpB,eAAe;YACjB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YACzD,UAAU,OAAO,GAAG;QACtB;QACA,eAAe;IACjB,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;QAE7C,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,UAAU,OAAO,UAAU,CAAC;QAElC,IAAI,CAAC,SAAS;QAEd,uCAAuC;QACvC,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,iCAAiC;QACjC,QAAQ,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE1D,0CAA0C;QAC1C,MAAM,YAAY,QAAQ,YAAY,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACxE,mBAAmB;QACnB,QAAQ,YAAY,CAAC,WAAW,GAAG;QAEnC,sBAAsB;QACtB,MAAM,UAAU,OAAO,SAAS,CAAC,cAAc;QAC/C,iBAAiB;QACjB;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,iBAAiB;QACjB;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,eAAe;QAEpB,2BAA2B;QAC3B,MAAM,eACH,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA;YACJ,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;gBAAE,MAAM;YAAa;YAChF,UAAU;YACV;QACF,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX;IACJ,GAAG;QAAC;QAAe;QAAW;KAAQ;IAEtC,qBAAqB;IACrB,MAAM,SAAS,CAAC;QACd,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAGzC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKjB,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAQ;;;;;;8CACrB,8OAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;oBAON,gBACC,sBAAsB;kCACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;;;;;;+BAIV,qBAAqB;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK;gCACL,QAAQ;gCACR,WAAW;gCACX,KAAK;gCACL,WAAU;gCACV,kBAAkB;;;;;;0CAIpB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDAGf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,8OAAC;wBAAO,KAAK;wBAAW,WAAU;;;;;;;;;;;;0BAIpC,8OAAC;gBAAI,WAAU;0BACZ,gBACC,0BAA0B;8BAC1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;2BAIV,kBAAkB;8BAClB,8OAAC;oBAAI,WAAU;8BACZ,4BACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;6CAGjB,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;AAEA;;CAEC,GACD,SAAS,mBAAmB,SAAoB;IAC9C,MAAM,OAAO,UAAU,IAAI;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;QACvC,uBAAuB;QACvB,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;QAEnF,oBAAoB;QACpB,MAAM,WAAW;QACjB,MAAM,SAAS,AAAC,MAAM,CAAC,WAAW,MAAM,GAAG,IAAK,CAAC,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC;QAC7E,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,IAAI;QAEnE,wBAAwB;QACxB,IAAI,CAAC,EAAE,GAAG,UAAc,MAAM;QAC9B,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,QAAQ;QAChC,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,OAAO;IAC/B,kCAAkC;IACpC;AACF", "debugId": null}}, {"offset": {"line": 3898, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/lib/report-generator.ts"], "sourcesContent": ["import jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\nimport { format, startOfMonth, endOfMonth, subMonths } from 'date-fns';\n\nexport interface MonthlyReportData {\n  month: string;\n  year: number;\n  totalExpenses: number;\n  totalTransactions: number;\n  categoryBreakdown: Array<{\n    category: string;\n    amount: number;\n    percentage: number;\n    transactionCount: number;\n  }>;\n  budgetAnalysis: Array<{\n    category: string;\n    budgetAmount: number;\n    spentAmount: number;\n    variance: number;\n    variancePercentage: number;\n  }>;\n  topExpenses: Array<{\n    merchant: string;\n    amount: number;\n    date: string;\n    category: string;\n  }>;\n  insights: string[];\n  comparisonToPreviousMonth: {\n    totalChange: number;\n    totalChangePercentage: number;\n    categoryChanges: Array<{\n      category: string;\n      change: number;\n      changePercentage: number;\n    }>;\n  };\n}\n\n/**\n * Generate insights based on spending data\n */\nexport const generateInsights = (data: MonthlyReportData): string[] => {\n  const insights: string[] = [];\n\n  // Top spending category\n  const topCategory = data.categoryBreakdown.reduce((prev, current) => \n    prev.amount > current.amount ? prev : current\n  );\n  insights.push(`Your highest spending category was ${topCategory.category} at $${topCategory.amount.toFixed(2)} (${topCategory.percentage.toFixed(1)}% of total)`);\n\n  // Budget performance\n  const overBudgetCategories = data.budgetAnalysis.filter(b => b.variance < 0);\n  if (overBudgetCategories.length > 0) {\n    const totalOverspend = overBudgetCategories.reduce((sum, cat) => sum + Math.abs(cat.variance), 0);\n    insights.push(`You exceeded budget in ${overBudgetCategories.length} categories, overspending by $${totalOverspend.toFixed(2)} total`);\n  } else {\n    const totalUnderBudget = data.budgetAnalysis.reduce((sum, cat) => sum + Math.max(0, cat.variance), 0);\n    insights.push(`Great job! You stayed under budget and saved $${totalUnderBudget.toFixed(2)} across all categories`);\n  }\n\n  // Comparison to previous month\n  if (data.comparisonToPreviousMonth.totalChangePercentage > 10) {\n    insights.push(`Your spending increased by ${data.comparisonToPreviousMonth.totalChangePercentage.toFixed(1)}% compared to last month`);\n  } else if (data.comparisonToPreviousMonth.totalChangePercentage < -10) {\n    insights.push(`You reduced spending by ${Math.abs(data.comparisonToPreviousMonth.totalChangePercentage).toFixed(1)}% compared to last month`);\n  }\n\n  // Average transaction size\n  const avgTransaction = data.totalExpenses / data.totalTransactions;\n  insights.push(`Your average transaction was $${avgTransaction.toFixed(2)}`);\n\n  // Frequent spending patterns\n  const frequentCategories = data.categoryBreakdown.filter(c => c.transactionCount >= 5);\n  if (frequentCategories.length > 0) {\n    const mostFrequent = frequentCategories.reduce((prev, current) => \n      prev.transactionCount > current.transactionCount ? prev : current\n    );\n    insights.push(`You made ${mostFrequent.transactionCount} transactions in ${mostFrequent.category}, your most active category`);\n  }\n\n  return insights;\n};\n\n/**\n * Generate HTML content for the report\n */\nexport const generateReportHTML = (data: MonthlyReportData): string => {\n  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;\n  const formatPercentage = (percentage: number) => `${percentage.toFixed(1)}%`;\n\n  return `\n    <div style=\"font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;\">\n      <header style=\"text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3B82F6; padding-bottom: 20px;\">\n        <h1 style=\"color: #1F2937; margin: 0;\">Monthly Expense Report</h1>\n        <h2 style=\"color: #6B7280; margin: 10px 0 0 0;\">${data.month} ${data.year}</h2>\n      </header>\n\n      <section style=\"margin-bottom: 30px;\">\n        <h3 style=\"color: #1F2937; border-bottom: 1px solid #E5E7EB; padding-bottom: 10px;\">Summary</h3>\n        <div style=\"display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-top: 15px;\">\n          <div style=\"background: #F3F4F6; padding: 15px; border-radius: 8px;\">\n            <h4 style=\"margin: 0 0 5px 0; color: #374151;\">Total Expenses</h4>\n            <p style=\"margin: 0; font-size: 24px; font-weight: bold; color: #1F2937;\">${formatCurrency(data.totalExpenses)}</p>\n          </div>\n          <div style=\"background: #F3F4F6; padding: 15px; border-radius: 8px;\">\n            <h4 style=\"margin: 0 0 5px 0; color: #374151;\">Total Transactions</h4>\n            <p style=\"margin: 0; font-size: 24px; font-weight: bold; color: #1F2937;\">${data.totalTransactions}</p>\n          </div>\n        </div>\n      </section>\n\n      <section style=\"margin-bottom: 30px;\">\n        <h3 style=\"color: #1F2937; border-bottom: 1px solid #E5E7EB; padding-bottom: 10px;\">Category Breakdown</h3>\n        <table style=\"width: 100%; border-collapse: collapse; margin-top: 15px;\">\n          <thead>\n            <tr style=\"background: #F9FAFB;\">\n              <th style=\"text-align: left; padding: 12px; border: 1px solid #E5E7EB;\">Category</th>\n              <th style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">Amount</th>\n              <th style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">Percentage</th>\n              <th style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">Transactions</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${data.categoryBreakdown.map(category => `\n              <tr>\n                <td style=\"padding: 12px; border: 1px solid #E5E7EB;\">${category.category}</td>\n                <td style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">${formatCurrency(category.amount)}</td>\n                <td style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">${formatPercentage(category.percentage)}</td>\n                <td style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">${category.transactionCount}</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n      </section>\n\n      <section style=\"margin-bottom: 30px;\">\n        <h3 style=\"color: #1F2937; border-bottom: 1px solid #E5E7EB; padding-bottom: 10px;\">Budget Performance</h3>\n        <table style=\"width: 100%; border-collapse: collapse; margin-top: 15px;\">\n          <thead>\n            <tr style=\"background: #F9FAFB;\">\n              <th style=\"text-align: left; padding: 12px; border: 1px solid #E5E7EB;\">Category</th>\n              <th style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">Budget</th>\n              <th style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">Spent</th>\n              <th style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">Variance</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${data.budgetAnalysis.map(budget => `\n              <tr>\n                <td style=\"padding: 12px; border: 1px solid #E5E7EB;\">${budget.category}</td>\n                <td style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">${formatCurrency(budget.budgetAmount)}</td>\n                <td style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">${formatCurrency(budget.spentAmount)}</td>\n                <td style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB; color: ${budget.variance >= 0 ? '#059669' : '#DC2626'};\">\n                  ${budget.variance >= 0 ? '+' : ''}${formatCurrency(budget.variance)} (${formatPercentage(budget.variancePercentage)})\n                </td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n      </section>\n\n      <section style=\"margin-bottom: 30px;\">\n        <h3 style=\"color: #1F2937; border-bottom: 1px solid #E5E7EB; padding-bottom: 10px;\">Top Expenses</h3>\n        <table style=\"width: 100%; border-collapse: collapse; margin-top: 15px;\">\n          <thead>\n            <tr style=\"background: #F9FAFB;\">\n              <th style=\"text-align: left; padding: 12px; border: 1px solid #E5E7EB;\">Merchant</th>\n              <th style=\"text-align: left; padding: 12px; border: 1px solid #E5E7EB;\">Category</th>\n              <th style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">Amount</th>\n              <th style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">Date</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${data.topExpenses.map(expense => `\n              <tr>\n                <td style=\"padding: 12px; border: 1px solid #E5E7EB;\">${expense.merchant}</td>\n                <td style=\"padding: 12px; border: 1px solid #E5E7EB;\">${expense.category}</td>\n                <td style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">${formatCurrency(expense.amount)}</td>\n                <td style=\"text-align: right; padding: 12px; border: 1px solid #E5E7EB;\">${expense.date}</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n      </section>\n\n      <section style=\"margin-bottom: 30px;\">\n        <h3 style=\"color: #1F2937; border-bottom: 1px solid #E5E7EB; padding-bottom: 10px;\">Insights & Recommendations</h3>\n        <ul style=\"margin-top: 15px; padding-left: 20px;\">\n          ${data.insights.map(insight => `<li style=\"margin-bottom: 10px; color: #374151;\">${insight}</li>`).join('')}\n        </ul>\n      </section>\n\n      <footer style=\"text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #E5E7EB; color: #6B7280; font-size: 12px;\">\n        <p>Generated on ${format(new Date(), 'MMMM d, yyyy')} by Expense Tracker</p>\n      </footer>\n    </div>\n  `;\n};\n\n/**\n * Generate and download PDF report\n */\nexport const generatePDFReport = async (data: MonthlyReportData): Promise<void> => {\n  // Create a temporary div with the report HTML\n  const reportDiv = document.createElement('div');\n  reportDiv.innerHTML = generateReportHTML(data);\n  reportDiv.style.position = 'absolute';\n  reportDiv.style.left = '-9999px';\n  reportDiv.style.top = '0';\n  document.body.appendChild(reportDiv);\n\n  try {\n    // Convert HTML to canvas\n    const canvas = await html2canvas(reportDiv, {\n      scale: 2,\n      useCORS: true,\n      allowTaint: true,\n    });\n\n    // Create PDF\n    const pdf = new jsPDF('p', 'mm', 'a4');\n    const imgData = canvas.toDataURL('image/png');\n    \n    const pdfWidth = pdf.internal.pageSize.getWidth();\n    const pdfHeight = pdf.internal.pageSize.getHeight();\n    const imgWidth = canvas.width;\n    const imgHeight = canvas.height;\n    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);\n    const imgX = (pdfWidth - imgWidth * ratio) / 2;\n    const imgY = 0;\n\n    pdf.addImage(imgData, 'PNG', imgX, imgY, imgWidth * ratio, imgHeight * ratio);\n    \n    // Download the PDF\n    const fileName = `expense-report-${data.month.toLowerCase()}-${data.year}.pdf`;\n    pdf.save(fileName);\n  } finally {\n    // Clean up\n    document.body.removeChild(reportDiv);\n  }\n};\n\n/**\n * Export report data as CSV\n */\nexport const exportToCSV = (data: MonthlyReportData): void => {\n  const csvContent = [\n    ['Category', 'Amount', 'Percentage', 'Transactions'],\n    ...data.categoryBreakdown.map(cat => [\n      cat.category,\n      cat.amount.toFixed(2),\n      cat.percentage.toFixed(1) + '%',\n      cat.transactionCount.toString()\n    ])\n  ].map(row => row.join(',')).join('\\n');\n\n  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n  const link = document.createElement('a');\n  const url = URL.createObjectURL(blob);\n  link.setAttribute('href', url);\n  link.setAttribute('download', `expense-data-${data.month.toLowerCase()}-${data.year}.csv`);\n  link.style.visibility = 'hidden';\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAyCO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,WAAqB,EAAE;IAE7B,wBAAwB;IACxB,MAAM,cAAc,KAAK,iBAAiB,CAAC,MAAM,CAAC,CAAC,MAAM,UACvD,KAAK,MAAM,GAAG,QAAQ,MAAM,GAAG,OAAO;IAExC,SAAS,IAAI,CAAC,CAAC,mCAAmC,EAAE,YAAY,QAAQ,CAAC,KAAK,EAAE,YAAY,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC;IAEhK,qBAAqB;IACrB,MAAM,uBAAuB,KAAK,cAAc,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG;IAC1E,IAAI,qBAAqB,MAAM,GAAG,GAAG;QACnC,MAAM,iBAAiB,qBAAqB,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,IAAI,QAAQ,GAAG;QAC/F,SAAS,IAAI,CAAC,CAAC,uBAAuB,EAAE,qBAAqB,MAAM,CAAC,8BAA8B,EAAE,eAAe,OAAO,CAAC,GAAG,MAAM,CAAC;IACvI,OAAO;QACL,MAAM,mBAAmB,KAAK,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,QAAQ,GAAG;QACnG,SAAS,IAAI,CAAC,CAAC,8CAA8C,EAAE,iBAAiB,OAAO,CAAC,GAAG,sBAAsB,CAAC;IACpH;IAEA,+BAA+B;IAC/B,IAAI,KAAK,yBAAyB,CAAC,qBAAqB,GAAG,IAAI;QAC7D,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,KAAK,yBAAyB,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,wBAAwB,CAAC;IACvI,OAAO,IAAI,KAAK,yBAAyB,CAAC,qBAAqB,GAAG,CAAC,IAAI;QACrE,SAAS,IAAI,CAAC,CAAC,wBAAwB,EAAE,KAAK,GAAG,CAAC,KAAK,yBAAyB,CAAC,qBAAqB,EAAE,OAAO,CAAC,GAAG,wBAAwB,CAAC;IAC9I;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,KAAK,aAAa,GAAG,KAAK,iBAAiB;IAClE,SAAS,IAAI,CAAC,CAAC,8BAA8B,EAAE,eAAe,OAAO,CAAC,IAAI;IAE1E,6BAA6B;IAC7B,MAAM,qBAAqB,KAAK,iBAAiB,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,IAAI;IACpF,IAAI,mBAAmB,MAAM,GAAG,GAAG;QACjC,MAAM,eAAe,mBAAmB,MAAM,CAAC,CAAC,MAAM,UACpD,KAAK,gBAAgB,GAAG,QAAQ,gBAAgB,GAAG,OAAO;QAE5D,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,aAAa,gBAAgB,CAAC,iBAAiB,EAAE,aAAa,QAAQ,CAAC,2BAA2B,CAAC;IAC/H;IAEA,OAAO;AACT;AAKO,MAAM,qBAAqB,CAAC;IACjC,MAAM,iBAAiB,CAAC,SAAmB,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAClE,MAAM,mBAAmB,CAAC,aAAuB,GAAG,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC;IAE5E,OAAO,CAAC;;;;wDAI8C,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC;;;;;;;;sFAQI,EAAE,eAAe,KAAK,aAAa,EAAE;;;;sFAIrC,EAAE,KAAK,iBAAiB,CAAC;;;;;;;;;;;;;;;;;YAiBnG,EAAE,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAA,WAAY,CAAC;;sEAEgB,EAAE,SAAS,QAAQ,CAAC;yFACD,EAAE,eAAe,SAAS,MAAM,EAAE;yFAClC,EAAE,iBAAiB,SAAS,UAAU,EAAE;yFACxC,EAAE,SAAS,gBAAgB,CAAC;;YAEzG,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;YAiBZ,EAAE,KAAK,cAAc,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;;sEAEqB,EAAE,OAAO,QAAQ,CAAC;yFACC,EAAE,eAAe,OAAO,YAAY,EAAE;yFACtC,EAAE,eAAe,OAAO,WAAW,EAAE;+FAC/B,EAAE,OAAO,QAAQ,IAAI,IAAI,YAAY,UAAU;kBAC5H,EAAE,OAAO,QAAQ,IAAI,IAAI,MAAM,KAAK,eAAe,OAAO,QAAQ,EAAE,EAAE,EAAE,iBAAiB,OAAO,kBAAkB,EAAE;;;YAG1H,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;YAiBZ,EAAE,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;;sEAEuB,EAAE,QAAQ,QAAQ,CAAC;sEACnB,EAAE,QAAQ,QAAQ,CAAC;yFACA,EAAE,eAAe,QAAQ,MAAM,EAAE;yFACjC,EAAE,QAAQ,IAAI,CAAC;;YAE5F,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;UAQd,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC,iDAAiD,EAAE,QAAQ,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;wBAK9F,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,gBAAgB;;;EAG3D,CAAC;AACH;AAKO,MAAM,oBAAoB,OAAO;IACtC,8CAA8C;IAC9C,MAAM,YAAY,SAAS,aAAa,CAAC;IACzC,UAAU,SAAS,GAAG,mBAAmB;IACzC,UAAU,KAAK,CAAC,QAAQ,GAAG;IAC3B,UAAU,KAAK,CAAC,IAAI,GAAG;IACvB,UAAU,KAAK,CAAC,GAAG,GAAG;IACtB,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,IAAI;QACF,yBAAyB;QACzB,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAW,AAAD,EAAE,WAAW;YAC1C,OAAO;YACP,SAAS;YACT,YAAY;QACd;QAEA,aAAa;QACb,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK,CAAC,KAAK,MAAM;QACjC,MAAM,UAAU,OAAO,SAAS,CAAC;QAEjC,MAAM,WAAW,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ;QAC/C,MAAM,YAAY,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS;QACjD,MAAM,WAAW,OAAO,KAAK;QAC7B,MAAM,YAAY,OAAO,MAAM;QAC/B,MAAM,QAAQ,KAAK,GAAG,CAAC,WAAW,UAAU,YAAY;QACxD,MAAM,OAAO,CAAC,WAAW,WAAW,KAAK,IAAI;QAC7C,MAAM,OAAO;QAEb,IAAI,QAAQ,CAAC,SAAS,OAAO,MAAM,MAAM,WAAW,OAAO,YAAY;QAEvE,mBAAmB;QACnB,MAAM,WAAW,CAAC,eAAe,EAAE,KAAK,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC;QAC9E,IAAI,IAAI,CAAC;IACX,SAAU;QACR,WAAW;QACX,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAKO,MAAM,cAAc,CAAC;IAC1B,MAAM,aAAa;QACjB;YAAC;YAAY;YAAU;YAAc;SAAe;WACjD,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAA,MAAO;gBACnC,IAAI,QAAQ;gBACZ,IAAI,MAAM,CAAC,OAAO,CAAC;gBACnB,IAAI,UAAU,CAAC,OAAO,CAAC,KAAK;gBAC5B,IAAI,gBAAgB,CAAC,QAAQ;aAC9B;KACF,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;IAEjC,MAAM,OAAO,IAAI,KAAK;QAAC;KAAW,EAAE;QAAE,MAAM;IAA0B;IACtE,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,KAAK,YAAY,CAAC,QAAQ;IAC1B,KAAK,YAAY,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC;IACzF,KAAK,KAAK,CAAC,UAAU,GAAG;IACxB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 4119, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/ExportPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Download, FileText, BarChart, Calendar, Loader2 } from 'lucide-react';\nimport { generatePDFReport, exportToCSV, generateReportHTML } from '@/lib/report-generator';\nimport { ThemedCard, ThemedButton } from './ThemeComponents';\nimport { useTheme } from '@/contexts/ThemeContext';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\ninterface ExportPanelProps {\n  expenses: Expense[];\n}\n\nexport default function ExportPanel({ expenses }: ExportPanelProps) {\n  const { currentTheme, theme } = useTheme();\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [exportType, setExportType] = useState<string | null>(null);\n\n  const generateMonthlyReport = async () => {\n    if (expenses.length === 0) {\n      alert('No expenses to export');\n      return;\n    }\n\n    setIsGenerating(true);\n    setExportType('pdf');\n\n    try {\n      // Prepare report data\n      const now = new Date();\n      const reportData = {\n        month: now.toLocaleString('default', { month: 'long' }),\n        year: now.getFullYear(),\n        totalExpenses: expenses.reduce((sum, e) => sum + e.amount, 0),\n        totalTransactions: expenses.length,\n        categoryBreakdown: generateCategoryBreakdown(expenses),\n        budgetAnalysis: [], // Would be populated with actual budget data\n        topExpenses: expenses\n          .sort((a, b) => b.amount - a.amount)\n          .slice(0, 10)\n          .map(e => ({\n            merchant: e.merchant,\n            amount: e.amount,\n            date: e.date,\n            category: e.category\n          })),\n        insights: generateQuickInsights(expenses),\n        comparisonToPreviousMonth: {\n          totalChange: 0,\n          totalChangePercentage: 0,\n          categoryChanges: []\n        }\n      };\n\n      await generatePDFReport(reportData);\n    } catch (error) {\n      console.error('Error generating PDF report:', error);\n      alert('Failed to generate PDF report');\n    } finally {\n      setIsGenerating(false);\n      setExportType(null);\n    }\n  };\n\n  const exportCSV = () => {\n    if (expenses.length === 0) {\n      alert('No expenses to export');\n      return;\n    }\n\n    setIsGenerating(true);\n    setExportType('csv');\n\n    try {\n      const reportData = {\n        month: new Date().toLocaleString('default', { month: 'long' }),\n        year: new Date().getFullYear(),\n        totalExpenses: 0,\n        totalTransactions: 0,\n        categoryBreakdown: generateCategoryBreakdown(expenses),\n        budgetAnalysis: [],\n        topExpenses: [],\n        insights: [],\n        comparisonToPreviousMonth: {\n          totalChange: 0,\n          totalChangePercentage: 0,\n          categoryChanges: []\n        }\n      };\n\n      exportToCSV(reportData);\n    } catch (error) {\n      console.error('Error exporting CSV:', error);\n      alert('Failed to export CSV');\n    } finally {\n      setIsGenerating(false);\n      setExportType(null);\n    }\n  };\n\n  const previewReport = () => {\n    if (expenses.length === 0) {\n      alert('No expenses to preview');\n      return;\n    }\n\n    const reportData = {\n      month: new Date().toLocaleString('default', { month: 'long' }),\n      year: new Date().getFullYear(),\n      totalExpenses: expenses.reduce((sum, e) => sum + e.amount, 0),\n      totalTransactions: expenses.length,\n      categoryBreakdown: generateCategoryBreakdown(expenses),\n      budgetAnalysis: [],\n      topExpenses: expenses\n        .sort((a, b) => b.amount - a.amount)\n        .slice(0, 10)\n        .map(e => ({\n          merchant: e.merchant,\n          amount: e.amount,\n          date: e.date,\n          category: e.category\n        })),\n      insights: generateQuickInsights(expenses),\n      comparisonToPreviousMonth: {\n        totalChange: 0,\n        totalChangePercentage: 0,\n        categoryChanges: []\n      }\n    };\n\n    const htmlContent = generateReportHTML(reportData);\n    const newWindow = window.open('', '_blank');\n    if (newWindow) {\n      newWindow.document.write(htmlContent);\n      newWindow.document.close();\n    }\n  };\n\n  return (\n    <ThemedCard className=\"p-6\" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n      <div className=\"flex items-center space-x-3 mb-6\">\n        <div\n          className=\"p-2 rounded-lg\"\n          style={{\n            backgroundColor: `${theme.colors.accent.success}20`,\n            color: theme.colors.accent.success\n          }}\n        >\n          <Download className=\"h-6 w-6\" />\n        </div>\n        <div>\n          <h2\n            className={`text-xl font-semibold ${\n              currentTheme === 'cyber' ? 'text-cyber' :\n              currentTheme === 'neon' ? 'text-neon' :\n              currentTheme === 'matrix' ? 'text-matrix' : ''\n            }`}\n            style={{ color: theme.colors.text.primary }}\n          >\n            Export & Reports\n          </h2>\n          <p\n            className=\"text-sm\"\n            style={{ color: theme.colors.text.secondary }}\n          >\n            Generate detailed reports and export your data\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        {/* PDF Report */}\n        <ThemedCard\n          className=\"p-4 border-2 transition-colors duration-300\"\n          style={{ borderColor: theme.colors.border.secondary }}\n          hover3D={currentTheme === 'cyber'}\n        >\n          <div className=\"flex items-center space-x-3 mb-3\">\n            <FileText\n              className=\"h-8 w-8\"\n              style={{ color: theme.colors.accent.error }}\n            />\n            <div>\n              <h3\n                className=\"font-medium\"\n                style={{ color: theme.colors.text.primary }}\n              >\n                PDF Report\n              </h3>\n              <p\n                className=\"text-sm\"\n                style={{ color: theme.colors.text.secondary }}\n              >\n                Comprehensive monthly report\n              </p>\n            </div>\n          </div>\n          <ThemedButton\n            onClick={generateMonthlyReport}\n            disabled={isGenerating && exportType === 'pdf'}\n            variant=\"error\"\n            cyber3D={currentTheme === 'cyber'}\n            className=\"w-full flex items-center justify-center space-x-2\"\n          >\n            {isGenerating && exportType === 'pdf' ? (\n              <>\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span>Generating...</span>\n              </>\n            ) : (\n              <>\n                <Download className=\"h-4 w-4\" />\n                <span>Download PDF</span>\n              </>\n            )}\n          </ThemedButton>\n        </ThemedCard>\n\n        {/* CSV Export */}\n        <ThemedCard\n          className=\"p-4 border-2 transition-colors duration-300\"\n          style={{ borderColor: theme.colors.border.secondary }}\n          hover3D={currentTheme === 'cyber'}\n        >\n          <div className=\"flex items-center space-x-3 mb-3\">\n            <BarChart\n              className=\"h-8 w-8\"\n              style={{ color: theme.colors.accent.success }}\n            />\n            <div>\n              <h3\n                className=\"font-medium\"\n                style={{ color: theme.colors.text.primary }}\n              >\n                CSV Export\n              </h3>\n              <p\n                className=\"text-sm\"\n                style={{ color: theme.colors.text.secondary }}\n              >\n                Raw data for analysis\n              </p>\n            </div>\n          </div>\n          <ThemedButton\n            onClick={exportCSV}\n            disabled={isGenerating && exportType === 'csv'}\n            variant=\"success\"\n            cyber3D={currentTheme === 'cyber'}\n            className=\"w-full flex items-center justify-center space-x-2\"\n          >\n            {isGenerating && exportType === 'csv' ? (\n              <>\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span>Exporting...</span>\n              </>\n            ) : (\n              <>\n                <Download className=\"h-4 w-4\" />\n                <span>Export CSV</span>\n              </>\n            )}\n          </ThemedButton>\n        </ThemedCard>\n\n        {/* Preview Report */}\n        <ThemedCard\n          className=\"p-4 border-2 transition-colors duration-300\"\n          style={{ borderColor: theme.colors.border.secondary }}\n          hover3D={currentTheme === 'cyber'}\n        >\n          <div className=\"flex items-center space-x-3 mb-3\">\n            <Calendar\n              className=\"h-8 w-8\"\n              style={{ color: theme.colors.accent.secondary }}\n            />\n            <div>\n              <h3\n                className=\"font-medium\"\n                style={{ color: theme.colors.text.primary }}\n              >\n                Preview Report\n              </h3>\n              <p\n                className=\"text-sm\"\n                style={{ color: theme.colors.text.secondary }}\n              >\n                View before downloading\n              </p>\n            </div>\n          </div>\n          <ThemedButton\n            onClick={previewReport}\n            variant=\"secondary\"\n            cyber3D={currentTheme === 'cyber'}\n            className=\"w-full flex items-center justify-center space-x-2\"\n          >\n            <FileText className=\"h-4 w-4\" />\n            <span>Preview</span>\n          </ThemedButton>\n        </ThemedCard>\n      </div>\n\n      {/* Export Summary */}\n      <div\n        className=\"mt-6 p-4 rounded-lg\"\n        style={{ backgroundColor: theme.colors.bg.tertiary }}\n      >\n        <h4\n          className=\"font-medium mb-2\"\n          style={{ color: theme.colors.text.primary }}\n        >\n          Export Summary\n        </h4>\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n          <div>\n            <span style={{ color: theme.colors.text.secondary }}>Total Expenses:</span>\n            <span\n              className=\"ml-2 font-medium\"\n              style={{ color: theme.colors.text.primary }}\n            >\n              {expenses.length}\n            </span>\n          </div>\n          <div>\n            <span style={{ color: theme.colors.text.secondary }}>Total Amount:</span>\n            <span\n              className=\"ml-2 font-medium\"\n              style={{ color: theme.colors.text.primary }}\n            >\n              ${expenses.reduce((sum, e) => sum + e.amount, 0).toFixed(2)}\n            </span>\n          </div>\n          <div>\n            <span style={{ color: theme.colors.text.secondary }}>Categories:</span>\n            <span\n              className=\"ml-2 font-medium\"\n              style={{ color: theme.colors.text.primary }}\n            >\n              {new Set(expenses.map(e => e.category)).size}\n            </span>\n          </div>\n          <div>\n            <span style={{ color: theme.colors.text.secondary }}>Date Range:</span>\n            <span\n              className=\"ml-2 font-medium\"\n              style={{ color: theme.colors.text.primary }}\n            >\n              {expenses.length > 0 ? getDateRange(expenses) : 'N/A'}\n            </span>\n          </div>\n        </div>\n      </div>\n    </ThemedCard>\n  );\n}\n\n// Helper functions\nfunction generateCategoryBreakdown(expenses: Expense[]) {\n  const categoryTotals = expenses.reduce((acc, expense) => {\n    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;\n    return acc;\n  }, {} as Record<string, number>);\n\n  const total = expenses.reduce((sum, e) => sum + e.amount, 0);\n\n  return Object.entries(categoryTotals).map(([category, amount]) => ({\n    category,\n    amount,\n    percentage: total > 0 ? (amount / total) * 100 : 0,\n    transactionCount: expenses.filter(e => e.category === category).length\n  }));\n}\n\nfunction generateQuickInsights(expenses: Expense[]): string[] {\n  const insights: string[] = [];\n  \n  if (expenses.length === 0) return insights;\n\n  const total = expenses.reduce((sum, e) => sum + e.amount, 0);\n  const average = total / expenses.length;\n  \n  insights.push(`Average expense amount: $${average.toFixed(2)}`);\n  \n  const categoryTotals = expenses.reduce((acc, expense) => {\n    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;\n    return acc;\n  }, {} as Record<string, number>);\n\n  const topCategory = Object.entries(categoryTotals).reduce((a, b) => a[1] > b[1] ? a : b);\n  insights.push(`Highest spending category: ${topCategory[0]} ($${topCategory[1].toFixed(2)})`);\n\n  return insights;\n}\n\nfunction getDateRange(expenses: Expense[]): string {\n  const dates = expenses.map(e => new Date(e.date)).sort((a, b) => a.getTime() - b.getTime());\n  const start = dates[0];\n  const end = dates[dates.length - 1];\n  \n  if (start.toDateString() === end.toDateString()) {\n    return start.toLocaleDateString();\n  }\n  \n  return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAoBe,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,wBAAwB;QAC5B,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,cAAc;QAEd,IAAI;YACF,sBAAsB;YACtB,MAAM,MAAM,IAAI;YAChB,MAAM,aAAa;gBACjB,OAAO,IAAI,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAO;gBACrD,MAAM,IAAI,WAAW;gBACrB,eAAe,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;gBAC3D,mBAAmB,SAAS,MAAM;gBAClC,mBAAmB,0BAA0B;gBAC7C,gBAAgB,EAAE;gBAClB,aAAa,SACV,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM,EAClC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,IAAK,CAAC;wBACT,UAAU,EAAE,QAAQ;wBACpB,QAAQ,EAAE,MAAM;wBAChB,MAAM,EAAE,IAAI;wBACZ,UAAU,EAAE,QAAQ;oBACtB,CAAC;gBACH,UAAU,sBAAsB;gBAChC,2BAA2B;oBACzB,aAAa;oBACb,uBAAuB;oBACvB,iBAAiB,EAAE;gBACrB;YACF;YAEA,MAAM,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR,SAAU;YACR,gBAAgB;YAChB,cAAc;QAChB;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,cAAc;QAEd,IAAI;YACF,MAAM,aAAa;gBACjB,OAAO,IAAI,OAAO,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAO;gBAC5D,MAAM,IAAI,OAAO,WAAW;gBAC5B,eAAe;gBACf,mBAAmB;gBACnB,mBAAmB,0BAA0B;gBAC7C,gBAAgB,EAAE;gBAClB,aAAa,EAAE;gBACf,UAAU,EAAE;gBACZ,2BAA2B;oBACzB,aAAa;oBACb,uBAAuB;oBACvB,iBAAiB,EAAE;gBACrB;YACF;YAEA,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,gBAAgB;YAChB,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,MAAM;YACN;QACF;QAEA,MAAM,aAAa;YACjB,OAAO,IAAI,OAAO,cAAc,CAAC,WAAW;gBAAE,OAAO;YAAO;YAC5D,MAAM,IAAI,OAAO,WAAW;YAC5B,eAAe,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YAC3D,mBAAmB,SAAS,MAAM;YAClC,mBAAmB,0BAA0B;YAC7C,gBAAgB,EAAE;YAClB,aAAa,SACV,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM,EAClC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,IAAK,CAAC;oBACT,UAAU,EAAE,QAAQ;oBACpB,QAAQ,EAAE,MAAM;oBAChB,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,QAAQ;gBACtB,CAAC;YACH,UAAU,sBAAsB;YAChC,2BAA2B;gBACzB,aAAa;gBACb,uBAAuB;gBACvB,iBAAiB,EAAE;YACrB;QACF;QAEA,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;QACvC,MAAM,YAAY,OAAO,IAAI,CAAC,IAAI;QAClC,IAAI,WAAW;YACb,UAAU,QAAQ,CAAC,KAAK,CAAC;YACzB,UAAU,QAAQ,CAAC,KAAK;QAC1B;IACF;IAEA,qBACE,8OAAC,qIAAA,CAAA,aAAU;QAAC,WAAU;QAAM,SAAS,iBAAiB;QAAS,MAAM,iBAAiB;;0BACpF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;4BACnD,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wBACpC;kCAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC;;0CACC,8OAAC;gCACC,WAAW,CAAC,sBAAsB,EAChC,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;gCACF,OAAO;oCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gCAAC;0CAC3C;;;;;;0CAGD,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;gCAAC;0CAC7C;;;;;;;;;;;;;;;;;;0BAML,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,qIAAA,CAAA,aAAU;wBACT,WAAU;wBACV,OAAO;4BAAE,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS;wBAAC;wBACpD,SAAS,iBAAiB;;0CAE1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCACP,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK;wCAAC;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gDAAC;0DAC3C;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;gDAAC;0DAC7C;;;;;;;;;;;;;;;;;;0CAKL,8OAAC,qIAAA,CAAA,eAAY;gCACX,SAAS;gCACT,UAAU,gBAAgB,eAAe;gCACzC,SAAQ;gCACR,SAAS,iBAAiB;gCAC1B,WAAU;0CAET,gBAAgB,eAAe,sBAC9B;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;sDAAK;;;;;;;iEAGR;;sDACE,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC,qIAAA,CAAA,aAAU;wBACT,WAAU;wBACV,OAAO;4BAAE,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS;wBAAC;wBACpD,SAAS,iBAAiB;;0CAE1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6OAAA,CAAA,WAAQ;wCACP,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wCAAC;;;;;;kDAE9C,8OAAC;;0DACC,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gDAAC;0DAC3C;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;gDAAC;0DAC7C;;;;;;;;;;;;;;;;;;0CAKL,8OAAC,qIAAA,CAAA,eAAY;gCACX,SAAS;gCACT,UAAU,gBAAgB,eAAe;gCACzC,SAAQ;gCACR,SAAS,iBAAiB;gCAC1B,WAAU;0CAET,gBAAgB,eAAe,sBAC9B;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;sDAAK;;;;;;;iEAGR;;sDACE,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC,qIAAA,CAAA,aAAU;wBACT,WAAU;wBACV,OAAO;4BAAE,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS;wBAAC;wBACpD,SAAS,iBAAiB;;0CAE1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCACP,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS;wCAAC;;;;;;kDAEhD,8OAAC;;0DACC,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gDAAC;0DAC3C;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;gDAAC;0DAC7C;;;;;;;;;;;;;;;;;;0CAKL,8OAAC,qIAAA,CAAA,eAAY;gCACX,SAAS;gCACT,SAAQ;gCACR,SAAS,iBAAiB;gCAC1B,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,iBAAiB,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ;gBAAC;;kCAEnD,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wBAAC;kCAC3C;;;;;;kCAGD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAK,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;wCAAC;kDAAG;;;;;;kDACrD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wCAAC;kDAEzC,SAAS,MAAM;;;;;;;;;;;;0CAGpB,8OAAC;;kDACC,8OAAC;wCAAK,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;wCAAC;kDAAG;;;;;;kDACrD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wCAAC;;4CAC3C;4CACG,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;;;;;;;;;;;;;0CAG7D,8OAAC;;kDACC,8OAAC;wCAAK,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;wCAAC;kDAAG;;;;;;kDACrD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wCAAC;kDAEzC,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,IAAI;;;;;;;;;;;;0CAGhD,8OAAC;;kDACC,8OAAC;wCAAK,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;wCAAC;kDAAG;;;;;;kDACrD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wCAAC;kDAEzC,SAAS,MAAM,GAAG,IAAI,aAAa,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9D;AAEA,mBAAmB;AACnB,SAAS,0BAA0B,QAAmB;IACpD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK;QAC3C,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,MAAM;QACrE,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,QAAQ,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAE1D,OAAO,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,GAAK,CAAC;YACjE;YACA;YACA,YAAY,QAAQ,IAAI,AAAC,SAAS,QAAS,MAAM;YACjD,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;QACxE,CAAC;AACH;AAEA,SAAS,sBAAsB,QAAmB;IAChD,MAAM,WAAqB,EAAE;IAE7B,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;IAElC,MAAM,QAAQ,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAC1D,MAAM,UAAU,QAAQ,SAAS,MAAM;IAEvC,SAAS,IAAI,CAAC,CAAC,yBAAyB,EAAE,QAAQ,OAAO,CAAC,IAAI;IAE9D,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK;QAC3C,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,MAAM;QACrE,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,cAAc,OAAO,OAAO,CAAC,gBAAgB,MAAM,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI;IACtF,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,WAAW,CAAC,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAE5F,OAAO;AACT;AAEA,SAAS,aAAa,QAAmB;IACvC,MAAM,QAAQ,SAAS,GAAG,CAAC,CAAA,IAAK,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,KAAK,EAAE,OAAO;IACxF,MAAM,QAAQ,KAAK,CAAC,EAAE;IACtB,MAAM,MAAM,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IAEnC,IAAI,MAAM,YAAY,OAAO,IAAI,YAAY,IAAI;QAC/C,OAAO,MAAM,kBAAkB;IACjC;IAEA,OAAO,GAAG,MAAM,kBAAkB,GAAG,GAAG,EAAE,IAAI,kBAAkB,IAAI;AACtE", "debugId": null}}, {"offset": {"line": 4826, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/ThemeSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ap, <PERSON><PERSON>les, Code, X, Check } from 'lucide-react';\nimport { useTheme, themes, type ThemeType } from '@/contexts/ThemeContext';\nimport { ThemedCard, ThemedButton } from './ThemeComponents';\n\ninterface ThemeSwitcherProps {\n  showModal?: boolean;\n  onClose?: () => void;\n}\n\nexport default function ThemeSwitcher({ showModal = false, onClose }: ThemeSwitcherProps) {\n  const { currentTheme, setTheme } = useTheme();\n  const [previewTheme, setPreviewTheme] = useState<ThemeType | null>(null);\n\n  const themeIcons = {\n    light: <Monitor className=\"h-5 w-5\" />,\n    dark: <Moon className=\"h-5 w-5\" />,\n    cyber: <Zap className=\"h-5 w-5\" />,\n    neon: <Sparkles className=\"h-5 w-5\" />,\n    matrix: <Code className=\"h-5 w-5\" />,\n  };\n\n  const handleThemeSelect = (theme: ThemeType) => {\n    setTheme(theme);\n    if (onClose) onClose();\n  };\n\n  const handlePreview = (theme: ThemeType) => {\n    setPreviewTheme(theme);\n    // Temporarily apply theme for preview\n    const root = document.documentElement;\n    const themeColors = themes[theme].colors;\n    \n    root.style.setProperty('--bg-primary', themeColors.bg.primary);\n    root.style.setProperty('--bg-secondary', themeColors.bg.secondary);\n    root.style.setProperty('--text-primary', themeColors.text.primary);\n    root.style.setProperty('--accent-primary', themeColors.accent.primary);\n  };\n\n  const stopPreview = () => {\n    setPreviewTheme(null);\n    // Restore current theme\n    const root = document.documentElement;\n    const themeColors = themes[currentTheme].colors;\n    \n    root.style.setProperty('--bg-primary', themeColors.bg.primary);\n    root.style.setProperty('--bg-secondary', themeColors.bg.secondary);\n    root.style.setProperty('--text-primary', themeColors.text.primary);\n    root.style.setProperty('--accent-primary', themeColors.accent.primary);\n  };\n\n  if (!showModal) {\n    // Compact theme switcher button\n    return (\n      <div className=\"relative\">\n        <ThemedButton\n          onClick={() => {}}\n          variant=\"secondary\"\n          size=\"sm\"\n          className=\"flex items-center space-x-2\"\n          cyber3D={currentTheme === 'cyber'}\n        >\n          <Palette className=\"h-4 w-4\" />\n          <span className=\"hidden sm:inline\">{themes[currentTheme].name}</span>\n        </ThemedButton>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n      <ThemedCard className=\"max-w-4xl w-full max-h-[90vh] overflow-y-auto\" hover3D={currentTheme === 'cyber'}>\n        <div className=\"p-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center space-x-3\">\n              <div className={`p-2 rounded-lg ${\n                currentTheme === 'cyber' ? 'bg-green-400/20 text-green-400' :\n                currentTheme === 'neon' ? 'bg-pink-500/20 text-pink-500' :\n                currentTheme === 'matrix' ? 'bg-green-500/20 text-green-500' :\n                currentTheme === 'dark' ? 'bg-blue-500/20 text-blue-400' :\n                'bg-blue-100 text-blue-600'\n              }`}>\n                <Palette className=\"h-6 w-6\" />\n              </div>\n              <div>\n                <h2 className={`text-xl font-semibold ${\n                  currentTheme === 'cyber' ? 'text-green-400' :\n                  currentTheme === 'neon' ? 'text-pink-500' :\n                  currentTheme === 'matrix' ? 'text-green-500' :\n                  currentTheme === 'dark' ? 'text-gray-100' :\n                  'text-gray-900'\n                }`}>\n                  Choose Your Theme\n                </h2>\n                <p className={`text-sm ${\n                  currentTheme === 'cyber' ? 'text-green-400/70' :\n                  currentTheme === 'neon' ? 'text-pink-500/70' :\n                  currentTheme === 'matrix' ? 'text-green-500/70' :\n                  currentTheme === 'dark' ? 'text-gray-400' :\n                  'text-gray-500'\n                }`}>\n                  Select a theme to customize your experience\n                </p>\n              </div>\n            </div>\n            <ThemedButton\n              onClick={onClose}\n              variant=\"secondary\"\n              size=\"sm\"\n              className=\"!p-2\"\n            >\n              <X className=\"h-4 w-4\" />\n            </ThemedButton>\n          </div>\n\n          {/* Theme Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {Object.entries(themes).map(([themeId, theme]) => (\n              <ThemePreviewCard\n                key={themeId}\n                theme={theme}\n                isActive={currentTheme === themeId}\n                isPreview={previewTheme === themeId}\n                onSelect={() => handleThemeSelect(themeId as ThemeType)}\n                onPreview={() => handlePreview(themeId as ThemeType)}\n                onStopPreview={stopPreview}\n                icon={themeIcons[themeId as ThemeType]}\n              />\n            ))}\n          </div>\n\n          {/* Preview Notice */}\n          {previewTheme && (\n            <div className={`mt-6 p-4 rounded-lg border-2 ${\n              currentTheme === 'cyber' ? 'bg-green-400/10 border-green-400/50 text-green-400' :\n              currentTheme === 'neon' ? 'bg-pink-500/10 border-pink-500/50 text-pink-500' :\n              currentTheme === 'matrix' ? 'bg-green-500/10 border-green-500/50 text-green-500' :\n              currentTheme === 'dark' ? 'bg-blue-500/10 border-blue-500/50 text-blue-400' :\n              'bg-blue-50 border-blue-200 text-blue-700'\n            }`}>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2\">\n                  <Sparkles className=\"h-5 w-5\" />\n                  <span className=\"font-medium\">\n                    Previewing {themes[previewTheme].name}\n                  </span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <ThemedButton\n                    onClick={() => handleThemeSelect(previewTheme)}\n                    variant=\"success\"\n                    size=\"sm\"\n                    className=\"flex items-center space-x-1\"\n                  >\n                    <Check className=\"h-4 w-4\" />\n                    <span>Apply</span>\n                  </ThemedButton>\n                  <ThemedButton\n                    onClick={stopPreview}\n                    variant=\"secondary\"\n                    size=\"sm\"\n                  >\n                    Cancel\n                  </ThemedButton>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </ThemedCard>\n    </div>\n  );\n}\n\ninterface ThemePreviewCardProps {\n  theme: any;\n  isActive: boolean;\n  isPreview: boolean;\n  onSelect: () => void;\n  onPreview: () => void;\n  onStopPreview: () => void;\n  icon: React.ReactNode;\n}\n\nfunction ThemePreviewCard({ \n  theme, \n  isActive, \n  isPreview, \n  onSelect, \n  onPreview, \n  onStopPreview, \n  icon \n}: ThemePreviewCardProps) {\n  const { currentTheme } = useTheme();\n  \n  const getPreviewStyles = () => {\n    const styles = {\n      backgroundColor: theme.colors.bg.primary,\n      borderColor: theme.colors.border.primary,\n      color: theme.colors.text.primary,\n    };\n    \n    if (theme.id === 'cyber') {\n      return {\n        ...styles,\n        boxShadow: '0 0 20px rgba(0, 255, 136, 0.3)',\n        border: '2px solid rgba(0, 255, 136, 0.5)',\n      };\n    } else if (theme.id === 'neon') {\n      return {\n        ...styles,\n        boxShadow: '0 0 25px rgba(255, 0, 110, 0.4)',\n        border: '2px solid rgba(255, 0, 110, 0.5)',\n      };\n    } else if (theme.id === 'matrix') {\n      return {\n        ...styles,\n        boxShadow: '0 0 20px rgba(0, 255, 0, 0.4)',\n        border: '2px solid rgba(0, 255, 0, 0.5)',\n      };\n    }\n    \n    return styles;\n  };\n\n  return (\n    <div\n      className={`\n        relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300\n        transform hover:scale-105 hover:rotate-1\n        ${isActive ? 'ring-4 ring-blue-500/50' : ''}\n        ${isPreview ? 'ring-4 ring-yellow-500/50' : ''}\n        ${theme.id === 'cyber' ? 'hover:shadow-[0_0_30px_rgba(0,255,136,0.5)]' : ''}\n        ${theme.id === 'neon' ? 'hover:shadow-[0_0_35px_rgba(255,0,110,0.6)]' : ''}\n        ${theme.id === 'matrix' ? 'hover:shadow-[0_0_30px_rgba(0,255,0,0.6)]' : ''}\n      `}\n      style={getPreviewStyles()}\n      onMouseEnter={onPreview}\n      onMouseLeave={onStopPreview}\n      onClick={onSelect}\n    >\n      {/* Active indicator */}\n      {isActive && (\n        <div className=\"absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\">\n          <Check className=\"h-4 w-4 text-white\" />\n        </div>\n      )}\n\n      {/* Theme preview content */}\n      <div className=\"space-y-3\">\n        <div className=\"flex items-center space-x-3\">\n          <div \n            className=\"p-2 rounded-lg\"\n            style={{ backgroundColor: theme.colors.accent.primary, color: theme.colors.text.inverse }}\n          >\n            {icon}\n          </div>\n          <div>\n            <h3 className=\"font-semibold\">{theme.name}</h3>\n            <p className=\"text-sm opacity-70\">{theme.description}</p>\n          </div>\n        </div>\n\n        {/* Mini preview elements */}\n        <div className=\"space-y-2\">\n          <div \n            className=\"h-2 rounded-full\"\n            style={{ backgroundColor: theme.colors.accent.primary }}\n          />\n          <div className=\"flex space-x-2\">\n            <div \n              className=\"h-2 w-1/3 rounded-full\"\n              style={{ backgroundColor: theme.colors.accent.secondary }}\n            />\n            <div \n              className=\"h-2 w-1/4 rounded-full\"\n              style={{ backgroundColor: theme.colors.accent.success }}\n            />\n            <div \n              className=\"h-2 w-1/5 rounded-full\"\n              style={{ backgroundColor: theme.colors.accent.warning }}\n            />\n          </div>\n        </div>\n\n        {/* Special effects for futuristic themes */}\n        {(theme.id === 'cyber' || theme.id === 'neon' || theme.id === 'matrix') && (\n          <div className=\"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white/5 pointer-events-none rounded-lg\" />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAYe,SAAS,cAAc,EAAE,YAAY,KAAK,EAAE,OAAO,EAAsB;IACtF,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEnE,MAAM,aAAa;QACjB,qBAAO,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC1B,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,qBAAO,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QACtB,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,sBAAQ,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAC1B;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS;QACT,IAAI,SAAS;IACf;IAEA,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;QAChB,sCAAsC;QACtC,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,cAAc,gIAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM;QAExC,KAAK,KAAK,CAAC,WAAW,CAAC,gBAAgB,YAAY,EAAE,CAAC,OAAO;QAC7D,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,EAAE,CAAC,SAAS;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,IAAI,CAAC,OAAO;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;IACvE;IAEA,MAAM,cAAc;QAClB,gBAAgB;QAChB,wBAAwB;QACxB,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,cAAc,gIAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM;QAE/C,KAAK,KAAK,CAAC,WAAW,CAAC,gBAAgB,YAAY,EAAE,CAAC,OAAO;QAC7D,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,EAAE,CAAC,SAAS;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,IAAI,CAAC,OAAO;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;IACvE;IAEA,IAAI,CAAC,WAAW;QACd,gCAAgC;QAChC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,qIAAA,CAAA,eAAY;gBACX,SAAS,KAAO;gBAChB,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,iBAAiB;;kCAE1B,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAoB,gIAAA,CAAA,SAAM,CAAC,aAAa,CAAC,IAAI;;;;;;;;;;;;;;;;;IAIrE;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,qIAAA,CAAA,aAAU;YAAC,WAAU;YAAgD,SAAS,iBAAiB;sBAC9F,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,eAAe,EAC9B,iBAAiB,UAAU,mCAC3B,iBAAiB,SAAS,iCAC1B,iBAAiB,WAAW,mCAC5B,iBAAiB,SAAS,iCAC1B,6BACA;kDACA,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;;0DACC,8OAAC;gDAAG,WAAW,CAAC,sBAAsB,EACpC,iBAAiB,UAAU,mBAC3B,iBAAiB,SAAS,kBAC1B,iBAAiB,WAAW,mBAC5B,iBAAiB,SAAS,kBAC1B,iBACA;0DAAE;;;;;;0DAGJ,8OAAC;gDAAE,WAAW,CAAC,QAAQ,EACrB,iBAAiB,UAAU,sBAC3B,iBAAiB,SAAS,qBAC1B,iBAAiB,WAAW,sBAC5B,iBAAiB,SAAS,kBAC1B,iBACA;0DAAE;;;;;;;;;;;;;;;;;;0CAKR,8OAAC,qIAAA,CAAA,eAAY;gCACX,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,gIAAA,CAAA,SAAM,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,MAAM,iBAC3C,8OAAC;gCAEC,OAAO;gCACP,UAAU,iBAAiB;gCAC3B,WAAW,iBAAiB;gCAC5B,UAAU,IAAM,kBAAkB;gCAClC,WAAW,IAAM,cAAc;gCAC/B,eAAe;gCACf,MAAM,UAAU,CAAC,QAAqB;+BAPjC;;;;;;;;;;oBAaV,8BACC,8OAAC;wBAAI,WAAW,CAAC,6BAA6B,EAC5C,iBAAiB,UAAU,uDAC3B,iBAAiB,SAAS,oDAC1B,iBAAiB,WAAW,uDAC5B,iBAAiB,SAAS,oDAC1B,4CACA;kCACA,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAK,WAAU;;gDAAc;gDAChB,gIAAA,CAAA,SAAM,CAAC,aAAa,CAAC,IAAI;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,qIAAA,CAAA,eAAY;4CACX,SAAS,IAAM,kBAAkB;4CACjC,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,qIAAA,CAAA,eAAY;4CACX,SAAS;4CACT,SAAQ;4CACR,MAAK;sDACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;AAYA,SAAS,iBAAiB,EACxB,KAAK,EACL,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACb,IAAI,EACkB;IACtB,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEhC,MAAM,mBAAmB;QACvB,MAAM,SAAS;YACb,iBAAiB,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YACxC,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;YACxC,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;QAClC;QAEA,IAAI,MAAM,EAAE,KAAK,SAAS;YACxB,OAAO;gBACL,GAAG,MAAM;gBACT,WAAW;gBACX,QAAQ;YACV;QACF,OAAO,IAAI,MAAM,EAAE,KAAK,QAAQ;YAC9B,OAAO;gBACL,GAAG,MAAM;gBACT,WAAW;gBACX,QAAQ;YACV;QACF,OAAO,IAAI,MAAM,EAAE,KAAK,UAAU;YAChC,OAAO;gBACL,GAAG,MAAM;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC;;;QAGV,EAAE,WAAW,4BAA4B,GAAG;QAC5C,EAAE,YAAY,8BAA8B,GAAG;QAC/C,EAAE,MAAM,EAAE,KAAK,UAAU,gDAAgD,GAAG;QAC5E,EAAE,MAAM,EAAE,KAAK,SAAS,gDAAgD,GAAG;QAC3E,EAAE,MAAM,EAAE,KAAK,WAAW,8CAA8C,GAAG;MAC7E,CAAC;QACD,OAAO;QACP,cAAc;QACd,cAAc;QACd,SAAS;;YAGR,0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAKrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;oCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gCAAC;0CAEvF;;;;;;0CAEH,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiB,MAAM,IAAI;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAAsB,MAAM,WAAW;;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;gCAAC;;;;;;0CAExD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS;wCAAC;;;;;;kDAE1D,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wCAAC;;;;;;kDAExD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wCAAC;;;;;;;;;;;;;;;;;;oBAM3D,CAAC,MAAM,EAAE,KAAK,WAAW,MAAM,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,QAAQ,mBACpE,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 5343, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Globe, Check, Loader2 } from 'lucide-react';\nimport { useLanguage, SUPPORTED_LANGUAGES, LanguageCode } from '@/contexts/LanguageContext';\nimport { ThemedCard, ThemedButton } from './ThemeComponents';\nimport { useTheme } from '@/contexts/ThemeContext';\n\ninterface LanguageSwitcherProps {\n  showModal?: boolean;\n  onClose?: () => void;\n}\n\nexport default function LanguageSwitcher({ showModal = false, onClose }: LanguageSwitcherProps) {\n  const { currentLanguage, setLanguage, isTranslating } = useLanguage();\n  const { currentTheme, theme } = useTheme();\n  const [isOpen, setIsOpen] = useState(showModal);\n\n  const handleLanguageChange = (langCode: LanguageCode) => {\n    setLanguage(langCode);\n    setIsOpen(false);\n    onClose?.();\n  };\n\n  const toggleOpen = () => {\n    setIsOpen(!isOpen);\n  };\n\n  if (showModal && isOpen) {\n    return (\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n        <ThemedCard \n          className=\"max-w-2xl w-full max-h-[80vh] overflow-y-auto\"\n          hover3D={currentTheme === 'cyber'}\n          glow={currentTheme !== 'light'}\n        >\n          <div className=\"p-6\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <div className=\"flex items-center space-x-3\">\n                <div \n                  className=\"p-2 rounded-lg\"\n                  style={{ \n                    backgroundColor: `${theme.colors.accent.primary}20`,\n                    color: theme.colors.accent.primary \n                  }}\n                >\n                  <Globe className=\"h-6 w-6\" />\n                </div>\n                <div>\n                  <h2 \n                    className={`text-xl font-semibold ${\n                      currentTheme === 'cyber' ? 'text-cyber' :\n                      currentTheme === 'neon' ? 'text-neon' :\n                      currentTheme === 'matrix' ? 'text-matrix' : ''\n                    }`}\n                    style={{ color: theme.colors.text.primary }}\n                  >\n                    Choose Language\n                  </h2>\n                  <p \n                    className=\"text-sm\"\n                    style={{ color: theme.colors.text.secondary }}\n                  >\n                    Select your preferred language for the interface\n                  </p>\n                </div>\n              </div>\n              {isTranslating && (\n                <div className=\"flex items-center space-x-2\">\n                  <Loader2 \n                    className=\"h-4 w-4 animate-spin\" \n                    style={{ color: theme.colors.accent.primary }}\n                  />\n                  <span \n                    className=\"text-sm\"\n                    style={{ color: theme.colors.text.secondary }}\n                  >\n                    Translating...\n                  </span>\n                </div>\n              )}\n            </div>\n\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\">\n              {Object.values(SUPPORTED_LANGUAGES).map((lang) => (\n                <ThemedButton\n                  key={lang.code}\n                  onClick={() => handleLanguageChange(lang.code)}\n                  variant={currentLanguage === lang.code ? 'primary' : 'secondary'}\n                  cyber3D={currentTheme === 'cyber'}\n                  className={`p-4 flex items-center justify-between text-left transition-all duration-300 ${\n                    currentLanguage === lang.code \n                      ? 'ring-2 ring-offset-2' \n                      : 'hover:scale-105'\n                  }`}\n                  style={{\n                    ringColor: currentLanguage === lang.code ? theme.colors.accent.primary : 'transparent',\n                    ringOffsetColor: theme.colors.bg.card\n                  }}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-2xl\">{lang.flag}</span>\n                    <div>\n                      <div \n                        className=\"font-medium\"\n                        style={{ color: theme.colors.text.primary }}\n                      >\n                        {lang.nativeName}\n                      </div>\n                      <div \n                        className=\"text-sm\"\n                        style={{ color: theme.colors.text.secondary }}\n                      >\n                        {lang.name}\n                      </div>\n                    </div>\n                  </div>\n                  {currentLanguage === lang.code && (\n                    <Check \n                      className=\"h-5 w-5\" \n                      style={{ color: theme.colors.accent.success }}\n                    />\n                  )}\n                </ThemedButton>\n              ))}\n            </div>\n\n            <div className=\"mt-6 flex justify-end\">\n              <ThemedButton\n                onClick={() => {\n                  setIsOpen(false);\n                  onClose?.();\n                }}\n                variant=\"secondary\"\n                cyber3D={currentTheme === 'cyber'}\n              >\n                Close\n              </ThemedButton>\n            </div>\n          </div>\n        </ThemedCard>\n      </div>\n    );\n  }\n\n  // Compact dropdown version\n  return (\n    <div className=\"relative\">\n      <ThemedButton\n        onClick={toggleOpen}\n        variant=\"secondary\"\n        size=\"sm\"\n        cyber3D={currentTheme === 'cyber'}\n        className=\"flex items-center space-x-2\"\n      >\n        <Globe className=\"h-4 w-4\" />\n        <span className=\"text-lg\">{SUPPORTED_LANGUAGES[currentLanguage].flag}</span>\n        <span className=\"hidden sm:inline\">{SUPPORTED_LANGUAGES[currentLanguage].nativeName}</span>\n      </ThemedButton>\n\n      {isOpen && (\n        <div className=\"absolute top-full right-0 mt-2 z-50\">\n          <ThemedCard \n            className=\"w-80 max-h-96 overflow-y-auto\"\n            hover3D={currentTheme === 'cyber'}\n            glow={currentTheme !== 'light'}\n          >\n            <div className=\"p-4\">\n              <div className=\"grid grid-cols-1 gap-2\">\n                {Object.values(SUPPORTED_LANGUAGES).map((lang) => (\n                  <button\n                    key={lang.code}\n                    onClick={() => handleLanguageChange(lang.code)}\n                    className={`p-3 rounded-lg flex items-center justify-between transition-all duration-200 ${\n                      currentLanguage === lang.code \n                        ? 'ring-2' \n                        : 'hover:scale-105'\n                    }`}\n                    style={{\n                      backgroundColor: currentLanguage === lang.code \n                        ? `${theme.colors.accent.primary}20` \n                        : 'transparent',\n                      ringColor: currentLanguage === lang.code ? theme.colors.accent.primary : 'transparent'\n                    }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-xl\">{lang.flag}</span>\n                      <div className=\"text-left\">\n                        <div \n                          className=\"font-medium text-sm\"\n                          style={{ color: theme.colors.text.primary }}\n                        >\n                          {lang.nativeName}\n                        </div>\n                        <div \n                          className=\"text-xs\"\n                          style={{ color: theme.colors.text.secondary }}\n                        >\n                          {lang.name}\n                        </div>\n                      </div>\n                    </div>\n                    {currentLanguage === lang.code && (\n                      <Check \n                        className=\"h-4 w-4\" \n                        style={{ color: theme.colors.accent.success }}\n                      />\n                    )}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </ThemedCard>\n        </div>\n      )}\n\n      {/* Click outside to close */}\n      {isOpen && (\n        <div \n          className=\"fixed inset-0 z-40\" \n          onClick={() => setIsOpen(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAae,SAAS,iBAAiB,EAAE,YAAY,KAAK,EAAE,OAAO,EAAyB;IAC5F,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAClE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,UAAU;QACV;IACF;IAEA,MAAM,aAAa;QACjB,UAAU,CAAC;IACb;IAEA,IAAI,aAAa,QAAQ;QACvB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,qIAAA,CAAA,aAAU;gBACT,WAAU;gBACV,SAAS,iBAAiB;gBAC1B,MAAM,iBAAiB;0BAEvB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,iBAAiB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;gDACnD,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;4CACpC;sDAEA,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDACC,WAAW,CAAC,sBAAsB,EAChC,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;oDACF,OAAO;wDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;oDAAC;8DAC3C;;;;;;8DAGD,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;oDAAC;8DAC7C;;;;;;;;;;;;;;;;;;gCAKJ,+BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CACN,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;4CAAC;;;;;;sDAE9C,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;4CAAC;sDAC7C;;;;;;;;;;;;;;;;;;sCAOP,8OAAC;4BAAI,WAAU;sCACZ,OAAO,MAAM,CAAC,mIAAA,CAAA,sBAAmB,EAAE,GAAG,CAAC,CAAC,qBACvC,8OAAC,qIAAA,CAAA,eAAY;oCAEX,SAAS,IAAM,qBAAqB,KAAK,IAAI;oCAC7C,SAAS,oBAAoB,KAAK,IAAI,GAAG,YAAY;oCACrD,SAAS,iBAAiB;oCAC1B,WAAW,CAAC,4EAA4E,EACtF,oBAAoB,KAAK,IAAI,GACzB,yBACA,mBACJ;oCACF,OAAO;wCACL,WAAW,oBAAoB,KAAK,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG;wCACzE,iBAAiB,MAAM,MAAM,CAAC,EAAE,CAAC,IAAI;oCACvC;;sDAEA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAY,KAAK,IAAI;;;;;;8DACrC,8OAAC;;sEACC,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;4DAAC;sEAEzC,KAAK,UAAU;;;;;;sEAElB,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;4DAAC;sEAE3C,KAAK,IAAI;;;;;;;;;;;;;;;;;;wCAIf,oBAAoB,KAAK,IAAI,kBAC5B,8OAAC,oMAAA,CAAA,QAAK;4CACJ,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;4CAAC;;;;;;;mCAlC3C,KAAK,IAAI;;;;;;;;;;sCAyCpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qIAAA,CAAA,eAAY;gCACX,SAAS;oCACP,UAAU;oCACV;gCACF;gCACA,SAAQ;gCACR,SAAS,iBAAiB;0CAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,2BAA2B;IAC3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qIAAA,CAAA,eAAY;gBACX,SAAS;gBACT,SAAQ;gBACR,MAAK;gBACL,SAAS,iBAAiB;gBAC1B,WAAU;;kCAEV,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAK,WAAU;kCAAW,mIAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC,IAAI;;;;;;kCACpE,8OAAC;wBAAK,WAAU;kCAAoB,mIAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC,UAAU;;;;;;;;;;;;YAGpF,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,qIAAA,CAAA,aAAU;oBACT,WAAU;oBACV,SAAS,iBAAiB;oBAC1B,MAAM,iBAAiB;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,OAAO,MAAM,CAAC,mIAAA,CAAA,sBAAmB,EAAE,GAAG,CAAC,CAAC,qBACvC,8OAAC;oCAEC,SAAS,IAAM,qBAAqB,KAAK,IAAI;oCAC7C,WAAW,CAAC,6EAA6E,EACvF,oBAAoB,KAAK,IAAI,GACzB,WACA,mBACJ;oCACF,OAAO;wCACL,iBAAiB,oBAAoB,KAAK,IAAI,GAC1C,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAClC;wCACJ,WAAW,oBAAoB,KAAK,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG;oCAC3E;;sDAEA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAW,KAAK,IAAI;;;;;;8DACpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;4DAAC;sEAEzC,KAAK,UAAU;;;;;;sEAElB,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;4DAAC;sEAE3C,KAAK,IAAI;;;;;;;;;;;;;;;;;;wCAIf,oBAAoB,KAAK,IAAI,kBAC5B,8OAAC,oMAAA,CAAA,QAAK;4CACJ,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;4CAAC;;;;;;;mCAlC3C,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;YA8C3B,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC", "debugId": null}}, {"offset": {"line": 5757, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Upload, Receipt, Plus, Trash2, BarChart3, Camera, Palette, Globe } from 'lucide-react';\nimport ReceiptUpload from '@/components/ReceiptUpload';\nimport Dashboard from '@/components/Dashboard';\nimport CameraCapture from '@/components/CameraCapture';\nimport ExportPanel from '@/components/ExportPanel';\nimport ThemeSwitcher from '@/components/ThemeSwitcher';\nimport LanguageSwitcher from '@/components/LanguageSwitcher';\nimport { ThemedCard, ThemedButton, ThemedInput } from '@/components/ThemeComponents';\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { useTranslate } from '@/contexts/LanguageContext';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\n// Sample data for demo\nconst sampleExpenses: Expense[] = [\n  {\n    id: '1',\n    merchant: 'Starbucks Coffee',\n    amount: 8.75,\n    date: new Date().toLocaleDateString(),\n    category: 'Food & Dining'\n  },\n  {\n    id: '2',\n    merchant: 'Shell Gas Station',\n    amount: 41.08,\n    date: new Date(Date.now() - 86400000).toLocaleDateString(), // Yesterday\n    category: 'Transportation'\n  },\n  {\n    id: '3',\n    merchant: 'Walmart Supercenter',\n    amount: 11.63,\n    date: new Date(Date.now() - 172800000).toLocaleDateString(), // 2 days ago\n    category: 'Shopping'\n  }\n];\n\nexport default function Home() {\n  const [expenses, setExpenses] = useState<Expense[]>([]);\n  const [showUpload, setShowUpload] = useState(false);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showCamera, setShowCamera] = useState(false);\n  const [showThemeSwitcher, setShowThemeSwitcher] = useState(false);\n  const [showLanguageSwitcher, setShowLanguageSwitcher] = useState(false);\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const { currentTheme, theme } = useTheme();\n  const t = useTranslate;\n\n  // Load expenses from localStorage on mount\n  useEffect(() => {\n    const saved = localStorage.getItem('expenses');\n    if (saved) {\n      setExpenses(JSON.parse(saved));\n    } else {\n      // Use sample data if no saved data\n      setExpenses(sampleExpenses);\n    }\n  }, []);\n\n  // Save expenses to localStorage whenever expenses change\n  useEffect(() => {\n    if (expenses.length > 0) {\n      localStorage.setItem('expenses', JSON.stringify(expenses));\n    }\n  }, [expenses]);\n\n  const handleReceiptData = (data: any) => {\n    const newExpense: Expense = {\n      id: Date.now().toString(),\n      merchant: data.merchant,\n      amount: data.amount,\n      date: data.date ? data.date.toLocaleDateString() : new Date().toLocaleDateString(),\n      category: data.suggestedCategory,\n    };\n    setExpenses(prev => [newExpense, ...prev]);\n  };\n\n  const handleAddExpense = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const formData = new FormData(e.currentTarget);\n    const newExpense: Expense = {\n      id: Date.now().toString(),\n      merchant: formData.get('merchant') as string,\n      amount: parseFloat(formData.get('amount') as string),\n      date: formData.get('date') as string,\n      category: formData.get('category') as string,\n    };\n    setExpenses(prev => [newExpense, ...prev]);\n    setShowAddForm(false);\n    e.currentTarget.reset();\n  };\n\n  const deleteExpense = (id: string) => {\n    setExpenses(prev => prev.filter(expense => expense.id !== id));\n  };\n\n  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);\n\n  return (\n    <div\n      className=\"min-h-screen transition-all duration-300\"\n      style={{ backgroundColor: theme.colors.bg.secondary }}\n    >\n      {/* Header */}\n      <header\n        className=\"shadow-sm border-b transition-all duration-300\"\n        style={{\n          backgroundColor: theme.colors.bg.card,\n          borderColor: theme.colors.border.primary\n        }}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Receipt\n                className=\"h-8 w-8 transition-colors duration-300\"\n                style={{ color: theme.colors.accent.primary }}\n              />\n              <h1\n                className={`ml-2 text-xl font-bold transition-colors duration-300 ${\n                  currentTheme === 'cyber' ? 'text-cyber' :\n                  currentTheme === 'neon' ? 'text-neon' :\n                  currentTheme === 'matrix' ? 'text-matrix' : ''\n                }`}\n                style={{ color: theme.colors.text.primary }}\n              >\n                {t('Smart Expense Tracker')}\n              </h1>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"hidden md:flex space-x-8\">\n              <ThemedButton\n                onClick={() => setActiveTab('dashboard')}\n                variant={activeTab === 'dashboard' ? 'primary' : 'secondary'}\n                size=\"sm\"\n                cyber3D={currentTheme === 'cyber'}\n                className=\"flex items-center space-x-2\"\n              >\n                <BarChart3 className=\"h-4 w-4\" />\n                <span>{t('Dashboard')}</span>\n              </ThemedButton>\n              <ThemedButton\n                onClick={() => setActiveTab('expenses')}\n                variant={activeTab === 'expenses' ? 'primary' : 'secondary'}\n                size=\"sm\"\n                cyber3D={currentTheme === 'cyber'}\n                className=\"flex items-center space-x-2\"\n              >\n                <Receipt className=\"h-4 w-4\" />\n                <span>{t('Expenses')}</span>\n              </ThemedButton>\n            </nav>\n            <div className=\"flex space-x-3\">\n              <ThemedButton\n                onClick={() => setShowCamera(true)}\n                variant=\"primary\"\n                size=\"sm\"\n                cyber3D={currentTheme === 'cyber'}\n                className=\"flex items-center space-x-2\"\n              >\n                <Camera className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">{t('Camera')}</span>\n              </ThemedButton>\n              <ThemedButton\n                onClick={() => setShowUpload(true)}\n                variant=\"secondary\"\n                size=\"sm\"\n                cyber3D={currentTheme === 'cyber'}\n                className=\"flex items-center space-x-2\"\n              >\n                <Upload className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">{t('Upload')}</span>\n              </ThemedButton>\n              <ThemedButton\n                onClick={() => setShowAddForm(true)}\n                variant=\"success\"\n                size=\"sm\"\n                cyber3D={currentTheme === 'cyber'}\n                className=\"flex items-center space-x-2\"\n              >\n                <Plus className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">{t('Add')}</span>\n              </ThemedButton>\n              <ThemedButton\n                onClick={() => {\n                  // Demo: Add a random sample expense\n                  const samples = [\n                    { merchant: 'McDonald\\'s', amount: 12.45, category: 'Food & Dining' },\n                    { merchant: 'Target', amount: 67.89, category: 'Shopping' },\n                    { merchant: 'Uber', amount: 15.30, category: 'Transportation' },\n                    { merchant: 'Netflix', amount: 15.99, category: 'Entertainment' },\n                    { merchant: 'Starbucks', amount: 8.75, category: 'Food & Dining' },\n                    { merchant: 'Shell', amount: 41.08, category: 'Transportation' },\n                    { merchant: 'Amazon', amount: 29.99, category: 'Shopping' },\n                    { merchant: 'Gym Membership', amount: 45.00, category: 'Healthcare' }\n                  ];\n                  const sample = samples[Math.floor(Math.random() * samples.length)];\n                  const newExpense: Expense = {\n                    id: Date.now().toString(),\n                    merchant: sample.merchant,\n                    amount: sample.amount,\n                    date: new Date().toLocaleDateString(),\n                    category: sample.category,\n                  };\n                  setExpenses(prev => [newExpense, ...prev]);\n                }}\n                variant=\"warning\"\n                size=\"sm\"\n                cyber3D={currentTheme === 'cyber'}\n                className=\"flex items-center space-x-2\"\n              >\n                <Receipt className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">{t('Demo')}</span>\n              </ThemedButton>\n              <ThemedButton\n                onClick={() => setShowThemeSwitcher(true)}\n                variant=\"secondary\"\n                size=\"sm\"\n                cyber3D={currentTheme === 'cyber'}\n                className=\"flex items-center space-x-2\"\n              >\n                <Palette className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">{t('Themes')}</span>\n              </ThemedButton>\n              <LanguageSwitcher />\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {activeTab === 'dashboard' && <Dashboard expenses={expenses} />}\n\n        {activeTab === 'expenses' && (\n          <div className=\"space-y-6\">\n            {/* Export Panel */}\n            <ExportPanel expenses={expenses} />\n\n            {/* Quick Summary */}\n            <ThemedCard className=\"p-6\" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n              <h2\n                className=\"text-lg font-medium mb-4\"\n                style={{ color: theme.colors.text.primary }}\n              >\n                {t('Quick Summary')}\n              </h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"text-center\">\n                  <p\n                    className={`text-2xl font-bold ${\n                      currentTheme === 'cyber' ? 'text-cyber' :\n                      currentTheme === 'neon' ? 'text-neon' :\n                      currentTheme === 'matrix' ? 'text-matrix' : ''\n                    }`}\n                    style={{ color: theme.colors.text.primary }}\n                  >\n                    ${totalExpenses.toFixed(2)}\n                  </p>\n                  <p\n                    className=\"text-sm\"\n                    style={{ color: theme.colors.text.secondary }}\n                  >\n                    {t('Total Expenses')}\n                  </p>\n                </div>\n                <div className=\"text-center\">\n                  <p\n                    className={`text-2xl font-bold ${\n                      currentTheme === 'cyber' ? 'text-cyber' :\n                      currentTheme === 'neon' ? 'text-neon' :\n                      currentTheme === 'matrix' ? 'text-matrix' : ''\n                    }`}\n                    style={{ color: theme.colors.text.primary }}\n                  >\n                    {expenses.length}\n                  </p>\n                  <p\n                    className=\"text-sm\"\n                    style={{ color: theme.colors.text.secondary }}\n                  >\n                    {t('Transactions')}\n                  </p>\n                </div>\n                <div className=\"text-center\">\n                  <p\n                    className={`text-2xl font-bold ${\n                      currentTheme === 'cyber' ? 'text-cyber' :\n                      currentTheme === 'neon' ? 'text-neon' :\n                      currentTheme === 'matrix' ? 'text-matrix' : ''\n                    }`}\n                    style={{ color: theme.colors.text.primary }}\n                  >\n                    ${expenses.length > 0 ? (totalExpenses / expenses.length).toFixed(2) : '0.00'}\n                  </p>\n                  <p\n                    className=\"text-sm\"\n                    style={{ color: theme.colors.text.secondary }}\n                  >\n                    {t('Average')}\n                  </p>\n                </div>\n              </div>\n            </ThemedCard>\n\n            {/* Expenses List */}\n            <ThemedCard hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>\n              <div\n                className=\"px-6 py-4 border-b flex justify-between items-center\"\n                style={{ borderColor: theme.colors.border.primary }}\n              >\n                <h3\n                  className=\"text-lg font-medium\"\n                  style={{ color: theme.colors.text.primary }}\n                >\n                  All Expenses\n                </h3>\n                {expenses.length > 0 && (\n                  <ThemedButton\n                    onClick={() => {\n                      setExpenses([]);\n                      localStorage.removeItem('expenses');\n                    }}\n                    variant=\"error\"\n                    size=\"sm\"\n                    cyber3D={currentTheme === 'cyber'}\n                  >\n                    Clear All\n                  </ThemedButton>\n                )}\n              </div>\n              <div\n                className=\"divide-y\"\n                style={{ borderColor: theme.colors.border.primary }}\n              >\n                {expenses.length === 0 ? (\n                  <div className=\"p-8 text-center\">\n                    <Receipt\n                      className=\"h-12 w-12 mx-auto mb-4\"\n                      style={{ color: theme.colors.text.tertiary }}\n                    />\n                    <p style={{ color: theme.colors.text.secondary }}>\n                      No expenses yet. Start by scanning a receipt or adding an expense manually!\n                    </p>\n                  </div>\n                ) : (\n                  expenses.map((expense) => (\n                    <div\n                      key={expense.id}\n                      className={`p-6 flex items-center justify-between transition-colors duration-200 ${\n                        currentTheme === 'cyber' ? 'hover:bg-green-400/5' :\n                        currentTheme === 'neon' ? 'hover:bg-pink-500/5' :\n                        currentTheme === 'matrix' ? 'hover:bg-green-500/5' :\n                        currentTheme === 'dark' ? 'hover:bg-gray-700/50' :\n                        'hover:bg-gray-50'\n                      }`}\n                    >\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center justify-between\">\n                          <h4\n                            className=\"text-sm font-medium\"\n                            style={{ color: theme.colors.text.primary }}\n                          >\n                            {expense.merchant}\n                          </h4>\n                          <p\n                            className={`text-lg font-semibold ${\n                              currentTheme === 'cyber' ? 'text-cyber' :\n                              currentTheme === 'neon' ? 'text-neon' :\n                              currentTheme === 'matrix' ? 'text-matrix' : ''\n                            }`}\n                            style={{ color: theme.colors.text.primary }}\n                          >\n                            ${expense.amount.toFixed(2)}\n                          </p>\n                        </div>\n                        <div className=\"mt-1 flex items-center space-x-4 text-sm\">\n                          <span\n                            className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                            style={{\n                              backgroundColor: `${theme.colors.accent.primary}20`,\n                              color: theme.colors.accent.primary\n                            }}\n                          >\n                            {expense.category}\n                          </span>\n                          <span style={{ color: theme.colors.text.tertiary }}>•</span>\n                          <span style={{ color: theme.colors.text.secondary }}>{expense.date}</span>\n                        </div>\n                      </div>\n                      <ThemedButton\n                        onClick={() => deleteExpense(expense.id)}\n                        variant=\"error\"\n                        size=\"sm\"\n                        cyber3D={currentTheme === 'cyber'}\n                        className=\"ml-4 !p-2\"\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                      </ThemedButton>\n                    </div>\n                  ))\n                )}\n              </div>\n            </ThemedCard>\n          </div>\n        )}\n      </main>\n\n      {/* Camera Capture */}\n      {showCamera && (\n        <CameraCapture\n          onCapture={(file) => {\n            setShowCamera(false);\n            // Process the captured file\n            const reader = new FileReader();\n            reader.onload = () => {\n              // For demo, we'll simulate processing the captured image\n              const mockData = {\n                merchant: 'Camera Captured Receipt',\n                amount: Math.round((Math.random() * 50 + 10) * 100) / 100,\n                date: new Date(),\n                suggestedCategory: 'Food & Dining'\n              };\n              handleReceiptData(mockData);\n            };\n            reader.readAsDataURL(file);\n          }}\n          onClose={() => setShowCamera(false)}\n        />\n      )}\n\n      {/* Receipt Upload Modal */}\n      {showUpload && (\n        <ReceiptUpload\n          onDataExtracted={handleReceiptData}\n          onClose={() => setShowUpload(false)}\n        />\n      )}\n\n      {/* Theme Switcher Modal */}\n      {showThemeSwitcher && (\n        <ThemeSwitcher\n          showModal={true}\n          onClose={() => setShowThemeSwitcher(false)}\n        />\n      )}\n\n      {/* Language Switcher Modal */}\n      {showLanguageSwitcher && (\n        <LanguageSwitcher\n          showModal={true}\n          onClose={() => setShowLanguageSwitcher(false)}\n        />\n      )}\n\n      {/* Add Expense Modal */}\n      {showAddForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n          <ThemedCard className=\"max-w-md w-full p-6\" hover3D={currentTheme === 'cyber'}>\n            <h2\n              className={`text-xl font-semibold mb-4 ${\n                currentTheme === 'cyber' ? 'text-cyber' :\n                currentTheme === 'neon' ? 'text-neon' :\n                currentTheme === 'matrix' ? 'text-matrix' : ''\n              }`}\n              style={{ color: theme.colors.text.primary }}\n            >\n              Add Expense\n            </h2>\n            <form onSubmit={handleAddExpense} className=\"space-y-4\">\n              <div>\n                <label\n                  className=\"block text-sm font-medium mb-1\"\n                  style={{ color: theme.colors.text.secondary }}\n                >\n                  Merchant\n                </label>\n                <ThemedInput\n                  type=\"text\"\n                  name=\"merchant\"\n                  placeholder=\"Store name\"\n                />\n              </div>\n              <div>\n                <label\n                  className=\"block text-sm font-medium mb-1\"\n                  style={{ color: theme.colors.text.secondary }}\n                >\n                  Amount\n                </label>\n                <ThemedInput\n                  type=\"number\"\n                  name=\"amount\"\n                  placeholder=\"0.00\"\n                />\n              </div>\n              <div>\n                <label\n                  className=\"block text-sm font-medium mb-1\"\n                  style={{ color: theme.colors.text.secondary }}\n                >\n                  Date\n                </label>\n                <ThemedInput\n                  type=\"date\"\n                  name=\"date\"\n                  value={new Date().toISOString().split('T')[0]}\n                />\n              </div>\n              <div>\n                <label\n                  className=\"block text-sm font-medium mb-1\"\n                  style={{ color: theme.colors.text.secondary }}\n                >\n                  Category\n                </label>\n                <select\n                  name=\"category\"\n                  required\n                  className={`w-full px-4 py-2 rounded-lg transition-all duration-300 ease-out focus:outline-none focus:ring-2 ${\n                    currentTheme === 'cyber' ? 'bg-black/80 border-2 border-green-400/50 text-green-400 focus:border-green-400 focus:ring-green-400/30' :\n                    currentTheme === 'neon' ? 'bg-gray-900/80 border-2 border-pink-500/50 text-pink-500 focus:border-pink-500 focus:ring-pink-500/30' :\n                    currentTheme === 'matrix' ? 'bg-black/80 border-2 border-green-500/50 text-green-500 focus:border-green-500 focus:ring-green-500/30' :\n                    currentTheme === 'dark' ? 'bg-gray-800 border border-gray-600 text-gray-100 focus:border-blue-400 focus:ring-blue-400/20' :\n                    'bg-white border border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500/20'\n                  }`}\n                >\n                  <option value=\"Food & Dining\">Food & Dining</option>\n                  <option value=\"Transportation\">Transportation</option>\n                  <option value=\"Shopping\">Shopping</option>\n                  <option value=\"Entertainment\">Entertainment</option>\n                  <option value=\"Bills & Utilities\">Bills & Utilities</option>\n                  <option value=\"Healthcare\">Healthcare</option>\n                  <option value=\"Education\">Education</option>\n                  <option value=\"Travel\">Travel</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div className=\"flex space-x-3 pt-4\">\n                <ThemedButton\n                  type=\"submit\"\n                  variant=\"success\"\n                  cyber3D={currentTheme === 'cyber'}\n                  className=\"flex-1\"\n                >\n                  Add Expense\n                </ThemedButton>\n                <ThemedButton\n                  type=\"button\"\n                  onClick={() => setShowAddForm(false)}\n                  variant=\"secondary\"\n                  cyber3D={currentTheme === 'cyber'}\n                  className=\"flex-1\"\n                >\n                  Cancel\n                </ThemedButton>\n              </div>\n            </form>\n          </ThemedCard>\n        </div>\n      )}\n    </div>\n  );\n}\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAsBA,uBAAuB;AACvB,MAAM,iBAA4B;IAChC;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,MAAM,IAAI,OAAO,kBAAkB;QACnC,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,kBAAkB;QACxD,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,kBAAkB;QACzD,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IACvC,MAAM,IAAI,mIAAA,CAAA,eAAY;IAEtB,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,YAAY,KAAK,KAAK,CAAC;QACzB,OAAO;YACL,mCAAmC;YACnC,YAAY;QACd;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;QAClD;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,MAAM;YACnB,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,kBAAkB,KAAK,IAAI,OAAO,kBAAkB;YAChF,UAAU,KAAK,iBAAiB;QAClC;QACA,YAAY,CAAA,OAAQ;gBAAC;mBAAe;aAAK;IAC3C;IAEA,MAAM,mBAAmB,CAAC;QACxB,EAAE,cAAc;QAChB,MAAM,WAAW,IAAI,SAAS,EAAE,aAAa;QAC7C,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,SAAS,GAAG,CAAC;YACvB,QAAQ,WAAW,SAAS,GAAG,CAAC;YAChC,MAAM,SAAS,GAAG,CAAC;YACnB,UAAU,SAAS,GAAG,CAAC;QACzB;QACA,YAAY,CAAA,OAAQ;gBAAC;mBAAe;aAAK;QACzC,eAAe;QACf,EAAE,aAAa,CAAC,KAAK;IACvB;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC5D;IAEA,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;IAE9E,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,iBAAiB,MAAM,MAAM,CAAC,EAAE,CAAC,SAAS;QAAC;;0BAGpD,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,MAAM,MAAM,CAAC,EAAE,CAAC,IAAI;oBACrC,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;gBAC1C;0BAEA,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCACN,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wCAAC;;;;;;kDAE9C,8OAAC;wCACC,WAAW,CAAC,sDAAsD,EAChE,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;wCACF,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wCAAC;kDAEzC,EAAE;;;;;;;;;;;;0CAKP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qIAAA,CAAA,eAAY;wCACX,SAAS,IAAM,aAAa;wCAC5B,SAAS,cAAc,cAAc,YAAY;wCACjD,MAAK;wCACL,SAAS,iBAAiB;wCAC1B,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAM,EAAE;;;;;;;;;;;;kDAEX,8OAAC,qIAAA,CAAA,eAAY;wCACX,SAAS,IAAM,aAAa;wCAC5B,SAAS,cAAc,aAAa,YAAY;wCAChD,MAAK;wCACL,SAAS,iBAAiB;wCAC1B,WAAU;;0DAEV,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;;0CAGb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qIAAA,CAAA,eAAY;wCACX,SAAS,IAAM,cAAc;wCAC7B,SAAQ;wCACR,MAAK;wCACL,SAAS,iBAAiB;wCAC1B,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAoB,EAAE;;;;;;;;;;;;kDAExC,8OAAC,qIAAA,CAAA,eAAY;wCACX,SAAS,IAAM,cAAc;wCAC7B,SAAQ;wCACR,MAAK;wCACL,SAAS,iBAAiB;wCAC1B,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAoB,EAAE;;;;;;;;;;;;kDAExC,8OAAC,qIAAA,CAAA,eAAY;wCACX,SAAS,IAAM,eAAe;wCAC9B,SAAQ;wCACR,MAAK;wCACL,SAAS,iBAAiB;wCAC1B,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAoB,EAAE;;;;;;;;;;;;kDAExC,8OAAC,qIAAA,CAAA,eAAY;wCACX,SAAS;4CACP,oCAAoC;4CACpC,MAAM,UAAU;gDACd;oDAAE,UAAU;oDAAe,QAAQ;oDAAO,UAAU;gDAAgB;gDACpE;oDAAE,UAAU;oDAAU,QAAQ;oDAAO,UAAU;gDAAW;gDAC1D;oDAAE,UAAU;oDAAQ,QAAQ;oDAAO,UAAU;gDAAiB;gDAC9D;oDAAE,UAAU;oDAAW,QAAQ;oDAAO,UAAU;gDAAgB;gDAChE;oDAAE,UAAU;oDAAa,QAAQ;oDAAM,UAAU;gDAAgB;gDACjE;oDAAE,UAAU;oDAAS,QAAQ;oDAAO,UAAU;gDAAiB;gDAC/D;oDAAE,UAAU;oDAAU,QAAQ;oDAAO,UAAU;gDAAW;gDAC1D;oDAAE,UAAU;oDAAkB,QAAQ;oDAAO,UAAU;gDAAa;6CACrE;4CACD,MAAM,SAAS,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;4CAClE,MAAM,aAAsB;gDAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;gDACvB,UAAU,OAAO,QAAQ;gDACzB,QAAQ,OAAO,MAAM;gDACrB,MAAM,IAAI,OAAO,kBAAkB;gDACnC,UAAU,OAAO,QAAQ;4CAC3B;4CACA,YAAY,CAAA,OAAQ;oDAAC;uDAAe;iDAAK;wCAC3C;wCACA,SAAQ;wCACR,MAAK;wCACL,SAAS,iBAAiB;wCAC1B,WAAU;;0DAEV,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAAoB,EAAE;;;;;;;;;;;;kDAExC,8OAAC,qIAAA,CAAA,eAAY;wCACX,SAAS,IAAM,qBAAqB;wCACpC,SAAQ;wCACR,MAAK;wCACL,SAAS,iBAAiB;wCAC1B,WAAU;;0DAEV,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAAoB,EAAE;;;;;;;;;;;;kDAExC,8OAAC,sIAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,8OAAC;gBAAK,WAAU;;oBACb,cAAc,6BAAe,8OAAC,+HAAA,CAAA,UAAS;wBAAC,UAAU;;;;;;oBAElD,cAAc,4BACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,iIAAA,CAAA,UAAW;gCAAC,UAAU;;;;;;0CAGvB,8OAAC,qIAAA,CAAA,aAAU;gCAAC,WAAU;gCAAM,SAAS,iBAAiB;gCAAS,MAAM,iBAAiB;;kDACpF,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wCAAC;kDAEzC,EAAE;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC,mBAAmB,EAC7B,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;wDACF,OAAO;4DAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wDAAC;;4DAC3C;4DACG,cAAc,OAAO,CAAC;;;;;;;kEAE1B,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;wDAAC;kEAE3C,EAAE;;;;;;;;;;;;0DAGP,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC,mBAAmB,EAC7B,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;wDACF,OAAO;4DAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wDAAC;kEAEzC,SAAS,MAAM;;;;;;kEAElB,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;wDAAC;kEAE3C,EAAE;;;;;;;;;;;;0DAGP,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC,mBAAmB,EAC7B,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;wDACF,OAAO;4DAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wDAAC;;4DAC3C;4DACG,SAAS,MAAM,GAAG,IAAI,CAAC,gBAAgB,SAAS,MAAM,EAAE,OAAO,CAAC,KAAK;;;;;;;kEAEzE,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;wDAAC;kEAE3C,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0CAOX,8OAAC,qIAAA,CAAA,aAAU;gCAAC,SAAS,iBAAiB;gCAAS,MAAM,iBAAiB;;kDACpE,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wCAAC;;0DAElD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;gDAAC;0DAC3C;;;;;;4CAGA,SAAS,MAAM,GAAG,mBACjB,8OAAC,qIAAA,CAAA,eAAY;gDACX,SAAS;oDACP,YAAY,EAAE;oDACd,aAAa,UAAU,CAAC;gDAC1B;gDACA,SAAQ;gDACR,MAAK;gDACL,SAAS,iBAAiB;0DAC3B;;;;;;;;;;;;kDAKL,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,aAAa,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wCAAC;kDAEjD,SAAS,MAAM,KAAK,kBACnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDACN,WAAU;oDACV,OAAO;wDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ;oDAAC;;;;;;8DAE7C,8OAAC;oDAAE,OAAO;wDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;oDAAC;8DAAG;;;;;;;;;;;mDAKpD,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC;gDAEC,WAAW,CAAC,qEAAqE,EAC/E,iBAAiB,UAAU,yBAC3B,iBAAiB,SAAS,wBAC1B,iBAAiB,WAAW,yBAC5B,iBAAiB,SAAS,yBAC1B,oBACA;;kEAEF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wEAAC;kFAEzC,QAAQ,QAAQ;;;;;;kFAEnB,8OAAC;wEACC,WAAW,CAAC,sBAAsB,EAChC,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;wEACF,OAAO;4EAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;wEAAC;;4EAC3C;4EACG,QAAQ,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;0EAG7B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,WAAU;wEACV,OAAO;4EACL,iBAAiB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;4EACnD,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;wEACpC;kFAEC,QAAQ,QAAQ;;;;;;kFAEnB,8OAAC;wEAAK,OAAO;4EAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ;wEAAC;kFAAG;;;;;;kFACpD,8OAAC;wEAAK,OAAO;4EAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;wEAAC;kFAAI,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kEAGtE,8OAAC,qIAAA,CAAA,eAAY;wDACX,SAAS,IAAM,cAAc,QAAQ,EAAE;wDACvC,SAAQ;wDACR,MAAK;wDACL,SAAS,iBAAiB;wDAC1B,WAAU;kEAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;+CAjDf,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA6D9B,4BACC,8OAAC,mIAAA,CAAA,UAAa;gBACZ,WAAW,CAAC;oBACV,cAAc;oBACd,4BAA4B;oBAC5B,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG;wBACd,yDAAyD;wBACzD,MAAM,WAAW;4BACf,UAAU;4BACV,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI,OAAO;4BACtD,MAAM,IAAI;4BACV,mBAAmB;wBACrB;wBACA,kBAAkB;oBACpB;oBACA,OAAO,aAAa,CAAC;gBACvB;gBACA,SAAS,IAAM,cAAc;;;;;;YAKhC,4BACC,8OAAC,mIAAA,CAAA,UAAa;gBACZ,iBAAiB;gBACjB,SAAS,IAAM,cAAc;;;;;;YAKhC,mCACC,8OAAC,mIAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS,IAAM,qBAAqB;;;;;;YAKvC,sCACC,8OAAC,sIAAA,CAAA,UAAgB;gBACf,WAAW;gBACX,SAAS,IAAM,wBAAwB;;;;;;YAK1C,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,qIAAA,CAAA,aAAU;oBAAC,WAAU;oBAAsB,SAAS,iBAAiB;;sCACpE,8OAAC;4BACC,WAAW,CAAC,2BAA2B,EACrC,iBAAiB,UAAU,eAC3B,iBAAiB,SAAS,cAC1B,iBAAiB,WAAW,gBAAgB,IAC5C;4BACF,OAAO;gCAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;4BAAC;sCAC3C;;;;;;sCAGD,8OAAC;4BAAK,UAAU;4BAAkB,WAAU;;8CAC1C,8OAAC;;sDACC,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;4CAAC;sDAC7C;;;;;;sDAGD,8OAAC,qIAAA,CAAA,cAAW;4CACV,MAAK;4CACL,MAAK;4CACL,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;4CAAC;sDAC7C;;;;;;sDAGD,8OAAC,qIAAA,CAAA,cAAW;4CACV,MAAK;4CACL,MAAK;4CACL,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;4CAAC;sDAC7C;;;;;;sDAGD,8OAAC,qIAAA,CAAA,cAAW;4CACV,MAAK;4CACL,MAAK;4CACL,OAAO,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;8CAGjD,8OAAC;;sDACC,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS;4CAAC;sDAC7C;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,QAAQ;4CACR,WAAW,CAAC,iGAAiG,EAC3G,iBAAiB,UAAU,2GAC3B,iBAAiB,SAAS,0GAC1B,iBAAiB,WAAW,2GAC5B,iBAAiB,SAAS,kGAC1B,8FACA;;8DAEF,8OAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAO,OAAM;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAO,OAAM;8DAAoB;;;;;;8DAClC,8OAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAG1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,qIAAA,CAAA,eAAY;4CACX,MAAK;4CACL,SAAQ;4CACR,SAAS,iBAAiB;4CAC1B,WAAU;sDACX;;;;;;sDAGD,8OAAC,qIAAA,CAAA,eAAY;4CACX,MAAK;4CACL,SAAS,IAAM,eAAe;4CAC9B,SAAQ;4CACR,SAAS,iBAAiB;4CAC1B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}