{"version": 3, "sources": [], "sections": [{"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/lib/ocr.ts"], "sourcesContent": ["import Tesseract from 'tesseract.js';\n\nexport interface OCRResult {\n  text: string;\n  confidence: number;\n  words: Array<{\n    text: string;\n    confidence: number;\n    bbox: {\n      x0: number;\n      y0: number;\n      x1: number;\n      y1: number;\n    };\n  }>;\n}\n\nexport interface ProcessedImage {\n  canvas: HTMLCanvasElement;\n  dataUrl: string;\n}\n\nexport interface ImageProcessingOptions {\n  contrast: number;\n  brightness: number;\n  sharpen: boolean;\n  denoise: boolean;\n  autoRotate: boolean;\n}\n\n/**\n * Advanced image preprocessing for optimal OCR results\n */\nexport const preprocessImage = (\n  file: File,\n  options: ImageProcessingOptions = {\n    contrast: 1.5,\n    brightness: 1.1,\n    sharpen: true,\n    denoise: true,\n    autoRotate: true\n  }\n): Promise<ProcessedImage> => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n\n    if (!ctx) {\n      reject(new Error('Could not get canvas context'));\n      return;\n    }\n\n    img.onload = () => {\n      // Auto-detect and correct rotation\n      let { width, height } = img;\n      let rotation = 0;\n\n      if (options.autoRotate) {\n        // Simple heuristic: if width > height significantly, might need rotation\n        if (width > height * 1.5) {\n          rotation = 0; // Landscape, likely correct\n        } else if (height > width * 1.5) {\n          rotation = 90; // Portrait, might need rotation for receipt\n        }\n      }\n\n      // Set canvas size accounting for rotation\n      if (rotation === 90 || rotation === 270) {\n        canvas.width = height;\n        canvas.height = width;\n      } else {\n        canvas.width = width;\n        canvas.height = height;\n      }\n\n      // Apply rotation if needed\n      if (rotation !== 0) {\n        ctx.translate(canvas.width / 2, canvas.height / 2);\n        ctx.rotate((rotation * Math.PI) / 180);\n        ctx.translate(-width / 2, -height / 2);\n      }\n\n      // Draw original image\n      ctx.drawImage(img, 0, 0);\n\n      // Get image data for processing\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // Advanced image processing\n      for (let i = 0; i < data.length; i += 4) {\n        // Convert to grayscale using luminance formula\n        let gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);\n\n        // Apply brightness\n        gray = Math.min(255, Math.max(0, gray * options.brightness));\n\n        // Apply contrast\n        const factor = (259 * (options.contrast * 255 + 255)) / (255 * (259 - options.contrast * 255));\n        gray = Math.min(255, Math.max(0, factor * (gray - 128) + 128));\n\n        // Apply to all channels\n        data[i] = gray;     // Red\n        data[i + 1] = gray; // Green\n        data[i + 2] = gray; // Blue\n        // Alpha channel (data[i + 3]) remains unchanged\n      }\n\n      // Apply sharpening filter if enabled\n      if (options.sharpen) {\n        applySharpenFilter(data, canvas.width, canvas.height);\n      }\n\n      // Apply denoising if enabled\n      if (options.denoise) {\n        applyDenoiseFilter(data, canvas.width, canvas.height);\n      }\n\n      // Put processed image data back\n      ctx.putImageData(imageData, 0, 0);\n\n      resolve({\n        canvas,\n        dataUrl: canvas.toDataURL('image/png')\n      });\n    };\n\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n\n    // Create object URL from file\n    const objectUrl = URL.createObjectURL(file);\n    img.src = objectUrl;\n  });\n};\n\n/**\n * Apply sharpening filter to image data\n */\nconst applySharpenFilter = (data: Uint8ClampedArray, width: number, height: number) => {\n  const kernel = [\n    0, -1, 0,\n    -1, 5, -1,\n    0, -1, 0\n  ];\n\n  const output = new Uint8ClampedArray(data);\n\n  for (let y = 1; y < height - 1; y++) {\n    for (let x = 1; x < width - 1; x++) {\n      let sum = 0;\n      for (let ky = -1; ky <= 1; ky++) {\n        for (let kx = -1; kx <= 1; kx++) {\n          const idx = ((y + ky) * width + (x + kx)) * 4;\n          sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];\n        }\n      }\n      const idx = (y * width + x) * 4;\n      const value = Math.min(255, Math.max(0, sum));\n      output[idx] = value;\n      output[idx + 1] = value;\n      output[idx + 2] = value;\n    }\n  }\n\n  data.set(output);\n};\n\n/**\n * Apply simple denoising filter\n */\nconst applyDenoiseFilter = (data: Uint8ClampedArray, width: number, height: number) => {\n  const output = new Uint8ClampedArray(data);\n\n  for (let y = 1; y < height - 1; y++) {\n    for (let x = 1; x < width - 1; x++) {\n      let sum = 0;\n      let count = 0;\n\n      // 3x3 median filter\n      for (let ky = -1; ky <= 1; ky++) {\n        for (let kx = -1; kx <= 1; kx++) {\n          const idx = ((y + ky) * width + (x + kx)) * 4;\n          sum += data[idx];\n          count++;\n        }\n      }\n\n      const idx = (y * width + x) * 4;\n      const value = Math.round(sum / count);\n      output[idx] = value;\n      output[idx + 1] = value;\n      output[idx + 2] = value;\n    }\n  }\n\n  data.set(output);\n};\n\n/**\n * Extract text from image using advanced Tesseract.js configuration\n */\nexport const extractTextFromImage = async (\n  imageSource: string | File | HTMLCanvasElement,\n  options?: {\n    language?: string;\n    logger?: (info: any) => void;\n    psm?: number;\n    oem?: number;\n  }\n): Promise<OCRResult> => {\n  try {\n    const {\n      language = 'eng',\n      logger,\n      psm = 6, // Assume a single uniform block of text\n      oem = 3  // Default OCR Engine Mode\n    } = options || {};\n\n    // Advanced Tesseract configuration for receipts\n    const result = await Tesseract.recognize(\n      imageSource,\n      language,\n      {\n        logger: logger || (() => {}),\n        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/$-:()# \\n\\t',\n        tessedit_pageseg_mode: psm,\n        tessedit_ocr_engine_mode: oem,\n        preserve_interword_spaces: '1',\n        user_defined_dpi: '300',\n        // Receipt-specific optimizations\n        textord_heavy_nr: '1',\n        textord_noise_normratio: '2',\n        textord_noise_sizelimit: '0.5',\n        // Improve number recognition\n        classify_enable_learning: '0',\n        classify_enable_adaptive_matcher: '0',\n      }\n    );\n\n    // Extract word-level data with confidence scores\n    const words = result.data.words.map(word => ({\n      text: word.text,\n      confidence: word.confidence,\n      bbox: {\n        x0: word.bbox.x0,\n        y0: word.bbox.y0,\n        x1: word.bbox.x1,\n        y1: word.bbox.y1,\n      }\n    }));\n\n    return {\n      text: result.data.text,\n      confidence: result.data.confidence,\n      words\n    };\n  } catch (error) {\n    console.error('OCR Error:', error);\n    throw new Error('Failed to extract text from image');\n  }\n};\n\n/**\n * Multi-pass OCR with different configurations for better accuracy\n */\nexport const extractTextMultiPass = async (\n  imageSource: string | File | HTMLCanvasElement,\n  onProgress?: (progress: number, pass: string) => void\n): Promise<OCRResult> => {\n  const passes = [\n    { name: 'Standard', psm: 6, oem: 3 },\n    { name: 'Single Block', psm: 8, oem: 3 },\n    { name: 'Single Line', psm: 7, oem: 3 },\n    { name: 'Sparse Text', psm: 11, oem: 3 }\n  ];\n\n  let bestResult: OCRResult | null = null;\n  let bestConfidence = 0;\n\n  for (let i = 0; i < passes.length; i++) {\n    const pass = passes[i];\n    onProgress?.(((i + 1) / passes.length) * 100, pass.name);\n\n    try {\n      const result = await extractTextFromImage(imageSource, {\n        psm: pass.psm,\n        oem: pass.oem,\n        logger: (info) => {\n          if (info.status === 'recognizing text') {\n            const passProgress = (i / passes.length) * 100;\n            const currentProgress = passProgress + (info.progress * (100 / passes.length));\n            onProgress?.(currentProgress, `${pass.name} - ${Math.round(info.progress * 100)}%`);\n          }\n        }\n      });\n\n      if (result.confidence > bestConfidence) {\n        bestResult = result;\n        bestConfidence = result.confidence;\n      }\n    } catch (error) {\n      console.warn(`OCR pass ${pass.name} failed:`, error);\n    }\n  }\n\n  if (!bestResult) {\n    throw new Error('All OCR passes failed');\n  }\n\n  return bestResult;\n};\n\n/**\n * Advanced receipt processing with multiple enhancement techniques\n */\nexport const processReceiptImage = async (\n  file: File,\n  onProgress?: (progress: number, status: string) => void\n): Promise<OCRResult> => {\n  try {\n    onProgress?.(5, 'Analyzing image...');\n\n    // Step 1: Preprocess image with optimal settings for receipts\n    const processedImage = await preprocessImage(file, {\n      contrast: 1.8,\n      brightness: 1.2,\n      sharpen: true,\n      denoise: true,\n      autoRotate: true\n    });\n\n    onProgress?.(20, 'Image enhanced, starting OCR...');\n\n    // Step 2: Multi-pass OCR for best results\n    const result = await extractTextMultiPass(\n      processedImage.canvas,\n      (progress, pass) => {\n        const overallProgress = 20 + (progress * 0.8);\n        onProgress?.(Math.round(overallProgress), `OCR Pass: ${pass}`);\n      }\n    );\n\n    onProgress?.(100, 'Processing complete!');\n    return result;\n  } catch (error) {\n    console.error('Receipt processing error:', error);\n    throw error;\n  }\n};\n\n/**\n * Process receipt with fallback strategies\n */\nexport const processReceiptWithFallback = async (\n  file: File,\n  onProgress?: (progress: number, status: string) => void\n): Promise<OCRResult> => {\n  const strategies = [\n    {\n      name: 'High Quality',\n      options: { contrast: 1.8, brightness: 1.2, sharpen: true, denoise: true, autoRotate: true }\n    },\n    {\n      name: 'High Contrast',\n      options: { contrast: 2.2, brightness: 1.0, sharpen: true, denoise: false, autoRotate: true }\n    },\n    {\n      name: 'Minimal Processing',\n      options: { contrast: 1.2, brightness: 1.1, sharpen: false, denoise: false, autoRotate: false }\n    }\n  ];\n\n  let bestResult: OCRResult | null = null;\n  let bestConfidence = 0;\n\n  for (let i = 0; i < strategies.length; i++) {\n    const strategy = strategies[i];\n\n    try {\n      onProgress?.(\n        (i / strategies.length) * 100,\n        `Trying ${strategy.name} processing...`\n      );\n\n      const processedImage = await preprocessImage(file, strategy.options);\n      const result = await extractTextFromImage(processedImage.canvas, {\n        logger: (info) => {\n          if (info.status === 'recognizing text') {\n            const strategyProgress = (i / strategies.length) * 100;\n            const currentProgress = strategyProgress + (info.progress * (100 / strategies.length));\n            onProgress?.(currentProgress, `${strategy.name}: ${Math.round(info.progress * 100)}%`);\n          }\n        }\n      });\n\n      if (result.confidence > bestConfidence) {\n        bestResult = result;\n        bestConfidence = result.confidence;\n      }\n\n      // If we get good confidence, use it\n      if (result.confidence > 80) {\n        break;\n      }\n    } catch (error) {\n      console.warn(`Strategy ${strategy.name} failed:`, error);\n    }\n  }\n\n  if (!bestResult) {\n    throw new Error('All processing strategies failed');\n  }\n\n  onProgress?.(100, `Best result: ${bestResult.confidence.toFixed(1)}% confidence`);\n  return bestResult;\n};\n\n/**\n * Validate image file\n */\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\n  const maxSize = 10 * 1024 * 1024; // 10MB\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n  if (!allowedTypes.includes(file.type)) {\n    return {\n      valid: false,\n      error: 'Please upload a valid image file (JPEG, PNG, or WebP)'\n    };\n  }\n\n  if (file.size > maxSize) {\n    return {\n      valid: false,\n      error: 'Image file size must be less than 10MB'\n    };\n  }\n\n  return { valid: true };\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAiCO,MAAM,kBAAkB,CAC7B,MACA,UAAkC;IAChC,UAAU;IACV,YAAY;IACZ,SAAS;IACT,SAAS;IACT,YAAY;AACd,CAAC;IAED,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,CAAC,KAAK;YACR,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,IAAI,MAAM,GAAG;YACX,mCAAmC;YACnC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;YACxB,IAAI,WAAW;YAEf,IAAI,QAAQ,UAAU,EAAE;gBACtB,yEAAyE;gBACzE,IAAI,QAAQ,SAAS,KAAK;oBACxB,WAAW,GAAG,4BAA4B;gBAC5C,OAAO,IAAI,SAAS,QAAQ,KAAK;oBAC/B,WAAW,IAAI,4CAA4C;gBAC7D;YACF;YAEA,0CAA0C;YAC1C,IAAI,aAAa,MAAM,aAAa,KAAK;gBACvC,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;YAClB,OAAO;gBACL,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;YAClB;YAEA,2BAA2B;YAC3B,IAAI,aAAa,GAAG;gBAClB,IAAI,SAAS,CAAC,OAAO,KAAK,GAAG,GAAG,OAAO,MAAM,GAAG;gBAChD,IAAI,MAAM,CAAC,AAAC,WAAW,KAAK,EAAE,GAAI;gBAClC,IAAI,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,SAAS;YACtC;YAEA,sBAAsB;YACtB,IAAI,SAAS,CAAC,KAAK,GAAG;YAEtB,gCAAgC;YAChC,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YACpE,MAAM,OAAO,UAAU,IAAI;YAE3B,4BAA4B;YAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;gBACvC,+CAA+C;gBAC/C,IAAI,OAAO,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;gBAEjF,mBAAmB;gBACnB,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,OAAO,QAAQ,UAAU;gBAE1D,iBAAiB;gBACjB,MAAM,SAAS,AAAC,MAAM,CAAC,QAAQ,QAAQ,GAAG,MAAM,GAAG,IAAK,CAAC,MAAM,CAAC,MAAM,QAAQ,QAAQ,GAAG,GAAG,CAAC;gBAC7F,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,IAAI;gBAEzD,wBAAwB;gBACxB,IAAI,CAAC,EAAE,GAAG,MAAU,MAAM;gBAC1B,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,QAAQ;gBAC5B,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,OAAO;YAC3B,gDAAgD;YAClD;YAEA,qCAAqC;YACrC,IAAI,QAAQ,OAAO,EAAE;gBACnB,mBAAmB,MAAM,OAAO,KAAK,EAAE,OAAO,MAAM;YACtD;YAEA,6BAA6B;YAC7B,IAAI,QAAQ,OAAO,EAAE;gBACnB,mBAAmB,MAAM,OAAO,KAAK,EAAE,OAAO,MAAM;YACtD;YAEA,gCAAgC;YAChC,IAAI,YAAY,CAAC,WAAW,GAAG;YAE/B,QAAQ;gBACN;gBACA,SAAS,OAAO,SAAS,CAAC;YAC5B;QACF;QAEA,IAAI,OAAO,GAAG;YACZ,OAAO,IAAI,MAAM;QACnB;QAEA,8BAA8B;QAC9B,MAAM,YAAY,IAAI,eAAe,CAAC;QACtC,IAAI,GAAG,GAAG;IACZ;AACF;AAEA;;CAEC,GACD,MAAM,qBAAqB,CAAC,MAAyB,OAAe;IAClE,MAAM,SAAS;QACb;QAAG,CAAC;QAAG;QACP,CAAC;QAAG;QAAG,CAAC;QACR;QAAG,CAAC;QAAG;KACR;IAED,MAAM,SAAS,IAAI,kBAAkB;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAAK;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;YAClC,IAAI,MAAM;YACV,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAC/B,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;oBAC/B,MAAM,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI;oBAC5C,OAAO,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;gBACpD;YACF;YACA,MAAM,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI;YAC9B,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;YACxC,MAAM,CAAC,IAAI,GAAG;YACd,MAAM,CAAC,MAAM,EAAE,GAAG;YAClB,MAAM,CAAC,MAAM,EAAE,GAAG;QACpB;IACF;IAEA,KAAK,GAAG,CAAC;AACX;AAEA;;CAEC,GACD,MAAM,qBAAqB,CAAC,MAAyB,OAAe;IAClE,MAAM,SAAS,IAAI,kBAAkB;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAAK;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;YAClC,IAAI,MAAM;YACV,IAAI,QAAQ;YAEZ,oBAAoB;YACpB,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAC/B,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;oBAC/B,MAAM,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI;oBAC5C,OAAO,IAAI,CAAC,IAAI;oBAChB;gBACF;YACF;YAEA,MAAM,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI;YAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM;YAC/B,MAAM,CAAC,IAAI,GAAG;YACd,MAAM,CAAC,MAAM,EAAE,GAAG;YAClB,MAAM,CAAC,MAAM,EAAE,GAAG;QACpB;IACF;IAEA,KAAK,GAAG,CAAC;AACX;AAKO,MAAM,uBAAuB,OAClC,aACA;IAOA,IAAI;QACF,MAAM,EACJ,WAAW,KAAK,EAChB,MAAM,EACN,MAAM,CAAC,EACP,MAAM,EAAG,0BAA0B;QAA5B,EACR,GAAG,WAAW,CAAC;QAEhB,gDAAgD;QAChD,MAAM,SAAS,MAAM,+IAAA,CAAA,UAAS,CAAC,SAAS,CACtC,aACA,UACA;YACE,QAAQ,UAAU,CAAC,KAAO,CAAC;YAC3B,yBAAyB;YACzB,uBAAuB;YACvB,0BAA0B;YAC1B,2BAA2B;YAC3B,kBAAkB;YAClB,iCAAiC;YACjC,kBAAkB;YAClB,yBAAyB;YACzB,yBAAyB;YACzB,6BAA6B;YAC7B,0BAA0B;YAC1B,kCAAkC;QACpC;QAGF,iDAAiD;QACjD,MAAM,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC3C,MAAM,KAAK,IAAI;gBACf,YAAY,KAAK,UAAU;gBAC3B,MAAM;oBACJ,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,IAAI,KAAK,IAAI,CAAC,EAAE;gBAClB;YACF,CAAC;QAED,OAAO;YACL,MAAM,OAAO,IAAI,CAAC,IAAI;YACtB,YAAY,OAAO,IAAI,CAAC,UAAU;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,uBAAuB,OAClC,aACA;IAEA,MAAM,SAAS;QACb;YAAE,MAAM;YAAY,KAAK;YAAG,KAAK;QAAE;QACnC;YAAE,MAAM;YAAgB,KAAK;YAAG,KAAK;QAAE;QACvC;YAAE,MAAM;YAAe,KAAK;YAAG,KAAK;QAAE;QACtC;YAAE,MAAM;YAAe,KAAK;YAAI,KAAK;QAAE;KACxC;IAED,IAAI,aAA+B;IACnC,IAAI,iBAAiB;IAErB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,aAAa,AAAC,CAAC,IAAI,CAAC,IAAI,OAAO,MAAM,GAAI,KAAK,KAAK,IAAI;QAEvD,IAAI;YACF,MAAM,SAAS,MAAM,qBAAqB,aAAa;gBACrD,KAAK,KAAK,GAAG;gBACb,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAC;oBACP,IAAI,KAAK,MAAM,KAAK,oBAAoB;wBACtC,MAAM,eAAe,AAAC,IAAI,OAAO,MAAM,GAAI;wBAC3C,MAAM,kBAAkB,eAAgB,KAAK,QAAQ,GAAG,CAAC,MAAM,OAAO,MAAM;wBAC5E,aAAa,iBAAiB,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,GAAG,KAAK,CAAC,CAAC;oBACpF;gBACF;YACF;YAEA,IAAI,OAAO,UAAU,GAAG,gBAAgB;gBACtC,aAAa;gBACb,iBAAiB,OAAO,UAAU;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE;QAChD;IACF;IAEA,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAKO,MAAM,sBAAsB,OACjC,MACA;IAEA,IAAI;QACF,aAAa,GAAG;QAEhB,8DAA8D;QAC9D,MAAM,iBAAiB,MAAM,gBAAgB,MAAM;YACjD,UAAU;YACV,YAAY;YACZ,SAAS;YACT,SAAS;YACT,YAAY;QACd;QAEA,aAAa,IAAI;QAEjB,0CAA0C;QAC1C,MAAM,SAAS,MAAM,qBACnB,eAAe,MAAM,EACrB,CAAC,UAAU;YACT,MAAM,kBAAkB,KAAM,WAAW;YACzC,aAAa,KAAK,KAAK,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM;QAC/D;QAGF,aAAa,KAAK;QAClB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAKO,MAAM,6BAA6B,OACxC,MACA;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,SAAS;gBAAE,UAAU;gBAAK,YAAY;gBAAK,SAAS;gBAAM,SAAS;gBAAM,YAAY;YAAK;QAC5F;QACA;YACE,MAAM;YACN,SAAS;gBAAE,UAAU;gBAAK,YAAY;gBAAK,SAAS;gBAAM,SAAS;gBAAO,YAAY;YAAK;QAC7F;QACA;YACE,MAAM;YACN,SAAS;gBAAE,UAAU;gBAAK,YAAY;gBAAK,SAAS;gBAAO,SAAS;gBAAO,YAAY;YAAM;QAC/F;KACD;IAED,IAAI,aAA+B;IACnC,IAAI,iBAAiB;IAErB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,MAAM,WAAW,UAAU,CAAC,EAAE;QAE9B,IAAI;YACF,aACE,AAAC,IAAI,WAAW,MAAM,GAAI,KAC1B,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC;YAGzC,MAAM,iBAAiB,MAAM,gBAAgB,MAAM,SAAS,OAAO;YACnE,MAAM,SAAS,MAAM,qBAAqB,eAAe,MAAM,EAAE;gBAC/D,QAAQ,CAAC;oBACP,IAAI,KAAK,MAAM,KAAK,oBAAoB;wBACtC,MAAM,mBAAmB,AAAC,IAAI,WAAW,MAAM,GAAI;wBACnD,MAAM,kBAAkB,mBAAoB,KAAK,QAAQ,GAAG,CAAC,MAAM,WAAW,MAAM;wBACpF,aAAa,iBAAiB,GAAG,SAAS,IAAI,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,GAAG,KAAK,CAAC,CAAC;oBACvF;gBACF;YACF;YAEA,IAAI,OAAO,UAAU,GAAG,gBAAgB;gBACtC,aAAa;gBACb,iBAAiB,OAAO,UAAU;YACpC;YAEA,oCAAoC;YACpC,IAAI,OAAO,UAAU,GAAG,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE;QACpD;IACF;IAEA,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,aAAa,KAAK,CAAC,aAAa,EAAE,WAAW,UAAU,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC;IAChF,OAAO;AACT;AAKO,MAAM,oBAAoB,CAAC;IAChC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;IACzC,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAE3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/ReceiptUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { Upload, X, Loader2, CheckCircle, AlertCircle, Camera, Zap } from 'lucide-react';\nimport { processReceiptWithFallback } from '@/lib/ocr';\n\n// Simple receipt parser\nconst parseReceiptText = (text: string) => {\n  const lines = text.split('\\n').map(line => line.trim()).filter(line => line.length > 0);\n\n  // Extract merchant (usually first meaningful line)\n  let merchant = 'Unknown Merchant';\n  for (const line of lines.slice(0, 3)) {\n    if (line.length > 3 && !line.match(/^\\d+/) && !line.includes('$')) {\n      merchant = line;\n      break;\n    }\n  }\n\n  // Extract amount (look for TOTAL)\n  let amount = 0;\n  for (const line of lines) {\n    if (line.toUpperCase().includes('TOTAL')) {\n      const match = line.match(/\\$?(\\d+\\.?\\d*)/);\n      if (match) {\n        amount = parseFloat(match[1]);\n        break;\n      }\n    }\n  }\n\n  // If no total found, look for any dollar amount\n  if (amount === 0) {\n    for (const line of lines.reverse()) {\n      const match = line.match(/\\$(\\d+\\.\\d{2})/);\n      if (match) {\n        amount = parseFloat(match[1]);\n        break;\n      }\n    }\n  }\n\n  // Extract date\n  let date = new Date();\n  for (const line of lines) {\n    const dateMatch = line.match(/(\\d{1,2}\\/\\d{1,2}\\/\\d{4})/);\n    if (dateMatch) {\n      date = new Date(dateMatch[1]);\n      break;\n    }\n  }\n\n  // Suggest category based on merchant\n  let suggestedCategory = 'Other';\n  const merchantLower = merchant.toLowerCase();\n  if (merchantLower.includes('walmart') || merchantLower.includes('target') || merchantLower.includes('store')) {\n    suggestedCategory = 'Shopping';\n  } else if (merchantLower.includes('starbucks') || merchantLower.includes('restaurant') || merchantLower.includes('cafe')) {\n    suggestedCategory = 'Food & Dining';\n  } else if (merchantLower.includes('shell') || merchantLower.includes('gas') || merchantLower.includes('fuel')) {\n    suggestedCategory = 'Transportation';\n  }\n\n  return {\n    merchant,\n    amount,\n    date,\n    suggestedCategory,\n    rawText: text\n  };\n};\n\ninterface ReceiptUploadProps {\n  onDataExtracted: (data: any) => void;\n  onClose: () => void;\n}\n\nexport default function ReceiptUpload({ onDataExtracted, onClose }: ReceiptUploadProps) {\n  const [file, setFile] = useState<File | null>(null);\n  const [preview, setPreview] = useState<string | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [extractedData, setExtractedData] = useState<any>(null);\n  const [processingStatus, setProcessingStatus] = useState<string>('');\n  const [processingProgress, setProcessingProgress] = useState<number>(0);\n  const [useRealOCR, setUseRealOCR] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const validateImageFile = (file: File) => {\n    const maxSize = 10 * 1024 * 1024; // 10MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return { valid: false, error: 'Please upload a valid image file (JPEG, PNG, or WebP)' };\n    }\n\n    if (file.size > maxSize) {\n      return { valid: false, error: 'Image file size must be less than 10MB' };\n    }\n\n    return { valid: true };\n  };\n\n  const handleFileSelect = (selectedFile: File) => {\n    const validation = validateImageFile(selectedFile);\n    if (!validation.valid) {\n      setError(validation.error || 'Invalid file');\n      return;\n    }\n\n    setFile(selectedFile);\n    setError(null);\n    setExtractedData(null);\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n    };\n    reader.readAsDataURL(selectedFile);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    const droppedFile = e.dataTransfer.files[0];\n    if (droppedFile) {\n      handleFileSelect(droppedFile);\n    }\n  };\n\n  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = e.target.files?.[0];\n    if (selectedFile) {\n      handleFileSelect(selectedFile);\n    }\n  };\n\n  const processReceipt = async () => {\n    if (!file) return;\n\n    setIsProcessing(true);\n    setError(null);\n    setProcessingProgress(0);\n    setProcessingStatus('Starting...');\n\n    try {\n      let ocrText: string;\n\n      if (useRealOCR) {\n        // Use real OCR with Tesseract.js\n        const result = await processReceiptWithFallback(\n          file,\n          (progress, status) => {\n            setProcessingProgress(progress);\n            setProcessingStatus(status);\n          }\n        );\n        ocrText = result.text;\n      } else {\n        // Use simulated OCR for demo\n        setProcessingStatus('Simulating OCR processing...');\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        const sampleReceipts = [\n          `WALMART SUPERCENTER\n123 MAIN ST\nANYTOWN, ST 12345\n\nDate: ${new Date().toLocaleDateString()}\nTime: 14:30\n\nGROCERIES\nMILK                 $3.99\nBREAD                $2.49\nEGGS                 $4.29\n\nSUBTOTAL            $10.77\nTAX                  $0.86\nTOTAL               $11.63\n\nTHANK YOU FOR SHOPPING`,\n\n          `STARBUCKS COFFEE\n\n${new Date().toLocaleDateString()} 10:45 AM\n\nLATTE GRANDE         $5.25\nCROISSANT            $3.50\n\nTOTAL                $8.75\n\nTHANK YOU`,\n\n          `SHELL STATION #12345\n\nFUEL PURCHASE\nGRADE: REGULAR\nGALLONS: 12.456\nPRICE/GAL: $3.299\n\nFUEL TOTAL          $41.08\n\nDATE: ${new Date().toLocaleDateString()}\nTIME: 08:15`\n        ];\n\n        ocrText = sampleReceipts[Math.floor(Math.random() * sampleReceipts.length)];\n        setProcessingProgress(100);\n      }\n\n      // Parse the extracted text\n      const parsedData = parseReceiptText(ocrText);\n\n      setExtractedData(parsedData);\n      setProcessingStatus('Processing complete!');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to process receipt');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleConfirm = () => {\n    if (extractedData) {\n      onDataExtracted(extractedData);\n      onClose();\n    }\n  };\n\n  const reset = () => {\n    setFile(null);\n    setPreview(null);\n    setExtractedData(null);\n    setError(null);\n    setProgress(0);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <div>\n            <h2 className=\"text-xl font-semibold text-gray-900\">Smart Receipt Scanner</h2>\n            <p className=\"text-sm text-gray-500 mt-1\">Upload a receipt image for automatic data extraction</p>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6 space-y-6\">\n          {/* OCR Mode Toggle */}\n          <div className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-center space-x-3\">\n              <Zap className=\"h-5 w-5 text-blue-600\" />\n              <div>\n                <p className=\"text-sm font-medium text-blue-900\">\n                  {useRealOCR ? 'Real OCR Mode' : 'Demo Mode'}\n                </p>\n                <p className=\"text-xs text-blue-700\">\n                  {useRealOCR ? 'Using Tesseract.js for actual text recognition' : 'Using simulated OCR for demonstration'}\n                </p>\n              </div>\n            </div>\n            <button\n              onClick={() => setUseRealOCR(!useRealOCR)}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                useRealOCR ? 'bg-blue-600' : 'bg-gray-200'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  useRealOCR ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n\n          {!file && (\n            <div\n              className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors cursor-pointer\"\n              onDrop={handleDrop}\n              onDragOver={(e) => e.preventDefault()}\n              onClick={() => fileInputRef.current?.click()}\n            >\n              <div className=\"flex flex-col items-center\">\n                <Camera className=\"h-12 w-12 text-gray-400 mb-4\" />\n                <Upload className=\"h-8 w-8 text-gray-300 mb-4\" />\n              </div>\n              <p className=\"text-lg font-medium text-gray-900 mb-2\">\n                Drop your receipt here or click to browse\n              </p>\n              <p className=\"text-sm text-gray-500 mb-4\">\n                Supports JPEG, PNG, and WebP files up to 10MB\n              </p>\n              <div className=\"flex items-center justify-center space-x-4 text-xs text-gray-400\">\n                <span>📱 Mobile photos</span>\n                <span>•</span>\n                <span>🖼️ Scanned images</span>\n                <span>•</span>\n                <span>📄 PDF receipts</span>\n              </div>\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileInput}\n                className=\"hidden\"\n              />\n            </div>\n          )}\n\n          {file && preview && (\n            <div className=\"space-y-4\">\n              <div className=\"relative\">\n                <img\n                  src={preview}\n                  alt=\"Receipt preview\"\n                  className=\"w-full max-h-64 object-contain rounded-lg border\"\n                />\n                <button\n                  onClick={reset}\n                  className=\"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n\n              {!isProcessing && !extractedData && (\n                <button\n                  onClick={processReceipt}\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  Process Receipt\n                </button>\n              )}\n\n              {isProcessing && (\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-center space-x-2\">\n                    <Loader2 className=\"h-5 w-5 animate-spin text-blue-600\" />\n                    <span className=\"text-sm text-gray-600\">{processingStatus}</span>\n                  </div>\n                  {useRealOCR && (\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${processingProgress}%` }}\n                      />\n                    </div>\n                  )}\n                  <div className=\"text-xs text-center text-gray-500\">\n                    {useRealOCR ? 'Real OCR processing may take 10-30 seconds' : 'Demo processing...'}\n                  </div>\n                </div>\n              )}\n\n              {error && (\n                <div className=\"flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg\">\n                  <AlertCircle className=\"h-5 w-5\" />\n                  <span className=\"text-sm\">{error}</span>\n                </div>\n              )}\n\n              {extractedData && (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg\">\n                    <CheckCircle className=\"h-5 w-5\" />\n                    <span className=\"text-sm\">Receipt processed successfully!</span>\n                  </div>\n\n                  <div className=\"bg-gray-50 p-4 rounded-lg space-y-3\">\n                    <h3 className=\"font-medium text-gray-900\">Extracted Information</h3>\n                    \n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Merchant:</span>\n                        <p className=\"font-medium\">{extractedData.merchant}</p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Amount:</span>\n                        <p className=\"font-medium\">${extractedData.amount.toFixed(2)}</p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Date:</span>\n                        <p className=\"font-medium\">\n                          {extractedData.date ? extractedData.date.toLocaleDateString() : 'Not found'}\n                        </p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Category:</span>\n                        <p className=\"font-medium\">{extractedData.suggestedCategory}</p>\n                      </div>\n                    </div>\n\n                    <div className=\"text-xs text-gray-500\">\n                      <p>Data extracted from receipt image using OCR simulation</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={handleConfirm}\n                      className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors\"\n                    >\n                      Use This Data\n                    </button>\n                    <button\n                      onClick={reset}\n                      className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors\"\n                    >\n                      Try Again\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,wBAAwB;AACxB,MAAM,mBAAmB,CAAC;IACxB,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAErF,mDAAmD;IACnD,IAAI,WAAW;IACf,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,GAAI;QACpC,IAAI,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC,MAAM;YACjE,WAAW;YACX;QACF;IACF;IAEA,kCAAkC;IAClC,IAAI,SAAS;IACb,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;YACxC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,OAAO;gBACT,SAAS,WAAW,KAAK,CAAC,EAAE;gBAC5B;YACF;QACF;IACF;IAEA,gDAAgD;IAChD,IAAI,WAAW,GAAG;QAChB,KAAK,MAAM,QAAQ,MAAM,OAAO,GAAI;YAClC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,OAAO;gBACT,SAAS,WAAW,KAAK,CAAC,EAAE;gBAC5B;YACF;QACF;IACF;IAEA,eAAe;IACf,IAAI,OAAO,IAAI;IACf,KAAK,MAAM,QAAQ,MAAO;QACxB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,IAAI,WAAW;YACb,OAAO,IAAI,KAAK,SAAS,CAAC,EAAE;YAC5B;QACF;IACF;IAEA,qCAAqC;IACrC,IAAI,oBAAoB;IACxB,MAAM,gBAAgB,SAAS,WAAW;IAC1C,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,UAAU;QAC5G,oBAAoB;IACtB,OAAO,IAAI,cAAc,QAAQ,CAAC,gBAAgB,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,SAAS;QACxH,oBAAoB;IACtB,OAAO,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,UAAU,cAAc,QAAQ,CAAC,SAAS;QAC7G,oBAAoB;IACtB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,SAAS;IACX;AACF;AAOe,SAAS,cAAc,EAAE,eAAe,EAAE,OAAO,EAAsB;IACpF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;QACzC,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAa;QAE3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAAwD;QACxF;QAEA,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAAyC;QACzE;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,kBAAkB;QACrC,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,SAAS,WAAW,KAAK,IAAI;YAC7B;QACF;QAEA,QAAQ;QACR,SAAS;QACT,iBAAiB;QAEjB,iBAAiB;QACjB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,WAAW,EAAE,MAAM,EAAE;QACvB;QACA,OAAO,aAAa,CAAC;IACvB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,MAAM,cAAc,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QAC3C,IAAI,aAAa;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,eAAe,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM;QAEX,gBAAgB;QAChB,SAAS;QACT,sBAAsB;QACtB,oBAAoB;QAEpB,IAAI;YACF,IAAI;YAEJ,IAAI,YAAY;gBACd,iCAAiC;gBACjC,MAAM,SAAS,MAAM,CAAA,GAAA,iHAAA,CAAA,6BAA0B,AAAD,EAC5C,MACA,CAAC,UAAU;oBACT,sBAAsB;oBACtB,oBAAoB;gBACtB;gBAEF,UAAU,OAAO,IAAI;YACvB,OAAO;gBACL,6BAA6B;gBAC7B,oBAAoB;gBACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,MAAM,iBAAiB;oBACrB,CAAC;;;;MAIL,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;;;;;;;;;sBAYlB,CAAC;oBAEb,CAAC;;AAEX,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;;;;SAOzB,CAAC;oBAEA,CAAC;;;;;;;;;MASL,EAAE,IAAI,OAAO,kBAAkB,GAAG;WAC7B,CAAC;iBACH;gBAED,UAAU,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;gBAC3E,sBAAsB;YACxB;YAEA,2BAA2B;YAC3B,MAAM,aAAa,iBAAiB;YAEpC,iBAAiB;YACjB,oBAAoB;QACtB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe;YACjB,gBAAgB;YAChB;QACF;IACF;IAEA,MAAM,QAAQ;QACZ,QAAQ;QACR,WAAW;QACX,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAE5C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DACV,aAAa,kBAAkB;;;;;;8DAElC,8OAAC;oDAAE,WAAU;8DACV,aAAa,mDAAmD;;;;;;;;;;;;;;;;;;8CAIvE,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAW,CAAC,0EAA0E,EACpF,aAAa,gBAAgB,eAC7B;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,aAAa,kBAAkB,iBAC/B;;;;;;;;;;;;;;;;;wBAKP,CAAC,sBACA,8OAAC;4BACC,WAAU;4BACV,QAAQ;4BACR,YAAY,CAAC,IAAM,EAAE,cAAc;4BACnC,SAAS,IAAM,aAAa,OAAO,EAAE;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;;;;;;;wBAKf,QAAQ,yBACP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,KAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAIhB,CAAC,gBAAgB,CAAC,+BACjB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;gCAKF,8BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;wCAE1C,4BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;gDAAC;;;;;;;;;;;sDAI/C,8OAAC;4CAAI,WAAU;sDACZ,aAAa,+CAA+C;;;;;;;;;;;;gCAKlE,uBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;gCAI9B,+BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAE,WAAU;8EAAe,cAAc,QAAQ;;;;;;;;;;;;sEAEpD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAE,WAAU;;wEAAc;wEAAE,cAAc,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;sEAE5D,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAE,WAAU;8EACV,cAAc,IAAI,GAAG,cAAc,IAAI,CAAC,kBAAkB,KAAK;;;;;;;;;;;;sEAGpE,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAE,WAAU;8EAAe,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;8DAI/D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAE;;;;;;;;;;;;;;;;;sDAIP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/Charts.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler,\n} from 'chart.js';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n);\n\ninterface SpendingTrendData {\n  labels: string[];\n  amounts: number[];\n}\n\ninterface CategoryData {\n  category: string;\n  amount: number;\n  color: string;\n}\n\ninterface MonthlyComparisonData {\n  month: string;\n  currentYear: number;\n  previousYear: number;\n}\n\n// Spending Trend Chart\nexport function SpendingTrendChart({ data }: { data: SpendingTrendData }) {\n  const chartData = {\n    labels: data.labels,\n    datasets: [\n      {\n        label: 'Daily Spending',\n        data: data.amounts,\n        borderColor: 'rgb(59, 130, 246)',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        borderWidth: 2,\n        fill: true,\n        tension: 0.4,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Spending Trend (Last 30 Days)',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '$' + value.toFixed(0);\n          },\n        },\n      },\n      x: {\n        grid: {\n          display: false,\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Line data={chartData} options={options} />\n    </div>\n  );\n}\n\n// Category Breakdown Chart\nexport function CategoryBreakdownChart({ data }: { data: CategoryData[] }) {\n  const chartData = {\n    labels: data.map(item => item.category),\n    datasets: [\n      {\n        data: data.map(item => item.amount),\n        backgroundColor: data.map(item => item.color),\n        borderWidth: 0,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'right' as const,\n        labels: {\n          usePointStyle: true,\n          padding: 20,\n        },\n      },\n      title: {\n        display: true,\n        text: 'Spending by Category',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);\n            const percentage = ((context.parsed / total) * 100).toFixed(1);\n            return `${context.label}: $${context.parsed.toFixed(2)} (${percentage}%)`;\n          },\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Doughnut data={chartData} options={options} />\n    </div>\n  );\n}\n\n// Monthly Comparison Chart\nexport function MonthlyComparisonChart({ data }: { data: MonthlyComparisonData[] }) {\n  const chartData = {\n    labels: data.map(item => item.month),\n    datasets: [\n      {\n        label: '2024',\n        data: data.map(item => item.currentYear),\n        backgroundColor: 'rgba(59, 130, 246, 0.8)',\n        borderColor: 'rgb(59, 130, 246)',\n        borderWidth: 1,\n      },\n      {\n        label: '2023',\n        data: data.map(item => item.previousYear),\n        backgroundColor: 'rgba(156, 163, 175, 0.8)',\n        borderColor: 'rgb(156, 163, 175)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: 'Monthly Spending Comparison',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            return `${context.dataset.label}: $${context.parsed.y.toFixed(2)}`;\n          },\n        },\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '$' + value.toFixed(0);\n          },\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Bar data={chartData} options={options} />\n    </div>\n  );\n}\n\n// Budget Progress Chart\nexport function BudgetProgressChart({ \n  budgets \n}: { \n  budgets: Array<{\n    category: string;\n    budgetAmount: number;\n    spentAmount: number;\n    color: string;\n  }> \n}) {\n  const chartData = {\n    labels: budgets.map(b => b.category),\n    datasets: [\n      {\n        label: 'Budget',\n        data: budgets.map(b => b.budgetAmount),\n        backgroundColor: 'rgba(229, 231, 235, 0.8)',\n        borderColor: 'rgb(229, 231, 235)',\n        borderWidth: 1,\n      },\n      {\n        label: 'Spent',\n        data: budgets.map(b => b.spentAmount),\n        backgroundColor: budgets.map(b => b.color),\n        borderColor: budgets.map(b => b.color),\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: 'Budget vs Actual Spending',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            return `${context.dataset.label}: $${context.parsed.y.toFixed(2)}`;\n          },\n        },\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '$' + value.toFixed(0);\n          },\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Bar data={chartData} options={options} />\n    </div>\n  );\n}\n\n// Savings Goal Progress Chart\nexport function SavingsGoalChart({ \n  goals \n}: { \n  goals: Array<{\n    name: string;\n    targetAmount: number;\n    currentAmount: number;\n    color: string;\n  }> \n}) {\n  const chartData = {\n    labels: goals.map(g => g.name),\n    datasets: [\n      {\n        label: 'Progress',\n        data: goals.map(g => (g.currentAmount / g.targetAmount) * 100),\n        backgroundColor: goals.map(g => g.color),\n        borderColor: goals.map(g => g.color),\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    indexAxis: 'y' as const,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Savings Goals Progress',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            const goal = goals[context.dataIndex];\n            return `${context.parsed.x.toFixed(1)}% ($${goal.currentAmount.toFixed(2)} / $${goal.targetAmount.toFixed(2)})`;\n          },\n        },\n      },\n    },\n    scales: {\n      x: {\n        beginAtZero: true,\n        max: 100,\n        ticks: {\n          callback: function(value: any) {\n            return value + '%';\n          },\n        },\n      },\n    },\n  };\n\n  return (\n    <div className=\"h-64\">\n      <Bar data={chartData} options={options} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAGA;AAaA;AAhBA;;;;AAkBA,+BAA+B;AAC/B,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,eAAY,EACZ,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM,EACN,4JAAA,CAAA,SAAM;AAqBD,SAAS,mBAAmB,EAAE,IAAI,EAA+B;IACtE,MAAM,YAAY;QAChB,QAAQ,KAAK,MAAM;QACnB,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,KAAK,OAAO;gBAClB,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,MAAM;gBACN,SAAS;YACX;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,MAAM,MAAM,OAAO,CAAC;oBAC7B;gBACF;YACF;YACA,GAAG;gBACD,MAAM;oBACJ,SAAS;gBACX;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,OAAI;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGtC;AAGO,SAAS,uBAAuB,EAAE,IAAI,EAA4B;IACvE,MAAM,YAAY;QAChB,QAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;QACtC,UAAU;YACR;gBACE,MAAM,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;gBAClC,iBAAiB,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;gBAC5C,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;gBACV,QAAQ;oBACN,eAAe;oBACf,SAAS;gBACX;YACF;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAW,IAAc,IAAI,GAAG;wBAC3E,MAAM,aAAa,CAAC,AAAC,QAAQ,MAAM,GAAG,QAAS,GAAG,EAAE,OAAO,CAAC;wBAC5D,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC;oBAC3E;gBACF;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,WAAQ;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAG1C;AAGO,SAAS,uBAAuB,EAAE,IAAI,EAAqC;IAChF,MAAM,YAAY;QAChB,QAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;QACnC,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,WAAW;gBACvC,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;YACA;gBACE,OAAO;gBACP,MAAM,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,YAAY;gBACxC,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI;oBACpE;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,MAAM,MAAM,OAAO,CAAC;oBAC7B;gBACF;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,MAAG;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGrC;AAGO,SAAS,oBAAoB,EAClC,OAAO,EAQR;IACC,MAAM,YAAY;QAChB,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;QACnC,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY;gBACrC,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;YACA;gBACE,OAAO;gBACP,MAAM,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;gBACpC,iBAAiB,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACzC,aAAa,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACrC,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI;oBACpE;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,MAAM,MAAM,OAAO,CAAC;oBAC7B;gBACF;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,MAAG;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGrC;AAGO,SAAS,iBAAiB,EAC/B,KAAK,EAQN;IACC,MAAM,YAAY;QAChB,QAAQ,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAC7B,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,MAAM,GAAG,CAAC,CAAA,IAAK,AAAC,EAAE,aAAa,GAAG,EAAE,YAAY,GAAI;gBAC1D,iBAAiB,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACvC,aAAa,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACnC,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,WAAW;QACX,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,MAAM,OAAO,KAAK,CAAC,QAAQ,SAAS,CAAC;wBACrC,OAAO,GAAG,QAAQ,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,KAAK,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACjH;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,KAAK;gBACL,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,QAAQ;oBACjB;gBACF;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sJAAA,CAAA,MAAG;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGrC", "debugId": null}}, {"offset": {"line": 1612, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/lib/ai-insights.ts"], "sourcesContent": ["import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, subMonths, differenceInDays } from 'date-fns';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\nexport interface SpendingInsight {\n  type: 'warning' | 'info' | 'success' | 'alert';\n  title: string;\n  description: string;\n  action?: string;\n  priority: 'low' | 'medium' | 'high';\n  category?: string;\n  amount?: number;\n}\n\nexport interface SpendingPattern {\n  pattern: string;\n  frequency: number;\n  averageAmount: number;\n  category: string;\n  confidence: number;\n}\n\nexport interface AnomalyDetection {\n  isAnomaly: boolean;\n  type: 'amount' | 'frequency' | 'category' | 'merchant';\n  description: string;\n  severity: 'low' | 'medium' | 'high';\n  expectedValue: number;\n  actualValue: number;\n}\n\n/**\n * Generate AI-powered spending insights\n */\nexport const generateSpendingInsights = (expenses: Expense[]): SpendingInsight[] => {\n  const insights: SpendingInsight[] = [];\n  \n  if (expenses.length === 0) {\n    return [{\n      type: 'info',\n      title: 'Start Tracking Your Expenses',\n      description: 'Add some expenses to get personalized insights and recommendations.',\n      priority: 'low'\n    }];\n  }\n\n  // Analyze spending patterns\n  const patterns = analyzeSpendingPatterns(expenses);\n  const anomalies = detectAnomalies(expenses);\n  const trends = analyzeTrends(expenses);\n  \n  // Generate insights based on patterns\n  patterns.forEach(pattern => {\n    if (pattern.confidence > 0.7) {\n      insights.push({\n        type: 'info',\n        title: `Regular ${pattern.category} Spending`,\n        description: `You spend an average of $${pattern.averageAmount.toFixed(2)} on ${pattern.category} ${pattern.pattern}. This accounts for ${((pattern.averageAmount * pattern.frequency) / getTotalSpending(expenses) * 100).toFixed(1)}% of your total spending.`,\n        priority: 'low',\n        category: pattern.category,\n        amount: pattern.averageAmount\n      });\n    }\n  });\n\n  // Generate anomaly alerts\n  anomalies.forEach(anomaly => {\n    if (anomaly.isAnomaly && anomaly.severity !== 'low') {\n      insights.push({\n        type: anomaly.severity === 'high' ? 'alert' : 'warning',\n        title: `Unusual ${anomaly.type} Detected`,\n        description: anomaly.description,\n        priority: anomaly.severity === 'high' ? 'high' : 'medium'\n      });\n    }\n  });\n\n  // Generate trend insights\n  if (trends.monthlyGrowth > 20) {\n    insights.push({\n      type: 'warning',\n      title: 'Spending Increase Alert',\n      description: `Your spending has increased by ${trends.monthlyGrowth.toFixed(1)}% compared to last month. Consider reviewing your budget.`,\n      priority: 'high',\n      action: 'Review recent expenses and identify areas to cut back'\n    });\n  } else if (trends.monthlyGrowth < -10) {\n    insights.push({\n      type: 'success',\n      title: 'Great Job Saving!',\n      description: `You've reduced your spending by ${Math.abs(trends.monthlyGrowth).toFixed(1)}% this month. Keep up the good work!`,\n      priority: 'low'\n    });\n  }\n\n  // Category-specific insights\n  const categoryInsights = generateCategoryInsights(expenses);\n  insights.push(...categoryInsights);\n\n  // Predictive insights\n  const predictions = generatePredictiveInsights(expenses);\n  insights.push(...predictions);\n\n  return insights.sort((a, b) => {\n    const priorityOrder = { high: 3, medium: 2, low: 1 };\n    return priorityOrder[b.priority] - priorityOrder[a.priority];\n  });\n};\n\n/**\n * Analyze spending patterns\n */\nconst analyzeSpendingPatterns = (expenses: Expense[]): SpendingPattern[] => {\n  const patterns: SpendingPattern[] = [];\n  const categoryGroups = groupByCategory(expenses);\n\n  Object.entries(categoryGroups).forEach(([category, categoryExpenses]) => {\n    // Analyze weekly patterns\n    const weeklySpending = analyzeWeeklyPattern(categoryExpenses);\n    if (weeklySpending.frequency > 0.5) {\n      patterns.push({\n        pattern: 'weekly',\n        frequency: weeklySpending.frequency,\n        averageAmount: weeklySpending.average,\n        category,\n        confidence: weeklySpending.frequency\n      });\n    }\n\n    // Analyze daily patterns\n    const dailySpending = analyzeDailyPattern(categoryExpenses);\n    if (dailySpending.frequency > 0.3) {\n      patterns.push({\n        pattern: 'daily',\n        frequency: dailySpending.frequency,\n        averageAmount: dailySpending.average,\n        category,\n        confidence: dailySpending.frequency\n      });\n    }\n  });\n\n  return patterns;\n};\n\n/**\n * Detect spending anomalies\n */\nconst detectAnomalies = (expenses: Expense[]): AnomalyDetection[] => {\n  const anomalies: AnomalyDetection[] = [];\n  \n  // Amount anomalies\n  const amounts = expenses.map(e => e.amount);\n  const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;\n  const stdDev = Math.sqrt(amounts.reduce((sum, amt) => sum + Math.pow(amt - avgAmount, 2), 0) / amounts.length);\n  \n  expenses.forEach(expense => {\n    const zScore = Math.abs((expense.amount - avgAmount) / stdDev);\n    if (zScore > 2) { // More than 2 standard deviations\n      anomalies.push({\n        isAnomaly: true,\n        type: 'amount',\n        description: `Unusually ${expense.amount > avgAmount ? 'high' : 'low'} expense of $${expense.amount.toFixed(2)} at ${expense.merchant}`,\n        severity: zScore > 3 ? 'high' : 'medium',\n        expectedValue: avgAmount,\n        actualValue: expense.amount\n      });\n    }\n  });\n\n  // Frequency anomalies\n  const categoryFrequency = analyzeFrequencyAnomalies(expenses);\n  anomalies.push(...categoryFrequency);\n\n  return anomalies;\n};\n\n/**\n * Analyze spending trends\n */\nconst analyzeTrends = (expenses: Expense[]) => {\n  const now = new Date();\n  const thisMonth = expenses.filter(e => {\n    const expenseDate = new Date(e.date);\n    return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();\n  });\n  \n  const lastMonth = expenses.filter(e => {\n    const expenseDate = new Date(e.date);\n    const lastMonthDate = subMonths(now, 1);\n    return expenseDate.getMonth() === lastMonthDate.getMonth() && expenseDate.getFullYear() === lastMonthDate.getFullYear();\n  });\n\n  const thisMonthTotal = thisMonth.reduce((sum, e) => sum + e.amount, 0);\n  const lastMonthTotal = lastMonth.reduce((sum, e) => sum + e.amount, 0);\n  \n  const monthlyGrowth = lastMonthTotal > 0 ? ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100 : 0;\n\n  return {\n    monthlyGrowth,\n    thisMonthTotal,\n    lastMonthTotal\n  };\n};\n\n/**\n * Generate category-specific insights\n */\nconst generateCategoryInsights = (expenses: Expense[]): SpendingInsight[] => {\n  const insights: SpendingInsight[] = [];\n  const categoryTotals = groupByCategory(expenses);\n  const totalSpending = getTotalSpending(expenses);\n\n  Object.entries(categoryTotals).forEach(([category, categoryExpenses]) => {\n    const categoryTotal = categoryExpenses.reduce((sum, e) => sum + e.amount, 0);\n    const percentage = (categoryTotal / totalSpending) * 100;\n\n    if (percentage > 40) {\n      insights.push({\n        type: 'warning',\n        title: `High ${category} Spending`,\n        description: `${category} accounts for ${percentage.toFixed(1)}% of your total spending ($${categoryTotal.toFixed(2)}). Consider if this aligns with your priorities.`,\n        priority: 'medium',\n        category,\n        amount: categoryTotal\n      });\n    }\n\n    // Detect frequent small purchases\n    const smallPurchases = categoryExpenses.filter(e => e.amount < 10);\n    if (smallPurchases.length > 10) {\n      const smallTotal = smallPurchases.reduce((sum, e) => sum + e.amount, 0);\n      insights.push({\n        type: 'info',\n        title: `Frequent Small ${category} Purchases`,\n        description: `You made ${smallPurchases.length} small purchases in ${category} totaling $${smallTotal.toFixed(2)}. These add up over time!`,\n        priority: 'low',\n        category,\n        amount: smallTotal\n      });\n    }\n  });\n\n  return insights;\n};\n\n/**\n * Generate predictive insights\n */\nconst generatePredictiveInsights = (expenses: Expense[]): SpendingInsight[] => {\n  const insights: SpendingInsight[] = [];\n  \n  // Predict monthly spending based on current trend\n  const now = new Date();\n  const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();\n  const daysPassed = now.getDate();\n  \n  const thisMonthExpenses = expenses.filter(e => {\n    const expenseDate = new Date(e.date);\n    return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();\n  });\n  \n  const currentMonthSpending = thisMonthExpenses.reduce((sum, e) => sum + e.amount, 0);\n  const dailyAverage = currentMonthSpending / daysPassed;\n  const projectedMonthlySpending = dailyAverage * daysInMonth;\n  \n  if (daysPassed > 7) { // Only predict after a week of data\n    insights.push({\n      type: 'info',\n      title: 'Monthly Spending Projection',\n      description: `Based on your current spending pattern, you're projected to spend $${projectedMonthlySpending.toFixed(2)} this month.`,\n      priority: 'low',\n      amount: projectedMonthlySpending\n    });\n  }\n\n  return insights;\n};\n\n// Helper functions\nconst groupByCategory = (expenses: Expense[]) => {\n  return expenses.reduce((groups, expense) => {\n    const category = expense.category;\n    if (!groups[category]) {\n      groups[category] = [];\n    }\n    groups[category].push(expense);\n    return groups;\n  }, {} as Record<string, Expense[]>);\n};\n\nconst getTotalSpending = (expenses: Expense[]) => {\n  return expenses.reduce((sum, expense) => sum + expense.amount, 0);\n};\n\nconst analyzeWeeklyPattern = (expenses: Expense[]) => {\n  // Simplified weekly pattern analysis\n  const weeklyTotals: number[] = [];\n  // Implementation would analyze weekly spending patterns\n  return { frequency: 0.5, average: 50 }; // Placeholder\n};\n\nconst analyzeDailyPattern = (expenses: Expense[]) => {\n  // Simplified daily pattern analysis\n  return { frequency: 0.3, average: 15 }; // Placeholder\n};\n\nconst analyzeFrequencyAnomalies = (expenses: Expense[]): AnomalyDetection[] => {\n  // Simplified frequency anomaly detection\n  return []; // Placeholder\n};\n"], "names": [], "mappings": ";;;AAAA;;AAwCO,MAAM,2BAA2B,CAAC;IACvC,MAAM,WAA8B,EAAE;IAEtC,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;YAAC;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;YACZ;SAAE;IACJ;IAEA,4BAA4B;IAC5B,MAAM,WAAW,wBAAwB;IACzC,MAAM,YAAY,gBAAgB;IAClC,MAAM,SAAS,cAAc;IAE7B,sCAAsC;IACtC,SAAS,OAAO,CAAC,CAAA;QACf,IAAI,QAAQ,UAAU,GAAG,KAAK;YAC5B,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,SAAS,CAAC;gBAC7C,aAAa,CAAC,yBAAyB,EAAE,QAAQ,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,oBAAoB,EAAE,CAAC,AAAC,QAAQ,aAAa,GAAG,QAAQ,SAAS,GAAI,iBAAiB,YAAY,GAAG,EAAE,OAAO,CAAC,GAAG,yBAAyB,CAAC;gBAChQ,UAAU;gBACV,UAAU,QAAQ,QAAQ;gBAC1B,QAAQ,QAAQ,aAAa;YAC/B;QACF;IACF;IAEA,0BAA0B;IAC1B,UAAU,OAAO,CAAC,CAAA;QAChB,IAAI,QAAQ,SAAS,IAAI,QAAQ,QAAQ,KAAK,OAAO;YACnD,SAAS,IAAI,CAAC;gBACZ,MAAM,QAAQ,QAAQ,KAAK,SAAS,UAAU;gBAC9C,OAAO,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC;gBACzC,aAAa,QAAQ,WAAW;gBAChC,UAAU,QAAQ,QAAQ,KAAK,SAAS,SAAS;YACnD;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,OAAO,aAAa,GAAG,IAAI;QAC7B,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,OAAO;YACP,aAAa,CAAC,+BAA+B,EAAE,OAAO,aAAa,CAAC,OAAO,CAAC,GAAG,yDAAyD,CAAC;YACzI,UAAU;YACV,QAAQ;QACV;IACF,OAAO,IAAI,OAAO,aAAa,GAAG,CAAC,IAAI;QACrC,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,OAAO;YACP,aAAa,CAAC,gCAAgC,EAAE,KAAK,GAAG,CAAC,OAAO,aAAa,EAAE,OAAO,CAAC,GAAG,oCAAoC,CAAC;YAC/H,UAAU;QACZ;IACF;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB,yBAAyB;IAClD,SAAS,IAAI,IAAI;IAEjB,sBAAsB;IACtB,MAAM,cAAc,2BAA2B;IAC/C,SAAS,IAAI,IAAI;IAEjB,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG;QACvB,MAAM,gBAAgB;YAAE,MAAM;YAAG,QAAQ;YAAG,KAAK;QAAE;QACnD,OAAO,aAAa,CAAC,EAAE,QAAQ,CAAC,GAAG,aAAa,CAAC,EAAE,QAAQ,CAAC;IAC9D;AACF;AAEA;;CAEC,GACD,MAAM,0BAA0B,CAAC;IAC/B,MAAM,WAA8B,EAAE;IACtC,MAAM,iBAAiB,gBAAgB;IAEvC,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,UAAU,iBAAiB;QAClE,0BAA0B;QAC1B,MAAM,iBAAiB,qBAAqB;QAC5C,IAAI,eAAe,SAAS,GAAG,KAAK;YAClC,SAAS,IAAI,CAAC;gBACZ,SAAS;gBACT,WAAW,eAAe,SAAS;gBACnC,eAAe,eAAe,OAAO;gBACrC;gBACA,YAAY,eAAe,SAAS;YACtC;QACF;QAEA,yBAAyB;QACzB,MAAM,gBAAgB,oBAAoB;QAC1C,IAAI,cAAc,SAAS,GAAG,KAAK;YACjC,SAAS,IAAI,CAAC;gBACZ,SAAS;gBACT,WAAW,cAAc,SAAS;gBAClC,eAAe,cAAc,OAAO;gBACpC;gBACA,YAAY,cAAc,SAAS;YACrC;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,kBAAkB,CAAC;IACvB,MAAM,YAAgC,EAAE;IAExC,mBAAmB;IACnB,MAAM,UAAU,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;IAC1C,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM;IAC7E,MAAM,SAAS,KAAK,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,WAAW,IAAI,KAAK,QAAQ,MAAM;IAE7G,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,SAAS,IAAI;QACvD,IAAI,SAAS,GAAG;YACd,UAAU,IAAI,CAAC;gBACb,WAAW;gBACX,MAAM;gBACN,aAAa,CAAC,UAAU,EAAE,QAAQ,MAAM,GAAG,YAAY,SAAS,MAAM,aAAa,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,QAAQ,QAAQ,EAAE;gBACvI,UAAU,SAAS,IAAI,SAAS;gBAChC,eAAe;gBACf,aAAa,QAAQ,MAAM;YAC7B;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,0BAA0B;IACpD,UAAU,IAAI,IAAI;IAElB,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,gBAAgB,CAAC;IACrB,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA;QAChC,MAAM,cAAc,IAAI,KAAK,EAAE,IAAI;QACnC,OAAO,YAAY,QAAQ,OAAO,IAAI,QAAQ,MAAM,YAAY,WAAW,OAAO,IAAI,WAAW;IACnG;IAEA,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA;QAChC,MAAM,cAAc,IAAI,KAAK,EAAE,IAAI;QACnC,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QACrC,OAAO,YAAY,QAAQ,OAAO,cAAc,QAAQ,MAAM,YAAY,WAAW,OAAO,cAAc,WAAW;IACvH;IAEA,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IACpE,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEpE,MAAM,gBAAgB,iBAAiB,IAAI,AAAC,CAAC,iBAAiB,cAAc,IAAI,iBAAkB,MAAM;IAExG,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA;;CAEC,GACD,MAAM,2BAA2B,CAAC;IAChC,MAAM,WAA8B,EAAE;IACtC,MAAM,iBAAiB,gBAAgB;IACvC,MAAM,gBAAgB,iBAAiB;IAEvC,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,UAAU,iBAAiB;QAClE,MAAM,gBAAgB,iBAAiB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAC1E,MAAM,aAAa,AAAC,gBAAgB,gBAAiB;QAErD,IAAI,aAAa,IAAI;YACnB,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO,CAAC,KAAK,EAAE,SAAS,SAAS,CAAC;gBAClC,aAAa,GAAG,SAAS,cAAc,EAAE,WAAW,OAAO,CAAC,GAAG,2BAA2B,EAAE,cAAc,OAAO,CAAC,GAAG,gDAAgD,CAAC;gBACtK,UAAU;gBACV;gBACA,QAAQ;YACV;QACF;QAEA,kCAAkC;QAClC,MAAM,iBAAiB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG;QAC/D,IAAI,eAAe,MAAM,GAAG,IAAI;YAC9B,MAAM,aAAa,eAAe,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YACrE,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO,CAAC,eAAe,EAAE,SAAS,UAAU,CAAC;gBAC7C,aAAa,CAAC,SAAS,EAAE,eAAe,MAAM,CAAC,oBAAoB,EAAE,SAAS,WAAW,EAAE,WAAW,OAAO,CAAC,GAAG,yBAAyB,CAAC;gBAC3I,UAAU;gBACV;gBACA,QAAQ;YACV;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,6BAA6B,CAAC;IAClC,MAAM,WAA8B,EAAE;IAEtC,kDAAkD;IAClD,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,KAAK,GAAG,GAAG,OAAO;IAC9E,MAAM,aAAa,IAAI,OAAO;IAE9B,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA;QACxC,MAAM,cAAc,IAAI,KAAK,EAAE,IAAI;QACnC,OAAO,YAAY,QAAQ,OAAO,IAAI,QAAQ,MAAM,YAAY,WAAW,OAAO,IAAI,WAAW;IACnG;IAEA,MAAM,uBAAuB,kBAAkB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAClF,MAAM,eAAe,uBAAuB;IAC5C,MAAM,2BAA2B,eAAe;IAEhD,IAAI,aAAa,GAAG;QAClB,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,OAAO;YACP,aAAa,CAAC,mEAAmE,EAAE,yBAAyB,OAAO,CAAC,GAAG,YAAY,CAAC;YACpI,UAAU;YACV,QAAQ;QACV;IACF;IAEA,OAAO;AACT;AAEA,mBAAmB;AACnB,MAAM,kBAAkB,CAAC;IACvB,OAAO,SAAS,MAAM,CAAC,CAAC,QAAQ;QAC9B,MAAM,WAAW,QAAQ,QAAQ;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACrB,MAAM,CAAC,SAAS,GAAG,EAAE;QACvB;QACA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;AACN;AAEA,MAAM,mBAAmB,CAAC;IACxB,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;AACjE;AAEA,MAAM,uBAAuB,CAAC;IAC5B,qCAAqC;IACrC,MAAM,eAAyB,EAAE;IACjC,wDAAwD;IACxD,OAAO;QAAE,WAAW;QAAK,SAAS;IAAG,GAAG,cAAc;AACxD;AAEA,MAAM,sBAAsB,CAAC;IAC3B,oCAAoC;IACpC,OAAO;QAAE,WAAW;QAAK,SAAS;IAAG,GAAG,cAAc;AACxD;AAEA,MAAM,4BAA4B,CAAC;IACjC,yCAAyC;IACzC,OAAO,EAAE,EAAE,cAAc;AAC3B", "debugId": null}}, {"offset": {"line": 1867, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/InsightsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { Brain, TrendingUp, AlertTriangle, CheckCircle, Info, Lightbulb, Zap } from 'lucide-react';\nimport { generateSpendingInsights, type SpendingInsight } from '@/lib/ai-insights';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\ninterface InsightsPanelProps {\n  expenses: Expense[];\n}\n\nexport default function InsightsPanel({ expenses }: InsightsPanelProps) {\n  const insights = useMemo(() => {\n    return generateSpendingInsights(expenses);\n  }, [expenses]);\n\n  const getInsightIcon = (type: SpendingInsight['type']) => {\n    switch (type) {\n      case 'alert':\n        return <AlertTriangle className=\"h-5 w-5 text-red-500\" />;\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />;\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />;\n      default:\n        return <Lightbulb className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getInsightBorderColor = (type: SpendingInsight['type']) => {\n    switch (type) {\n      case 'alert':\n        return 'border-l-red-500 bg-red-50';\n      case 'warning':\n        return 'border-l-yellow-500 bg-yellow-50';\n      case 'success':\n        return 'border-l-green-500 bg-green-50';\n      case 'info':\n        return 'border-l-blue-500 bg-blue-50';\n      default:\n        return 'border-l-gray-500 bg-gray-50';\n    }\n  };\n\n  const getPriorityBadge = (priority: SpendingInsight['priority']) => {\n    const colors = {\n      high: 'bg-red-100 text-red-800',\n      medium: 'bg-yellow-100 text-yellow-800',\n      low: 'bg-blue-100 text-blue-800'\n    };\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[priority]}`}>\n        {priority.charAt(0).toUpperCase() + priority.slice(1)}\n      </span>\n    );\n  };\n\n  const highPriorityInsights = insights.filter(insight => insight.priority === 'high');\n  const otherInsights = insights.filter(insight => insight.priority !== 'high');\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-3\">\n        <div className=\"p-2 bg-purple-100 rounded-lg\">\n          <Brain className=\"h-6 w-6 text-purple-600\" />\n        </div>\n        <div>\n          <h2 className=\"text-xl font-semibold text-gray-900\">AI Insights</h2>\n          <p className=\"text-sm text-gray-500\">Personalized spending analysis and recommendations</p>\n        </div>\n      </div>\n\n      {/* High Priority Alerts */}\n      {highPriorityInsights.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm border border-red-200\">\n          <div className=\"px-4 py-3 border-b border-red-200 bg-red-50\">\n            <div className=\"flex items-center space-x-2\">\n              <Zap className=\"h-4 w-4 text-red-600\" />\n              <h3 className=\"text-sm font-medium text-red-900\">Urgent Attention Required</h3>\n            </div>\n          </div>\n          <div className=\"p-4 space-y-3\">\n            {highPriorityInsights.map((insight, index) => (\n              <InsightCard key={index} insight={insight} />\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* All Insights */}\n      <div className=\"bg-white rounded-lg shadow-sm\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Smart Recommendations</h3>\n            <span className=\"text-sm text-gray-500\">{insights.length} insights</span>\n          </div>\n        </div>\n        <div className=\"divide-y divide-gray-200\">\n          {insights.length === 0 ? (\n            <div className=\"p-8 text-center\">\n              <Brain className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n              <p className=\"text-gray-500\">Add more expenses to get AI-powered insights</p>\n            </div>\n          ) : (\n            insights.map((insight, index) => (\n              <div key={index} className=\"p-6\">\n                <InsightCard insight={insight} />\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n\n      {/* Insights Summary */}\n      {insights.length > 0 && (\n        <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6\">\n          <div className=\"flex items-start space-x-4\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <TrendingUp className=\"h-5 w-5 text-purple-600\" />\n            </div>\n            <div className=\"flex-1\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Insights Summary</h4>\n              <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-500\">High Priority:</span>\n                  <span className=\"ml-2 font-medium text-red-600\">\n                    {insights.filter(i => i.priority === 'high').length}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-500\">Opportunities:</span>\n                  <span className=\"ml-2 font-medium text-green-600\">\n                    {insights.filter(i => i.type === 'success').length}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-500\">Recommendations:</span>\n                  <span className=\"ml-2 font-medium text-blue-600\">\n                    {insights.filter(i => i.action).length}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\ninterface InsightCardProps {\n  insight: SpendingInsight;\n}\n\nfunction InsightCard({ insight }: InsightCardProps) {\n  const getInsightIcon = (type: SpendingInsight['type']) => {\n    switch (type) {\n      case 'alert':\n        return <AlertTriangle className=\"h-5 w-5 text-red-500\" />;\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />;\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />;\n      default:\n        return <Lightbulb className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getInsightBorderColor = (type: SpendingInsight['type']) => {\n    switch (type) {\n      case 'alert':\n        return 'border-l-red-500 bg-red-50';\n      case 'warning':\n        return 'border-l-yellow-500 bg-yellow-50';\n      case 'success':\n        return 'border-l-green-500 bg-green-50';\n      case 'info':\n        return 'border-l-blue-500 bg-blue-50';\n      default:\n        return 'border-l-gray-500 bg-gray-50';\n    }\n  };\n\n  const getPriorityBadge = (priority: SpendingInsight['priority']) => {\n    const colors = {\n      high: 'bg-red-100 text-red-800',\n      medium: 'bg-yellow-100 text-yellow-800',\n      low: 'bg-blue-100 text-blue-800'\n    };\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[priority]}`}>\n        {priority.charAt(0).toUpperCase() + priority.slice(1)}\n      </span>\n    );\n  };\n\n  return (\n    <div className={`border-l-4 p-4 rounded-r-lg ${getInsightBorderColor(insight.type)}`}>\n      <div className=\"flex items-start space-x-3\">\n        <div className=\"flex-shrink-0 mt-0.5\">\n          {getInsightIcon(insight.type)}\n        </div>\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center justify-between mb-1\">\n            <h4 className=\"text-sm font-medium text-gray-900\">{insight.title}</h4>\n            {getPriorityBadge(insight.priority)}\n          </div>\n          <p className=\"text-sm text-gray-700 mb-2\">{insight.description}</p>\n          \n          {insight.action && (\n            <div className=\"mt-3 p-3 bg-white rounded-md border border-gray-200\">\n              <div className=\"flex items-start space-x-2\">\n                <Lightbulb className=\"h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0\" />\n                <p className=\"text-xs text-gray-600\">{insight.action}</p>\n              </div>\n            </div>\n          )}\n          \n          {(insight.category || insight.amount) && (\n            <div className=\"mt-2 flex items-center space-x-4 text-xs text-gray-500\">\n              {insight.category && (\n                <span className=\"inline-flex items-center\">\n                  Category: <span className=\"ml-1 font-medium\">{insight.category}</span>\n                </span>\n              )}\n              {insight.amount && (\n                <span className=\"inline-flex items-center\">\n                  Amount: <span className=\"ml-1 font-medium\">${insight.amount.toFixed(2)}</span>\n                </span>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAkBe,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,CAAA,GAAA,4HAAA,CAAA,2BAAwB,AAAD,EAAE;IAClC,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBA<PERSON>,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,4MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,MAAM,CAAC,SAAS,EAAE;sBAC3G,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;;;;;;IAGzD;IAEA,MAAM,uBAAuB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC7E,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEtE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAKxC,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAGrD,8OAAC;wBAAI,WAAU;kCACZ,qBAAqB,GAAG,CAAC,CAAC,SAAS,sBAClC,8OAAC;gCAAwB,SAAS;+BAAhB;;;;;;;;;;;;;;;;0BAO1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAK,WAAU;;wCAAyB,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAG7D,8OAAC;wBAAI,WAAU;kCACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;mCAG/B,SAAS,GAAG,CAAC,CAAC,SAAS,sBACrB,8OAAC;gCAAgB,WAAU;0CACzB,cAAA,8OAAC;oCAAY,SAAS;;;;;;+BADd;;;;;;;;;;;;;;;;YASjB,SAAS,MAAM,GAAG,mBACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAExB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;;;;;;;;;;;;sDAGvD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;;;;;;;;;;;;sDAGtD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D;AAMA,SAAS,YAAY,EAAE,OAAO,EAAoB;IAChD,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,4MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,MAAM,CAAC,SAAS,EAAE;sBAC3G,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;;;;;;IAGzD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,sBAAsB,QAAQ,IAAI,GAAG;kBAClF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,eAAe,QAAQ,IAAI;;;;;;8BAE9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC,QAAQ,KAAK;;;;;;gCAC/D,iBAAiB,QAAQ,QAAQ;;;;;;;sCAEpC,8OAAC;4BAAE,WAAU;sCAA8B,QAAQ,WAAW;;;;;;wBAE7D,QAAQ,MAAM,kBACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAE,WAAU;kDAAyB,QAAQ,MAAM;;;;;;;;;;;;;;;;;wBAKzD,CAAC,QAAQ,QAAQ,IAAI,QAAQ,MAAM,mBAClC,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,QAAQ,kBACf,8OAAC;oCAAK,WAAU;;wCAA2B;sDAC/B,8OAAC;4CAAK,WAAU;sDAAoB,QAAQ,QAAQ;;;;;;;;;;;;gCAGjE,QAAQ,MAAM,kBACb,8OAAC;oCAAK,WAAU;;wCAA2B;sDACjC,8OAAC;4CAAK,WAAU;;gDAAmB;gDAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStF", "debugId": null}}, {"offset": {"line": 2510, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { TrendingUp, TrendingDown, DollarSign, ShoppingCart, Calendar, Target } from 'lucide-react';\nimport { SpendingTrendChart, CategoryBreakdownChart, MonthlyComparisonChart } from './Charts';\nimport InsightsPanel from './InsightsPanel';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\ninterface DashboardProps {\n  expenses: Expense[];\n}\n\nexport default function Dashboard({ expenses }: DashboardProps) {\n  // Calculate analytics\n  const analytics = useMemo(() => {\n    const now = new Date();\n    const thisMonth = now.getMonth();\n    const thisYear = now.getFullYear();\n    const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;\n    const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;\n\n    // This month expenses\n    const thisMonthExpenses = expenses.filter(expense => {\n      const expenseDate = new Date(expense.date);\n      return expenseDate.getMonth() === thisMonth && expenseDate.getFullYear() === thisYear;\n    });\n\n    // Last month expenses\n    const lastMonthExpenses = expenses.filter(expense => {\n      const expenseDate = new Date(expense.date);\n      return expenseDate.getMonth() === lastMonth && expenseDate.getFullYear() === lastMonthYear;\n    });\n\n    // Today's expenses\n    const today = now.toDateString();\n    const todayExpenses = expenses.filter(expense => \n      new Date(expense.date).toDateString() === today\n    );\n\n    // This week expenses\n    const weekStart = new Date(now);\n    weekStart.setDate(now.getDate() - now.getDay());\n    const thisWeekExpenses = expenses.filter(expense => {\n      const expenseDate = new Date(expense.date);\n      return expenseDate >= weekStart;\n    });\n\n    // Category breakdown\n    const categoryTotals = expenses.reduce((acc, expense) => {\n      acc[expense.category] = (acc[expense.category] || 0) + expense.amount;\n      return acc;\n    }, {} as Record<string, number>);\n\n    // Spending trend (last 30 days)\n    const last30Days = Array.from({ length: 30 }, (_, i) => {\n      const date = new Date();\n      date.setDate(date.getDate() - (29 - i));\n      return date.toDateString();\n    });\n\n    const dailySpending = last30Days.map(dateStr => {\n      const dayExpenses = expenses.filter(expense => \n        new Date(expense.date).toDateString() === dateStr\n      );\n      return dayExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    });\n\n    // Calculate totals\n    const thisMonthTotal = thisMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    const lastMonthTotal = lastMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    const todayTotal = todayExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    const thisWeekTotal = thisWeekExpenses.reduce((sum, expense) => sum + expense.amount, 0);\n    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);\n\n    // Calculate changes\n    const monthlyChange = lastMonthTotal > 0 ? ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100 : 0;\n    const avgDailySpending = thisMonthExpenses.length > 0 ? thisMonthTotal / new Date().getDate() : 0;\n\n    return {\n      thisMonthTotal,\n      lastMonthTotal,\n      todayTotal,\n      thisWeekTotal,\n      totalExpenses,\n      monthlyChange,\n      avgDailySpending,\n      categoryTotals,\n      dailySpending,\n      last30Days,\n      expenseCount: expenses.length,\n      avgExpenseAmount: expenses.length > 0 ? totalExpenses / expenses.length : 0\n    };\n  }, [expenses]);\n\n  // Prepare chart data\n  const spendingTrendData = {\n    labels: analytics.last30Days.map(date => {\n      const d = new Date(date);\n      return `${d.getMonth() + 1}/${d.getDate()}`;\n    }),\n    amounts: analytics.dailySpending\n  };\n\n  const categoryData = Object.entries(analytics.categoryTotals).map(([category, amount]) => ({\n    category,\n    amount,\n    color: getCategoryColor(category)\n  }));\n\n  const monthlyComparisonData = [\n    {\n      month: 'Last Month',\n      currentYear: analytics.lastMonthTotal,\n      previousYear: 0 // Could calculate year-over-year if we had more data\n    },\n    {\n      month: 'This Month',\n      currentYear: analytics.thisMonthTotal,\n      previousYear: 0\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <MetricCard\n          title=\"This Month\"\n          value={`$${analytics.thisMonthTotal.toFixed(2)}`}\n          change={analytics.monthlyChange}\n          icon={<DollarSign className=\"h-6 w-6\" />}\n          color=\"blue\"\n        />\n        <MetricCard\n          title=\"This Week\"\n          value={`$${analytics.thisWeekTotal.toFixed(2)}`}\n          subtitle={`${Math.ceil(analytics.thisWeekTotal / 7)} avg/day`}\n          icon={<Calendar className=\"h-6 w-6\" />}\n          color=\"green\"\n        />\n        <MetricCard\n          title=\"Today\"\n          value={`$${analytics.todayTotal.toFixed(2)}`}\n          subtitle={`vs $${analytics.avgDailySpending.toFixed(2)} avg`}\n          icon={<ShoppingCart className=\"h-6 w-6\" />}\n          color=\"purple\"\n        />\n        <MetricCard\n          title=\"Total Expenses\"\n          value={analytics.expenseCount.toString()}\n          subtitle={`$${analytics.avgExpenseAmount.toFixed(2)} avg`}\n          icon={<Target className=\"h-6 w-6\" />}\n          color=\"orange\"\n        />\n      </div>\n\n      {/* AI Insights */}\n      <InsightsPanel expenses={expenses} />\n\n      {/* Charts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Spending Trend (30 Days)</h3>\n          <SpendingTrendChart data={spendingTrendData} />\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Category Breakdown</h3>\n          {categoryData.length > 0 ? (\n            <CategoryBreakdownChart data={categoryData} />\n          ) : (\n            <div className=\"h-64 flex items-center justify-center text-gray-500\">\n              No expense data available\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Monthly Comparison */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Monthly Comparison</h3>\n        <MonthlyComparisonChart data={monthlyComparisonData} />\n      </div>\n\n      {/* Top Categories */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Top Spending Categories</h3>\n        <div className=\"space-y-4\">\n          {Object.entries(analytics.categoryTotals)\n            .sort(([,a], [,b]) => b - a)\n            .slice(0, 5)\n            .map(([category, amount]) => {\n              const percentage = (amount / analytics.totalExpenses) * 100;\n              return (\n                <div key={category} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div \n                      className=\"w-4 h-4 rounded-full\"\n                      style={{ backgroundColor: getCategoryColor(category) }}\n                    />\n                    <span className=\"text-sm font-medium text-gray-900\">{category}</span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      ${amount.toFixed(2)}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      {percentage.toFixed(1)}%\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface MetricCardProps {\n  title: string;\n  value: string;\n  change?: number;\n  subtitle?: string;\n  icon: React.ReactNode;\n  color: 'blue' | 'green' | 'purple' | 'orange';\n}\n\nfunction MetricCard({ title, value, change, subtitle, icon, color }: MetricCardProps) {\n  const colorClasses = {\n    blue: 'text-blue-600 bg-blue-100',\n    green: 'text-green-600 bg-green-100',\n    purple: 'text-purple-600 bg-purple-100',\n    orange: 'text-orange-600 bg-orange-100'\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-500\">{title}</p>\n          <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{value}</p>\n          {change !== undefined && (\n            <div className=\"flex items-center mt-2\">\n              {change >= 0 ? (\n                <TrendingUp className=\"h-4 w-4 text-green-500 mr-1\" />\n              ) : (\n                <TrendingDown className=\"h-4 w-4 text-red-500 mr-1\" />\n              )}\n              <span className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {Math.abs(change).toFixed(1)}%\n              </span>\n            </div>\n          )}\n          {subtitle && (\n            <p className=\"text-xs text-gray-500 mt-1\">{subtitle}</p>\n          )}\n        </div>\n        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>\n          {icon}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction getCategoryColor(category: string): string {\n  const colors: Record<string, string> = {\n    'Food & Dining': '#EF4444',\n    'Transportation': '#3B82F6',\n    'Shopping': '#8B5CF6',\n    'Entertainment': '#F59E0B',\n    'Bills & Utilities': '#10B981',\n    'Healthcare': '#EC4899',\n    'Education': '#6366F1',\n    'Travel': '#14B8A6',\n    'Other': '#6B7280'\n  };\n  return colors[category] || '#6B7280';\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAmBe,SAAS,UAAU,EAAE,QAAQ,EAAkB;IAC5D,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,IAAI,QAAQ;QAC9B,MAAM,WAAW,IAAI,WAAW;QAChC,MAAM,YAAY,cAAc,IAAI,KAAK,YAAY;QACrD,MAAM,gBAAgB,cAAc,IAAI,WAAW,IAAI;QAEvD,sBAAsB;QACtB,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA;YACxC,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;YACzC,OAAO,YAAY,QAAQ,OAAO,aAAa,YAAY,WAAW,OAAO;QAC/E;QAEA,sBAAsB;QACtB,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA;YACxC,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;YACzC,OAAO,YAAY,QAAQ,OAAO,aAAa,YAAY,WAAW,OAAO;QAC/E;QAEA,mBAAmB;QACnB,MAAM,QAAQ,IAAI,YAAY;QAC9B,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UACpC,IAAI,KAAK,QAAQ,IAAI,EAAE,YAAY,OAAO;QAG5C,qBAAqB;QACrB,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,IAAI,OAAO,KAAK,IAAI,MAAM;QAC5C,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;YACvC,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;YACzC,OAAO,eAAe;QACxB;QAEA,qBAAqB;QACrB,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK;YAC3C,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,MAAM;YACrE,OAAO;QACT,GAAG,CAAC;QAEJ,gCAAgC;QAChC,MAAM,aAAa,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,CAAC,GAAG;YAChD,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,CAAC,KAAK,CAAC;YACrC,OAAO,KAAK,YAAY;QAC1B;QAEA,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA;YACnC,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,UAClC,IAAI,KAAK,QAAQ,IAAI,EAAE,YAAY,OAAO;YAE5C,OAAO,YAAY,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QACpE;QAEA,mBAAmB;QACnB,MAAM,iBAAiB,kBAAkB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QACxF,MAAM,iBAAiB,kBAAkB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QACxF,MAAM,aAAa,cAAc,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QAChF,MAAM,gBAAgB,iBAAiB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QACtF,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QAE9E,oBAAoB;QACpB,MAAM,gBAAgB,iBAAiB,IAAI,AAAC,CAAC,iBAAiB,cAAc,IAAI,iBAAkB,MAAM;QACxG,MAAM,mBAAmB,kBAAkB,MAAM,GAAG,IAAI,iBAAiB,IAAI,OAAO,OAAO,KAAK;QAEhG,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,cAAc,SAAS,MAAM;YAC7B,kBAAkB,SAAS,MAAM,GAAG,IAAI,gBAAgB,SAAS,MAAM,GAAG;QAC5E;IACF,GAAG;QAAC;KAAS;IAEb,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ,UAAU,UAAU,CAAC,GAAG,CAAC,CAAA;YAC/B,MAAM,IAAI,IAAI,KAAK;YACnB,OAAO,GAAG,EAAE,QAAQ,KAAK,EAAE,CAAC,EAAE,EAAE,OAAO,IAAI;QAC7C;QACA,SAAS,UAAU,aAAa;IAClC;IAEA,MAAM,eAAe,OAAO,OAAO,CAAC,UAAU,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,GAAK,CAAC;YACzF;YACA;YACA,OAAO,iBAAiB;QAC1B,CAAC;IAED,MAAM,wBAAwB;QAC5B;YACE,OAAO;YACP,aAAa,UAAU,cAAc;YACrC,cAAc,EAAE,qDAAqD;QACvE;QACA;YACE,OAAO;YACP,aAAa,UAAU,cAAc;YACrC,cAAc;QAChB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAC,CAAC,EAAE,UAAU,cAAc,CAAC,OAAO,CAAC,IAAI;wBAChD,QAAQ,UAAU,aAAa;wBAC/B,oBAAM,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBAC5B,OAAM;;;;;;kCAER,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAC,CAAC,EAAE,UAAU,aAAa,CAAC,OAAO,CAAC,IAAI;wBAC/C,UAAU,GAAG,KAAK,IAAI,CAAC,UAAU,aAAa,GAAG,GAAG,QAAQ,CAAC;wBAC7D,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAM;;;;;;kCAER,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,OAAO,CAAC,IAAI;wBAC5C,UAAU,CAAC,IAAI,EAAE,UAAU,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;wBAC5D,oBAAM,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC9B,OAAM;;;;;;kCAER,8OAAC;wBACC,OAAM;wBACN,OAAO,UAAU,YAAY,CAAC,QAAQ;wBACtC,UAAU,CAAC,CAAC,EAAE,UAAU,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;wBACzD,oBAAM,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACxB,OAAM;;;;;;;;;;;;0BAKV,8OAAC,mIAAA,CAAA,UAAa;gBAAC,UAAU;;;;;;0BAGzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,4HAAA,CAAA,qBAAkB;gCAAC,MAAM;;;;;;;;;;;;kCAG5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;4BACtD,aAAa,MAAM,GAAG,kBACrB,8OAAC,4HAAA,CAAA,yBAAsB;gCAAC,MAAM;;;;;qDAE9B,8OAAC;gCAAI,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;0BAQ3E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC,4HAAA,CAAA,yBAAsB;wBAAC,MAAM;;;;;;;;;;;;0BAIhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,UAAU,cAAc,EACrC,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,IAAI,GACzB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO;4BACtB,MAAM,aAAa,AAAC,SAAS,UAAU,aAAa,GAAI;4BACxD,qBACE,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,iBAAiB;gDAAU;;;;;;0DAEvD,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAoC;oDAC/C,OAAO,OAAO,CAAC;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;;oDACZ,WAAW,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;+BAbnB;;;;;wBAkBd;;;;;;;;;;;;;;;;;;AAKZ;AAWA,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAmB;IAClF,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAA6C;;;;;;wBACzD,WAAW,2BACV,8OAAC;4BAAI,WAAU;;gCACZ,UAAU,kBACT,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;yDAEtB,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CAE1B,8OAAC;oCAAK,WAAW,CAAC,QAAQ,EAAE,UAAU,IAAI,mBAAmB,gBAAgB;;wCAC1E,KAAK,GAAG,CAAC,QAAQ,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAIlC,0BACC,8OAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;8BAG/C,8OAAC;oBAAI,WAAW,CAAC,eAAe,EAAE,YAAY,CAAC,MAAM,EAAE;8BACpD;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,iBAAiB,QAAgB;IACxC,MAAM,SAAiC;QACrC,iBAAiB;QACjB,kBAAkB;QAClB,YAAY;QACZ,iBAAiB;QACjB,qBAAqB;QACrB,cAAc;QACd,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA,OAAO,MAAM,CAAC,SAAS,IAAI;AAC7B", "debugId": null}}, {"offset": {"line": 3017, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/CameraCapture.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useCallback } from 'react';\nimport { Camera, X, RotateCcw, Check, Zap } from 'lucide-react';\n\ninterface CameraCaptureProps {\n  onCapture: (file: File) => void;\n  onClose: () => void;\n}\n\nexport default function CameraCapture({ onCapture, onClose }: CameraCaptureProps) {\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [capturedImage, setCapturedImage] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const streamRef = useRef<MediaStream | null>(null);\n\n  const startCamera = useCallback(async () => {\n    try {\n      setError(null);\n      \n      // Request camera access with optimal settings for document capture\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment', // Use back camera on mobile\n          width: { ideal: 1920 },\n          height: { ideal: 1080 },\n          aspectRatio: { ideal: 16/9 }\n        }\n      });\n\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        streamRef.current = stream;\n        setIsStreaming(true);\n      }\n    } catch (err) {\n      console.error('Camera access error:', err);\n      setError('Unable to access camera. Please check permissions and try again.');\n    }\n  }, []);\n\n  const stopCamera = useCallback(() => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n      streamRef.current = null;\n    }\n    setIsStreaming(false);\n  }, []);\n\n  const capturePhoto = useCallback(() => {\n    if (!videoRef.current || !canvasRef.current) return;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const context = canvas.getContext('2d');\n\n    if (!context) return;\n\n    // Set canvas dimensions to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw the video frame to canvas\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Apply image enhancements for better OCR\n    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);\n    enhanceImageForOCR(imageData);\n    context.putImageData(imageData, 0, 0);\n\n    // Convert to data URL\n    const dataURL = canvas.toDataURL('image/jpeg', 0.9);\n    setCapturedImage(dataURL);\n    stopCamera();\n  }, [stopCamera]);\n\n  const retakePhoto = useCallback(() => {\n    setCapturedImage(null);\n    startCamera();\n  }, [startCamera]);\n\n  const confirmCapture = useCallback(() => {\n    if (!capturedImage) return;\n\n    // Convert data URL to File\n    fetch(capturedImage)\n      .then(res => res.blob())\n      .then(blob => {\n        const file = new File([blob], `receipt-${Date.now()}.jpg`, { type: 'image/jpeg' });\n        onCapture(file);\n        onClose();\n      })\n      .catch(err => {\n        console.error('Error converting image:', err);\n        setError('Failed to process captured image');\n      });\n  }, [capturedImage, onCapture, onClose]);\n\n  // Cleanup on unmount\n  React.useEffect(() => {\n    return () => {\n      stopCamera();\n    };\n  }, [stopCamera]);\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 flex flex-col\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 bg-black text-white\">\n        <div className=\"flex items-center space-x-3\">\n          <Camera className=\"h-6 w-6\" />\n          <div>\n            <h2 className=\"text-lg font-semibold\">Capture Receipt</h2>\n            <p className=\"text-sm text-gray-300\">Position receipt in frame and tap capture</p>\n          </div>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"p-2 rounded-full bg-gray-800 hover:bg-gray-700\"\n        >\n          <X className=\"h-6 w-6\" />\n        </button>\n      </div>\n\n      {/* Camera View */}\n      <div className=\"flex-1 relative overflow-hidden\">\n        {error && (\n          <div className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 z-10\">\n            <div className=\"bg-red-600 text-white p-6 rounded-lg max-w-sm mx-4 text-center\">\n              <p className=\"mb-4\">{error}</p>\n              <button\n                onClick={() => setError(null)}\n                className=\"bg-white text-red-600 px-4 py-2 rounded-lg font-medium\"\n              >\n                Try Again\n              </button>\n            </div>\n          </div>\n        )}\n\n        {capturedImage ? (\n          // Show captured image\n          <div className=\"relative w-full h-full flex items-center justify-center bg-black\">\n            <img\n              src={capturedImage}\n              alt=\"Captured receipt\"\n              className=\"max-w-full max-h-full object-contain\"\n            />\n            \n            {/* Overlay with enhancement indicator */}\n            <div className=\"absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm flex items-center space-x-2\">\n              <Zap className=\"h-4 w-4\" />\n              <span>Enhanced for OCR</span>\n            </div>\n          </div>\n        ) : (\n          // Show camera stream\n          <div className=\"relative w-full h-full\">\n            <video\n              ref={videoRef}\n              autoPlay\n              playsInline\n              muted\n              className=\"w-full h-full object-cover\"\n              onLoadedMetadata={startCamera}\n            />\n            \n            {/* Camera overlay guides */}\n            <div className=\"absolute inset-0 pointer-events-none\">\n              {/* Corner guides */}\n              <div className=\"absolute top-8 left-8 w-8 h-8 border-l-2 border-t-2 border-white opacity-75\"></div>\n              <div className=\"absolute top-8 right-8 w-8 h-8 border-r-2 border-t-2 border-white opacity-75\"></div>\n              <div className=\"absolute bottom-8 left-8 w-8 h-8 border-l-2 border-b-2 border-white opacity-75\"></div>\n              <div className=\"absolute bottom-8 right-8 w-8 h-8 border-r-2 border-b-2 border-white opacity-75\"></div>\n              \n              {/* Center guide */}\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"border-2 border-dashed border-white opacity-50 w-80 h-60 rounded-lg\"></div>\n              </div>\n              \n              {/* Instructions */}\n              <div className=\"absolute bottom-32 left-0 right-0 text-center\">\n                <div className=\"bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg mx-4\">\n                  <p className=\"text-sm\">Position receipt within the frame</p>\n                  <p className=\"text-xs text-gray-300 mt-1\">Ensure good lighting and avoid shadows</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Hidden canvas for image processing */}\n        <canvas ref={canvasRef} className=\"hidden\" />\n      </div>\n\n      {/* Controls */}\n      <div className=\"bg-black p-6\">\n        {capturedImage ? (\n          // Captured image controls\n          <div className=\"flex items-center justify-center space-x-6\">\n            <button\n              onClick={retakePhoto}\n              className=\"flex items-center space-x-2 bg-gray-700 text-white px-6 py-3 rounded-full hover:bg-gray-600\"\n            >\n              <RotateCcw className=\"h-5 w-5\" />\n              <span>Retake</span>\n            </button>\n            <button\n              onClick={confirmCapture}\n              className=\"flex items-center space-x-2 bg-green-600 text-white px-8 py-3 rounded-full hover:bg-green-700\"\n            >\n              <Check className=\"h-5 w-5\" />\n              <span>Use Photo</span>\n            </button>\n          </div>\n        ) : (\n          // Camera controls\n          <div className=\"flex items-center justify-center\">\n            {isStreaming ? (\n              <button\n                onClick={capturePhoto}\n                className=\"w-20 h-20 bg-white rounded-full border-4 border-gray-300 hover:border-blue-500 transition-colors flex items-center justify-center\"\n              >\n                <div className=\"w-16 h-16 bg-white rounded-full shadow-lg\"></div>\n              </button>\n            ) : (\n              <button\n                onClick={startCamera}\n                className=\"flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-full hover:bg-blue-700\"\n              >\n                <Camera className=\"h-5 w-5\" />\n                <span>Start Camera</span>\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Enhance image for better OCR results\n */\nfunction enhanceImageForOCR(imageData: ImageData) {\n  const data = imageData.data;\n  \n  for (let i = 0; i < data.length; i += 4) {\n    // Convert to grayscale\n    const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);\n    \n    // Increase contrast\n    const contrast = 1.5;\n    const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255));\n    const enhanced = Math.min(255, Math.max(0, factor * (gray - 128) + 128));\n    \n    // Apply to all channels\n    data[i] = enhanced;     // Red\n    data[i + 1] = enhanced; // Green\n    data[i + 2] = enhanced; // Blue\n    // Alpha channel remains unchanged\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAUe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAE7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,SAAS;YAET,mEAAmE;YACnE,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBACL,YAAY;oBACZ,OAAO;wBAAE,OAAO;oBAAK;oBACrB,QAAQ;wBAAE,OAAO;oBAAK;oBACtB,aAAa;wBAAE,OAAO,KAAG;oBAAE;gBAC7B;YACF;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,UAAU,OAAO,GAAG;gBACpB,eAAe;YACjB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YACzD,UAAU,OAAO,GAAG;QACtB;QACA,eAAe;IACjB,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;QAE7C,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,UAAU,OAAO,UAAU,CAAC;QAElC,IAAI,CAAC,SAAS;QAEd,uCAAuC;QACvC,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,iCAAiC;QACjC,QAAQ,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE1D,0CAA0C;QAC1C,MAAM,YAAY,QAAQ,YAAY,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACxE,mBAAmB;QACnB,QAAQ,YAAY,CAAC,WAAW,GAAG;QAEnC,sBAAsB;QACtB,MAAM,UAAU,OAAO,SAAS,CAAC,cAAc;QAC/C,iBAAiB;QACjB;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,iBAAiB;QACjB;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,eAAe;QAEpB,2BAA2B;QAC3B,MAAM,eACH,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA;YACJ,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;gBAAE,MAAM;YAAa;YAChF,UAAU;YACV;QACF,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX;IACJ,GAAG;QAAC;QAAe;QAAW;KAAQ;IAEtC,qBAAqB;IACrB,MAAM,SAAS,CAAC;QACd,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAGzC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKjB,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAQ;;;;;;8CACrB,8OAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;oBAON,gBACC,sBAAsB;kCACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;;;;;;+BAIV,qBAAqB;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK;gCACL,QAAQ;gCACR,WAAW;gCACX,KAAK;gCACL,WAAU;gCACV,kBAAkB;;;;;;0CAIpB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDAGf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,8OAAC;wBAAO,KAAK;wBAAW,WAAU;;;;;;;;;;;;0BAIpC,8OAAC;gBAAI,WAAU;0BACZ,gBACC,0BAA0B;8BAC1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;2BAIV,kBAAkB;8BAClB,8OAAC;oBAAI,WAAU;8BACZ,4BACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;6CAGjB,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;AAEA;;CAEC,GACD,SAAS,mBAAmB,SAAoB;IAC9C,MAAM,OAAO,UAAU,IAAI;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;QACvC,uBAAuB;QACvB,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;QAEnF,oBAAoB;QACpB,MAAM,WAAW;QACjB,MAAM,SAAS,AAAC,MAAM,CAAC,WAAW,MAAM,GAAG,IAAK,CAAC,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC;QAC7E,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,IAAI;QAEnE,wBAAwB;QACxB,IAAI,CAAC,EAAE,GAAG,UAAc,MAAM;QAC9B,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,QAAQ;QAChC,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,OAAO;IAC/B,kCAAkC;IACpC;AACF", "debugId": null}}, {"offset": {"line": 3531, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Upload, Receipt, Plus, Trash2, BarChart3, Camera, Smartphone } from 'lucide-react';\nimport ReceiptUpload from '@/components/ReceiptUpload';\nimport Dashboard from '@/components/Dashboard';\nimport CameraCapture from '@/components/CameraCapture';\nimport ExportPanel from '@/components/ExportPanel';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\n// Sample data for demo\nconst sampleExpenses: Expense[] = [\n  {\n    id: '1',\n    merchant: 'Starbucks Coffee',\n    amount: 8.75,\n    date: new Date().toLocaleDateString(),\n    category: 'Food & Dining'\n  },\n  {\n    id: '2',\n    merchant: 'Shell Gas Station',\n    amount: 41.08,\n    date: new Date(Date.now() - 86400000).toLocaleDateString(), // Yesterday\n    category: 'Transportation'\n  },\n  {\n    id: '3',\n    merchant: 'Walmart Supercenter',\n    amount: 11.63,\n    date: new Date(Date.now() - 172800000).toLocaleDateString(), // 2 days ago\n    category: 'Shopping'\n  }\n];\n\nexport default function Home() {\n  const [expenses, setExpenses] = useState<Expense[]>([]);\n  const [showUpload, setShowUpload] = useState(false);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showCamera, setShowCamera] = useState(false);\n  const [activeTab, setActiveTab] = useState('dashboard');\n\n  // Load expenses from localStorage on mount\n  useEffect(() => {\n    const saved = localStorage.getItem('expenses');\n    if (saved) {\n      setExpenses(JSON.parse(saved));\n    } else {\n      // Use sample data if no saved data\n      setExpenses(sampleExpenses);\n    }\n  }, []);\n\n  // Save expenses to localStorage whenever expenses change\n  useEffect(() => {\n    if (expenses.length > 0) {\n      localStorage.setItem('expenses', JSON.stringify(expenses));\n    }\n  }, [expenses]);\n\n  const handleReceiptData = (data: any) => {\n    const newExpense: Expense = {\n      id: Date.now().toString(),\n      merchant: data.merchant,\n      amount: data.amount,\n      date: data.date ? data.date.toLocaleDateString() : new Date().toLocaleDateString(),\n      category: data.suggestedCategory,\n    };\n    setExpenses(prev => [newExpense, ...prev]);\n  };\n\n  const handleAddExpense = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const formData = new FormData(e.currentTarget);\n    const newExpense: Expense = {\n      id: Date.now().toString(),\n      merchant: formData.get('merchant') as string,\n      amount: parseFloat(formData.get('amount') as string),\n      date: formData.get('date') as string,\n      category: formData.get('category') as string,\n    };\n    setExpenses(prev => [newExpense, ...prev]);\n    setShowAddForm(false);\n    e.currentTarget.reset();\n  };\n\n  const deleteExpense = (id: string) => {\n    setExpenses(prev => prev.filter(expense => expense.id !== id));\n  };\n\n  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Receipt className=\"h-8 w-8 text-blue-600\" />\n              <h1 className=\"ml-2 text-xl font-bold text-gray-900\">Smart Expense Tracker</h1>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"hidden md:flex space-x-8\">\n              <button\n                onClick={() => setActiveTab('dashboard')}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === 'dashboard'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                <BarChart3 className=\"h-4 w-4 inline mr-2\" />\n                Dashboard\n              </button>\n              <button\n                onClick={() => setActiveTab('expenses')}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === 'expenses'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                <Receipt className=\"h-4 w-4 inline mr-2\" />\n                Expenses\n              </button>\n            </nav>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => setShowCamera(true)}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 shadow-sm\"\n              >\n                <Camera className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">Camera</span>\n              </button>\n              <button\n                onClick={() => setShowUpload(true)}\n                className=\"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 flex items-center space-x-2 shadow-sm\"\n              >\n                <Upload className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">Upload</span>\n              </button>\n              <button\n                onClick={() => setShowAddForm(true)}\n                className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2 shadow-sm\"\n              >\n                <Plus className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">Add Expense</span>\n              </button>\n              <button\n                onClick={() => {\n                  // Demo: Add a random sample expense\n                  const samples = [\n                    { merchant: 'McDonald\\'s', amount: 12.45, category: 'Food & Dining' },\n                    { merchant: 'Target', amount: 67.89, category: 'Shopping' },\n                    { merchant: 'Uber', amount: 15.30, category: 'Transportation' },\n                    { merchant: 'Netflix', amount: 15.99, category: 'Entertainment' },\n                    { merchant: 'Starbucks', amount: 8.75, category: 'Food & Dining' },\n                    { merchant: 'Shell', amount: 41.08, category: 'Transportation' },\n                    { merchant: 'Amazon', amount: 29.99, category: 'Shopping' },\n                    { merchant: 'Gym Membership', amount: 45.00, category: 'Healthcare' }\n                  ];\n                  const sample = samples[Math.floor(Math.random() * samples.length)];\n                  const newExpense: Expense = {\n                    id: Date.now().toString(),\n                    merchant: sample.merchant,\n                    amount: sample.amount,\n                    date: new Date().toLocaleDateString(),\n                    category: sample.category,\n                  };\n                  setExpenses(prev => [newExpense, ...prev]);\n                }}\n                className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center space-x-2 shadow-sm\"\n              >\n                <Receipt className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">Demo</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {activeTab === 'dashboard' && <Dashboard expenses={expenses} />}\n\n        {activeTab === 'expenses' && (\n          <div className=\"space-y-6\">\n            {/* Quick Summary */}\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Summary</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"text-center\">\n                  <p className=\"text-2xl font-bold text-gray-900\">${totalExpenses.toFixed(2)}</p>\n                  <p className=\"text-sm text-gray-500\">Total Expenses</p>\n                </div>\n                <div className=\"text-center\">\n                  <p className=\"text-2xl font-bold text-gray-900\">{expenses.length}</p>\n                  <p className=\"text-sm text-gray-500\">Transactions</p>\n                </div>\n                <div className=\"text-center\">\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    ${expenses.length > 0 ? (totalExpenses / expenses.length).toFixed(2) : '0.00'}\n                  </p>\n                  <p className=\"text-sm text-gray-500\">Average</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Expenses List */}\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n                <h3 className=\"text-lg font-medium text-gray-900\">All Expenses</h3>\n                {expenses.length > 0 && (\n                  <button\n                    onClick={() => {\n                      setExpenses([]);\n                      localStorage.removeItem('expenses');\n                    }}\n                    className=\"text-red-600 hover:text-red-800 text-sm\"\n                  >\n                    Clear All\n                  </button>\n                )}\n              </div>\n              <div className=\"divide-y divide-gray-200\">\n                {expenses.length === 0 ? (\n                  <div className=\"p-8 text-center text-gray-500\">\n                    <Receipt className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                    <p>No expenses yet. Start by scanning a receipt or adding an expense manually!</p>\n                  </div>\n                ) : (\n                  expenses.map((expense) => (\n                    <div key={expense.id} className=\"p-6 flex items-center justify-between hover:bg-gray-50\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center justify-between\">\n                          <h4 className=\"text-sm font-medium text-gray-900\">{expense.merchant}</h4>\n                          <p className=\"text-lg font-semibold text-gray-900\">${expense.amount.toFixed(2)}</p>\n                        </div>\n                        <div className=\"mt-1 flex items-center space-x-4 text-sm text-gray-500\">\n                          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                            {expense.category}\n                          </span>\n                          <span>•</span>\n                          <span>{expense.date}</span>\n                        </div>\n                      </div>\n                      <button\n                        onClick={() => deleteExpense(expense.id)}\n                        className=\"ml-4 text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50\"\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Camera Capture */}\n      {showCamera && (\n        <CameraCapture\n          onCapture={(file) => {\n            setShowCamera(false);\n            // Process the captured file\n            const reader = new FileReader();\n            reader.onload = () => {\n              // For demo, we'll simulate processing the captured image\n              const mockData = {\n                merchant: 'Camera Captured Receipt',\n                amount: Math.round((Math.random() * 50 + 10) * 100) / 100,\n                date: new Date(),\n                suggestedCategory: 'Food & Dining'\n              };\n              handleReceiptData(mockData);\n            };\n            reader.readAsDataURL(file);\n          }}\n          onClose={() => setShowCamera(false)}\n        />\n      )}\n\n      {/* Receipt Upload Modal */}\n      {showUpload && (\n        <ReceiptUpload\n          onDataExtracted={handleReceiptData}\n          onClose={() => setShowUpload(false)}\n        />\n      )}\n\n      {/* Add Expense Modal */}\n      {showAddForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Add Expense</h2>\n            <form onSubmit={handleAddExpense} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Merchant\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"merchant\"\n                  required\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Store name\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Amount\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"amount\"\n                  step=\"0.01\"\n                  required\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"0.00\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Date\n                </label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  required\n                  defaultValue={new Date().toISOString().split('T')[0]}\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category\n                </label>\n                <select\n                  name=\"category\"\n                  required\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"Food & Dining\">Food & Dining</option>\n                  <option value=\"Transportation\">Transportation</option>\n                  <option value=\"Shopping\">Shopping</option>\n                  <option value=\"Entertainment\">Entertainment</option>\n                  <option value=\"Bills & Utilities\">Bills & Utilities</option>\n                  <option value=\"Healthcare\">Healthcare</option>\n                  <option value=\"Education\">Education</option>\n                  <option value=\"Travel\">Travel</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div className=\"flex space-x-3 pt-4\">\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700\"\n                >\n                  Add Expense\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => setShowAddForm(false)}\n                  className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400\"\n                >\n                  Cancel\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAiBA,uBAAuB;AACvB,MAAM,iBAA4B;IAChC;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,MAAM,IAAI,OAAO,kBAAkB;QACnC,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,kBAAkB;QACxD,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,kBAAkB;QACzD,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,YAAY,KAAK,KAAK,CAAC;QACzB,OAAO;YACL,mCAAmC;YACnC,YAAY;QACd;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;QAClD;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,MAAM;YACnB,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,kBAAkB,KAAK,IAAI,OAAO,kBAAkB;YAChF,UAAU,KAAK,iBAAiB;QAClC;QACA,YAAY,CAAA,OAAQ;gBAAC;mBAAe;aAAK;IAC3C;IAEA,MAAM,mBAAmB,CAAC;QACxB,EAAE,cAAc;QAChB,MAAM,WAAW,IAAI,SAAS,EAAE,aAAa;QAC7C,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,SAAS,GAAG,CAAC;YACvB,QAAQ,WAAW,SAAS,GAAG,CAAC;YAChC,MAAM,SAAS,GAAG,CAAC;YACnB,UAAU,SAAS,GAAG,CAAC;QACzB;QACA,YAAY,CAAA,OAAQ;gBAAC;mBAAe;aAAK;QACzC,eAAe;QACf,EAAE,aAAa,CAAC,KAAK;IACvB;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC5D;IAEA,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;IAE9E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;;;;;;;0CAIvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,cACV,8BACA,qCACJ;;0DAEF,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG/C,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,8BACA,qCACJ;;0DAEF,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;0CAI/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAErC,8OAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAErC,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAErC,8OAAC;wCACC,SAAS;4CACP,oCAAoC;4CACpC,MAAM,UAAU;gDACd;oDAAE,UAAU;oDAAe,QAAQ;oDAAO,UAAU;gDAAgB;gDACpE;oDAAE,UAAU;oDAAU,QAAQ;oDAAO,UAAU;gDAAW;gDAC1D;oDAAE,UAAU;oDAAQ,QAAQ;oDAAO,UAAU;gDAAiB;gDAC9D;oDAAE,UAAU;oDAAW,QAAQ;oDAAO,UAAU;gDAAgB;gDAChE;oDAAE,UAAU;oDAAa,QAAQ;oDAAM,UAAU;gDAAgB;gDACjE;oDAAE,UAAU;oDAAS,QAAQ;oDAAO,UAAU;gDAAiB;gDAC/D;oDAAE,UAAU;oDAAU,QAAQ;oDAAO,UAAU;gDAAW;gDAC1D;oDAAE,UAAU;oDAAkB,QAAQ;oDAAO,UAAU;gDAAa;6CACrE;4CACD,MAAM,SAAS,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;4CAClE,MAAM,aAAsB;gDAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;gDACvB,UAAU,OAAO,QAAQ;gDACzB,QAAQ,OAAO,MAAM;gDACrB,MAAM,IAAI,OAAO,kBAAkB;gDACnC,UAAU,OAAO,QAAQ;4CAC3B;4CACA,YAAY,CAAA,OAAQ;oDAAC;uDAAe;iDAAK;wCAC3C;wCACA,WAAU;;0DAEV,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,8OAAC;gBAAK,WAAU;;oBACb,cAAc,6BAAe,8OAAC,+HAAA,CAAA,UAAS;wBAAC,UAAU;;;;;;oBAElD,cAAc,4BACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAAmC;4DAAE,cAAc,OAAO,CAAC;;;;;;;kEACxE,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC,SAAS,MAAM;;;;;;kEAChE,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAAmC;4DAC5C,SAAS,MAAM,GAAG,IAAI,CAAC,gBAAgB,SAAS,MAAM,EAAE,OAAO,CAAC,KAAK;;;;;;;kEAEzE,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;4CACjD,SAAS,MAAM,GAAG,mBACjB,8OAAC;gDACC,SAAS;oDACP,YAAY,EAAE;oDACd,aAAa,UAAU,CAAC;gDAC1B;gDACA,WAAU;0DACX;;;;;;;;;;;;kDAKL,8OAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;8DAAE;;;;;;;;;;;mDAGL,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAqC,QAAQ,QAAQ;;;;;;kFACnE,8OAAC;wEAAE,WAAU;;4EAAsC;4EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;0EAE9E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFACb,QAAQ,QAAQ;;;;;;kFAEnB,8OAAC;kFAAK;;;;;;kFACN,8OAAC;kFAAM,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kEAGvB,8OAAC;wDACC,SAAS,IAAM,cAAc,QAAQ,EAAE;wDACvC,WAAU;kEAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;+CAlBZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA8BjC,4BACC,8OAAC,mIAAA,CAAA,UAAa;gBACZ,WAAW,CAAC;oBACV,cAAc;oBACd,4BAA4B;oBAC5B,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG;wBACd,yDAAyD;wBACzD,MAAM,WAAW;4BACf,UAAU;4BACV,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI,OAAO;4BACtD,MAAM,IAAI;4BACV,mBAAmB;wBACrB;wBACA,kBAAkB;oBACpB;oBACA,OAAO,aAAa,CAAC;gBACvB;gBACA,SAAS,IAAM,cAAc;;;;;;YAKhC,4BACC,8OAAC,mIAAA,CAAA,UAAa;gBACZ,iBAAiB;gBACjB,SAAS,IAAM,cAAc;;;;;;YAKhC,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAK,UAAU;4BAAkB,WAAU;;8CAC1C,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;4CACpD,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,QAAQ;4CACR,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAO,OAAM;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAO,OAAM;8DAAoB;;;;;;8DAClC,8OAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAG1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}