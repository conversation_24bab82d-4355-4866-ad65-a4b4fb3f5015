(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/contexts/ThemeContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": ()=>ThemeProvider,
    "themes": ()=>themes,
    "useTheme": ()=>useTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const themes = {
    light: {
        id: 'light',
        name: 'Light Mode',
        description: 'Clean and bright interface',
        colors: {
            bg: {
                primary: '#ffffff',
                secondary: '#f8fafc',
                tertiary: '#f1f5f9',
                card: '#ffffff',
                modal: '#ffffff'
            },
            text: {
                primary: '#1f2937',
                secondary: '#6b7280',
                tertiary: '#9ca3af',
                inverse: '#ffffff'
            },
            accent: {
                primary: '#3b82f6',
                secondary: '#6366f1',
                success: '#10b981',
                warning: '#f59e0b',
                error: '#ef4444',
                info: '#06b6d4'
            },
            border: {
                primary: '#e5e7eb',
                secondary: '#d1d5db',
                focus: '#3b82f6'
            },
            glow: {
                primary: 'rgba(59, 130, 246, 0.3)',
                secondary: 'rgba(99, 102, 241, 0.3)',
                accent: 'rgba(16, 185, 129, 0.3)'
            }
        },
        effects: {
            blur: 'backdrop-blur-sm',
            shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            glow: '0 0 20px rgba(59, 130, 246, 0.3)',
            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            hover: {
                scale: 'scale-105',
                glow: '0 0 25px rgba(59, 130, 246, 0.4)',
                shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.2)'
            }
        }
    },
    dark: {
        id: 'dark',
        name: 'Dark Mode',
        description: 'Easy on the eyes',
        colors: {
            bg: {
                primary: '#111827',
                secondary: '#1f2937',
                tertiary: '#374151',
                card: '#1f2937',
                modal: '#111827'
            },
            text: {
                primary: '#f9fafb',
                secondary: '#d1d5db',
                tertiary: '#9ca3af',
                inverse: '#111827'
            },
            accent: {
                primary: '#60a5fa',
                secondary: '#818cf8',
                success: '#34d399',
                warning: '#fbbf24',
                error: '#f87171',
                info: '#22d3ee'
            },
            border: {
                primary: '#374151',
                secondary: '#4b5563',
                focus: '#60a5fa'
            },
            glow: {
                primary: 'rgba(96, 165, 250, 0.3)',
                secondary: 'rgba(129, 140, 248, 0.3)',
                accent: 'rgba(52, 211, 153, 0.3)'
            }
        },
        effects: {
            blur: 'backdrop-blur-sm',
            shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',
            glow: '0 0 20px rgba(96, 165, 250, 0.3)',
            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            hover: {
                scale: 'scale-105',
                glow: '0 0 25px rgba(96, 165, 250, 0.4)',
                shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.4)'
            }
        }
    },
    cyber: {
        id: 'cyber',
        name: 'Cyber Black',
        description: 'Futuristic super black with neon accents',
        colors: {
            bg: {
                primary: '#000000',
                secondary: '#0a0a0a',
                tertiary: '#111111',
                card: 'rgba(0, 0, 0, 0.9)',
                modal: 'rgba(0, 0, 0, 0.95)'
            },
            text: {
                primary: '#00ff88',
                secondary: '#00ccff',
                tertiary: '#888888',
                inverse: '#000000'
            },
            accent: {
                primary: '#00ff88',
                secondary: '#00ccff',
                success: '#00ff00',
                warning: '#ffaa00',
                error: '#ff0055',
                info: '#0088ff'
            },
            border: {
                primary: '#00ff88',
                secondary: '#00ccff',
                focus: '#00ff88'
            },
            glow: {
                primary: 'rgba(0, 255, 136, 0.5)',
                secondary: 'rgba(0, 204, 255, 0.5)',
                accent: 'rgba(0, 255, 0, 0.5)'
            }
        },
        effects: {
            blur: 'backdrop-blur-md',
            shadow: '0 0 30px rgba(0, 255, 136, 0.3)',
            glow: '0 0 40px rgba(0, 255, 136, 0.6)',
            gradient: 'linear-gradient(135deg, #00ff88 0%, #00ccff 100%)',
            hover: {
                scale: 'scale-110',
                glow: '0 0 50px rgba(0, 255, 136, 0.8)',
                shadow: '0 20px 40px -10px rgba(0, 255, 136, 0.4)'
            }
        }
    },
    neon: {
        id: 'neon',
        name: 'Neon Dreams',
        description: 'Vibrant neon with dark background',
        colors: {
            bg: {
                primary: '#0d1117',
                secondary: '#161b22',
                tertiary: '#21262d',
                card: 'rgba(13, 17, 23, 0.9)',
                modal: 'rgba(13, 17, 23, 0.95)'
            },
            text: {
                primary: '#ff006e',
                secondary: '#8338ec',
                tertiary: '#aaaaaa',
                inverse: '#0d1117'
            },
            accent: {
                primary: '#ff006e',
                secondary: '#8338ec',
                success: '#06ffa5',
                warning: '#ffbe0b',
                error: '#fb5607',
                info: '#3a86ff'
            },
            border: {
                primary: '#ff006e',
                secondary: '#8338ec',
                focus: '#ff006e'
            },
            glow: {
                primary: 'rgba(255, 0, 110, 0.5)',
                secondary: 'rgba(131, 56, 236, 0.5)',
                accent: 'rgba(6, 255, 165, 0.5)'
            }
        },
        effects: {
            blur: 'backdrop-blur-lg',
            shadow: '0 0 25px rgba(255, 0, 110, 0.4)',
            glow: '0 0 35px rgba(255, 0, 110, 0.7)',
            gradient: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',
            hover: {
                scale: 'scale-108',
                glow: '0 0 45px rgba(255, 0, 110, 0.9)',
                shadow: '0 15px 35px -8px rgba(255, 0, 110, 0.5)'
            }
        }
    },
    matrix: {
        id: 'matrix',
        name: 'Matrix Code',
        description: 'Green matrix-style interface',
        colors: {
            bg: {
                primary: '#000000',
                secondary: '#001100',
                tertiary: '#002200',
                card: 'rgba(0, 17, 0, 0.9)',
                modal: 'rgba(0, 0, 0, 0.95)'
            },
            text: {
                primary: '#00ff00',
                secondary: '#00cc00',
                tertiary: '#008800',
                inverse: '#000000'
            },
            accent: {
                primary: '#00ff00',
                secondary: '#00cc00',
                success: '#00ff00',
                warning: '#ffff00',
                error: '#ff0000',
                info: '#00ffff'
            },
            border: {
                primary: '#00ff00',
                secondary: '#00cc00',
                focus: '#00ff00'
            },
            glow: {
                primary: 'rgba(0, 255, 0, 0.5)',
                secondary: 'rgba(0, 204, 0, 0.5)',
                accent: 'rgba(0, 255, 0, 0.7)'
            }
        },
        effects: {
            blur: 'backdrop-blur-sm',
            shadow: '0 0 20px rgba(0, 255, 0, 0.4)',
            glow: '0 0 30px rgba(0, 255, 0, 0.8)',
            gradient: 'linear-gradient(135deg, #00ff00 0%, #00cc00 100%)',
            hover: {
                scale: 'scale-105',
                glow: '0 0 40px rgba(0, 255, 0, 1)',
                shadow: '0 10px 30px -5px rgba(0, 255, 0, 0.6)'
            }
        }
    }
};
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ThemeProvider(param) {
    let { children } = param;
    _s();
    const [currentTheme, setCurrentTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            const savedTheme = localStorage.getItem('expense-tracker-theme');
            if (savedTheme && themes[savedTheme]) {
                setCurrentTheme(savedTheme);
            }
        }
    }["ThemeProvider.useEffect"], []);
    const setTheme = (theme)=>{
        setCurrentTheme(theme);
        localStorage.setItem('expense-tracker-theme', theme);
        // Apply CSS custom properties
        const root = document.documentElement;
        const themeColors = themes[theme].colors;
        // Set CSS variables
        root.style.setProperty('--bg-primary', themeColors.bg.primary);
        root.style.setProperty('--bg-secondary', themeColors.bg.secondary);
        root.style.setProperty('--bg-tertiary', themeColors.bg.tertiary);
        root.style.setProperty('--bg-card', themeColors.bg.card);
        root.style.setProperty('--bg-modal', themeColors.bg.modal);
        root.style.setProperty('--text-primary', themeColors.text.primary);
        root.style.setProperty('--text-secondary', themeColors.text.secondary);
        root.style.setProperty('--text-tertiary', themeColors.text.tertiary);
        root.style.setProperty('--text-inverse', themeColors.text.inverse);
        root.style.setProperty('--accent-primary', themeColors.accent.primary);
        root.style.setProperty('--accent-secondary', themeColors.accent.secondary);
        root.style.setProperty('--accent-success', themeColors.accent.success);
        root.style.setProperty('--accent-warning', themeColors.accent.warning);
        root.style.setProperty('--accent-error', themeColors.accent.error);
        root.style.setProperty('--accent-info', themeColors.accent.info);
        root.style.setProperty('--border-primary', themeColors.border.primary);
        root.style.setProperty('--border-secondary', themeColors.border.secondary);
        root.style.setProperty('--border-focus', themeColors.border.focus);
        root.style.setProperty('--glow-primary', themeColors.glow.primary);
        root.style.setProperty('--glow-secondary', themeColors.glow.secondary);
        root.style.setProperty('--glow-accent', themeColors.glow.accent);
    };
    const toggleTheme = ()=>{
        const themeOrder = [
            'light',
            'dark',
            'cyber',
            'neon',
            'matrix'
        ];
        const currentIndex = themeOrder.indexOf(currentTheme);
        const nextIndex = (currentIndex + 1) % themeOrder.length;
        setTheme(themeOrder[nextIndex]);
    };
    const value = {
        currentTheme,
        theme: themes[currentTheme],
        setTheme,
        toggleTheme
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ThemeContext.tsx",
        lineNumber: 380,
        columnNumber: 5
    }, this);
}
_s(ThemeProvider, "Lejb1i4u7WfVBoaijmgVVxlI00c=");
_c = ThemeProvider;
function useTheme() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
_s1(useTheme, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return type.displayName || "Context";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler"), REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        react_stack_bottom_frame: function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
}]);

//# sourceMappingURL=_39fa064a._.js.map