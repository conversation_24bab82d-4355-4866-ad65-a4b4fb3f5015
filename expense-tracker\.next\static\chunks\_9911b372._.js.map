{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\nexport type ThemeType = 'light' | 'dark' | 'cyber' | 'neon' | 'matrix';\n\nexport interface Theme {\n  id: ThemeType;\n  name: string;\n  description: string;\n  colors: {\n    // Background colors\n    bg: {\n      primary: string;\n      secondary: string;\n      tertiary: string;\n      card: string;\n      modal: string;\n    };\n    // Text colors\n    text: {\n      primary: string;\n      secondary: string;\n      tertiary: string;\n      inverse: string;\n    };\n    // Accent colors\n    accent: {\n      primary: string;\n      secondary: string;\n      success: string;\n      warning: string;\n      error: string;\n      info: string;\n    };\n    // Border colors\n    border: {\n      primary: string;\n      secondary: string;\n      focus: string;\n    };\n    // Special effects\n    glow: {\n      primary: string;\n      secondary: string;\n      accent: string;\n    };\n  };\n  effects: {\n    blur: string;\n    shadow: string;\n    glow: string;\n    gradient: string;\n    hover: {\n      scale: string;\n      glow: string;\n      shadow: string;\n    };\n  };\n}\n\nexport const themes: Record<ThemeType, Theme> = {\n  light: {\n    id: 'light',\n    name: 'Light Mode',\n    description: 'Clean and bright interface',\n    colors: {\n      bg: {\n        primary: '#ffffff',\n        secondary: '#f8fafc',\n        tertiary: '#f1f5f9',\n        card: '#ffffff',\n        modal: '#ffffff',\n      },\n      text: {\n        primary: '#1f2937',\n        secondary: '#6b7280',\n        tertiary: '#9ca3af',\n        inverse: '#ffffff',\n      },\n      accent: {\n        primary: '#3b82f6',\n        secondary: '#6366f1',\n        success: '#10b981',\n        warning: '#f59e0b',\n        error: '#ef4444',\n        info: '#06b6d4',\n      },\n      border: {\n        primary: '#e5e7eb',\n        secondary: '#d1d5db',\n        focus: '#3b82f6',\n      },\n      glow: {\n        primary: 'rgba(59, 130, 246, 0.3)',\n        secondary: 'rgba(99, 102, 241, 0.3)',\n        accent: 'rgba(16, 185, 129, 0.3)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-sm',\n      shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n      glow: '0 0 20px rgba(59, 130, 246, 0.3)',\n      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      hover: {\n        scale: 'scale-105',\n        glow: '0 0 25px rgba(59, 130, 246, 0.4)',\n        shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.2)',\n      },\n    },\n  },\n  dark: {\n    id: 'dark',\n    name: 'Dark Mode',\n    description: 'Easy on the eyes',\n    colors: {\n      bg: {\n        primary: '#111827',\n        secondary: '#1f2937',\n        tertiary: '#374151',\n        card: '#1f2937',\n        modal: '#111827',\n      },\n      text: {\n        primary: '#f9fafb',\n        secondary: '#d1d5db',\n        tertiary: '#9ca3af',\n        inverse: '#111827',\n      },\n      accent: {\n        primary: '#60a5fa',\n        secondary: '#818cf8',\n        success: '#34d399',\n        warning: '#fbbf24',\n        error: '#f87171',\n        info: '#22d3ee',\n      },\n      border: {\n        primary: '#374151',\n        secondary: '#4b5563',\n        focus: '#60a5fa',\n      },\n      glow: {\n        primary: 'rgba(96, 165, 250, 0.3)',\n        secondary: 'rgba(129, 140, 248, 0.3)',\n        accent: 'rgba(52, 211, 153, 0.3)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-sm',\n      shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',\n      glow: '0 0 20px rgba(96, 165, 250, 0.3)',\n      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      hover: {\n        scale: 'scale-105',\n        glow: '0 0 25px rgba(96, 165, 250, 0.4)',\n        shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.4)',\n      },\n    },\n  },\n  cyber: {\n    id: 'cyber',\n    name: 'Cyber Black',\n    description: 'Futuristic super black with neon accents',\n    colors: {\n      bg: {\n        primary: '#000000',\n        secondary: '#0a0a0a',\n        tertiary: '#111111',\n        card: 'rgba(0, 0, 0, 0.9)',\n        modal: 'rgba(0, 0, 0, 0.95)',\n      },\n      text: {\n        primary: '#00ff88',\n        secondary: '#00ccff',\n        tertiary: '#888888',\n        inverse: '#000000',\n      },\n      accent: {\n        primary: '#00ff88',\n        secondary: '#00ccff',\n        success: '#00ff00',\n        warning: '#ffaa00',\n        error: '#ff0055',\n        info: '#0088ff',\n      },\n      border: {\n        primary: '#00ff88',\n        secondary: '#00ccff',\n        focus: '#00ff88',\n      },\n      glow: {\n        primary: 'rgba(0, 255, 136, 0.5)',\n        secondary: 'rgba(0, 204, 255, 0.5)',\n        accent: 'rgba(0, 255, 0, 0.5)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-md',\n      shadow: '0 0 30px rgba(0, 255, 136, 0.3)',\n      glow: '0 0 40px rgba(0, 255, 136, 0.6)',\n      gradient: 'linear-gradient(135deg, #00ff88 0%, #00ccff 100%)',\n      hover: {\n        scale: 'scale-110',\n        glow: '0 0 50px rgba(0, 255, 136, 0.8)',\n        shadow: '0 20px 40px -10px rgba(0, 255, 136, 0.4)',\n      },\n    },\n  },\n  neon: {\n    id: 'neon',\n    name: 'Neon Dreams',\n    description: 'Vibrant neon with dark background',\n    colors: {\n      bg: {\n        primary: '#0d1117',\n        secondary: '#161b22',\n        tertiary: '#21262d',\n        card: 'rgba(13, 17, 23, 0.9)',\n        modal: 'rgba(13, 17, 23, 0.95)',\n      },\n      text: {\n        primary: '#ff006e',\n        secondary: '#8338ec',\n        tertiary: '#aaaaaa',\n        inverse: '#0d1117',\n      },\n      accent: {\n        primary: '#ff006e',\n        secondary: '#8338ec',\n        success: '#06ffa5',\n        warning: '#ffbe0b',\n        error: '#fb5607',\n        info: '#3a86ff',\n      },\n      border: {\n        primary: '#ff006e',\n        secondary: '#8338ec',\n        focus: '#ff006e',\n      },\n      glow: {\n        primary: 'rgba(255, 0, 110, 0.5)',\n        secondary: 'rgba(131, 56, 236, 0.5)',\n        accent: 'rgba(6, 255, 165, 0.5)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-lg',\n      shadow: '0 0 25px rgba(255, 0, 110, 0.4)',\n      glow: '0 0 35px rgba(255, 0, 110, 0.7)',\n      gradient: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',\n      hover: {\n        scale: 'scale-108',\n        glow: '0 0 45px rgba(255, 0, 110, 0.9)',\n        shadow: '0 15px 35px -8px rgba(255, 0, 110, 0.5)',\n      },\n    },\n  },\n  matrix: {\n    id: 'matrix',\n    name: 'Matrix Code',\n    description: 'Green matrix-style interface',\n    colors: {\n      bg: {\n        primary: '#000000',\n        secondary: '#001100',\n        tertiary: '#002200',\n        card: 'rgba(0, 17, 0, 0.9)',\n        modal: 'rgba(0, 0, 0, 0.95)',\n      },\n      text: {\n        primary: '#00ff00',\n        secondary: '#00cc00',\n        tertiary: '#008800',\n        inverse: '#000000',\n      },\n      accent: {\n        primary: '#00ff00',\n        secondary: '#00cc00',\n        success: '#00ff00',\n        warning: '#ffff00',\n        error: '#ff0000',\n        info: '#00ffff',\n      },\n      border: {\n        primary: '#00ff00',\n        secondary: '#00cc00',\n        focus: '#00ff00',\n      },\n      glow: {\n        primary: 'rgba(0, 255, 0, 0.5)',\n        secondary: 'rgba(0, 204, 0, 0.5)',\n        accent: 'rgba(0, 255, 0, 0.7)',\n      },\n    },\n    effects: {\n      blur: 'backdrop-blur-sm',\n      shadow: '0 0 20px rgba(0, 255, 0, 0.4)',\n      glow: '0 0 30px rgba(0, 255, 0, 0.8)',\n      gradient: 'linear-gradient(135deg, #00ff00 0%, #00cc00 100%)',\n      hover: {\n        scale: 'scale-105',\n        glow: '0 0 40px rgba(0, 255, 0, 1)',\n        shadow: '0 10px 30px -5px rgba(0, 255, 0, 0.6)',\n      },\n    },\n  },\n};\n\ninterface ThemeContextType {\n  currentTheme: ThemeType;\n  theme: Theme;\n  setTheme: (theme: ThemeType) => void;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [currentTheme, setCurrentTheme] = useState<ThemeType>('light');\n\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('expense-tracker-theme') as ThemeType;\n    if (savedTheme && themes[savedTheme]) {\n      setCurrentTheme(savedTheme);\n    }\n  }, []);\n\n  const setTheme = (theme: ThemeType) => {\n    setCurrentTheme(theme);\n    localStorage.setItem('expense-tracker-theme', theme);\n    \n    // Apply CSS custom properties\n    const root = document.documentElement;\n    const themeColors = themes[theme].colors;\n    \n    // Set CSS variables\n    root.style.setProperty('--bg-primary', themeColors.bg.primary);\n    root.style.setProperty('--bg-secondary', themeColors.bg.secondary);\n    root.style.setProperty('--bg-tertiary', themeColors.bg.tertiary);\n    root.style.setProperty('--bg-card', themeColors.bg.card);\n    root.style.setProperty('--bg-modal', themeColors.bg.modal);\n    \n    root.style.setProperty('--text-primary', themeColors.text.primary);\n    root.style.setProperty('--text-secondary', themeColors.text.secondary);\n    root.style.setProperty('--text-tertiary', themeColors.text.tertiary);\n    root.style.setProperty('--text-inverse', themeColors.text.inverse);\n    \n    root.style.setProperty('--accent-primary', themeColors.accent.primary);\n    root.style.setProperty('--accent-secondary', themeColors.accent.secondary);\n    root.style.setProperty('--accent-success', themeColors.accent.success);\n    root.style.setProperty('--accent-warning', themeColors.accent.warning);\n    root.style.setProperty('--accent-error', themeColors.accent.error);\n    root.style.setProperty('--accent-info', themeColors.accent.info);\n    \n    root.style.setProperty('--border-primary', themeColors.border.primary);\n    root.style.setProperty('--border-secondary', themeColors.border.secondary);\n    root.style.setProperty('--border-focus', themeColors.border.focus);\n    \n    root.style.setProperty('--glow-primary', themeColors.glow.primary);\n    root.style.setProperty('--glow-secondary', themeColors.glow.secondary);\n    root.style.setProperty('--glow-accent', themeColors.glow.accent);\n  };\n\n  const toggleTheme = () => {\n    const themeOrder: ThemeType[] = ['light', 'dark', 'cyber', 'neon', 'matrix'];\n    const currentIndex = themeOrder.indexOf(currentTheme);\n    const nextIndex = (currentIndex + 1) % themeOrder.length;\n    setTheme(themeOrder[nextIndex]);\n  };\n\n  const value = {\n    currentTheme,\n    theme: themes[currentTheme],\n    setTheme,\n    toggleTheme,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AA6DO,MAAM,SAAmC;IAC9C,OAAO;QACL,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IACA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IACA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,OAAO;YACT;YACA,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;YACV;QACF;QACA,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF;AACF;AASA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IAE5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,cAAc,MAAM,CAAC,WAAW,EAAE;gBACpC,gBAAgB;YAClB;QACF;kCAAG,EAAE;IAEL,MAAM,WAAW,CAAC;QAChB,gBAAgB;QAChB,aAAa,OAAO,CAAC,yBAAyB;QAE9C,8BAA8B;QAC9B,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,cAAc,MAAM,CAAC,MAAM,CAAC,MAAM;QAExC,oBAAoB;QACpB,KAAK,KAAK,CAAC,WAAW,CAAC,gBAAgB,YAAY,EAAE,CAAC,OAAO;QAC7D,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,EAAE,CAAC,SAAS;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,iBAAiB,YAAY,EAAE,CAAC,QAAQ;QAC/D,KAAK,KAAK,CAAC,WAAW,CAAC,aAAa,YAAY,EAAE,CAAC,IAAI;QACvD,KAAK,KAAK,CAAC,WAAW,CAAC,cAAc,YAAY,EAAE,CAAC,KAAK;QAEzD,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,IAAI,CAAC,OAAO;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,IAAI,CAAC,SAAS;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB,YAAY,IAAI,CAAC,QAAQ;QACnE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,IAAI,CAAC,OAAO;QAEjE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,sBAAsB,YAAY,MAAM,CAAC,SAAS;QACzE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,MAAM,CAAC,KAAK;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,iBAAiB,YAAY,MAAM,CAAC,IAAI;QAE/D,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,MAAM,CAAC,OAAO;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,sBAAsB,YAAY,MAAM,CAAC,SAAS;QACzE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,MAAM,CAAC,KAAK;QAEjE,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,YAAY,IAAI,CAAC,OAAO;QACjE,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,IAAI,CAAC,SAAS;QACrE,KAAK,KAAK,CAAC,WAAW,CAAC,iBAAiB,YAAY,IAAI,CAAC,MAAM;IACjE;IAEA,MAAM,cAAc;QAClB,MAAM,aAA0B;YAAC;YAAS;YAAQ;YAAS;YAAQ;SAAS;QAC5E,MAAM,eAAe,WAAW,OAAO,CAAC;QACxC,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,WAAW,MAAM;QACxD,SAAS,UAAU,CAAC,UAAU;IAChC;IAEA,MAAM,QAAQ;QACZ;QACA,OAAO,MAAM,CAAC,aAAa;QAC3B;QACA;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;GAjEgB;KAAA;AAmET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\n// Supported languages with their metadata\nexport const SUPPORTED_LANGUAGES = {\n  en: {\n    code: 'en',\n    name: 'English',\n    nativeName: 'English',\n    flag: '🇺🇸',\n    rtl: false,\n  },\n  zh: {\n    code: 'zh',\n    name: 'Chinese',\n    nativeName: '中文',\n    flag: '🇨🇳',\n    rtl: false,\n  },\n  hi: {\n    code: 'hi',\n    name: 'Hindi',\n    nativeName: 'हिन्दी',\n    flag: '🇮🇳',\n    rtl: false,\n  },\n  es: {\n    code: 'es',\n    name: 'Spanish',\n    nativeName: 'Español',\n    flag: '🇪🇸',\n    rtl: false,\n  },\n  ar: {\n    code: 'ar',\n    name: 'Arabic',\n    nativeName: 'العربية',\n    flag: '🇸🇦',\n    rtl: true,\n  },\n  bn: {\n    code: 'bn',\n    name: 'Bengali',\n    nativeName: 'বাংলা',\n    flag: '🇧🇩',\n    rtl: false,\n  },\n  fr: {\n    code: 'fr',\n    name: 'French',\n    nativeName: 'Français',\n    flag: '🇫🇷',\n    rtl: false,\n  },\n  ru: {\n    code: 'ru',\n    name: 'Russian',\n    nativeName: 'Русский',\n    flag: '🇷🇺',\n    rtl: false,\n  },\n  pt: {\n    code: 'pt',\n    name: 'Portuguese',\n    nativeName: 'Português',\n    flag: '🇧🇷',\n    rtl: false,\n  },\n  ur: {\n    code: 'ur',\n    name: 'Urdu',\n    nativeName: 'اردو',\n    flag: '🇵🇰',\n    rtl: true,\n  },\n  id: {\n    code: 'id',\n    name: 'Indonesian',\n    nativeName: 'Bahasa Indonesia',\n    flag: '🇮🇩',\n    rtl: false,\n  },\n  sw: {\n    code: 'sw',\n    name: 'Swahili',\n    nativeName: 'Kiswahili',\n    flag: '🇰🇪',\n    rtl: false,\n  },\n  pa: {\n    code: 'pa',\n    name: 'Punjabi',\n    nativeName: 'ਪੰਜਾਬੀ',\n    flag: '🇮🇳',\n    rtl: false,\n  },\n  ko: {\n    code: 'ko',\n    name: 'Korean',\n    nativeName: '한국어',\n    flag: '🇰🇷',\n    rtl: false,\n  },\n  de: {\n    code: 'de',\n    name: 'German',\n    nativeName: 'Deutsch',\n    flag: '🇩🇪',\n    rtl: false,\n  },\n  ja: {\n    code: 'ja',\n    name: 'Japanese',\n    nativeName: '日本語',\n    flag: '🇯🇵',\n    rtl: false,\n  },\n} as const;\n\nexport type LanguageCode = keyof typeof SUPPORTED_LANGUAGES;\n\ninterface LanguageContextType {\n  currentLanguage: LanguageCode;\n  setLanguage: (language: LanguageCode) => void;\n  t: (text: string) => string;\n  isRTL: boolean;\n  languageData: typeof SUPPORTED_LANGUAGES[LanguageCode];\n  isTranslating: boolean;\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\ninterface LanguageProviderProps {\n  children: ReactNode;\n}\n\n// Translation cache to avoid repeated API calls\nconst translationCache = new Map<string, string>();\n\n// Detect browser language\nfunction detectBrowserLanguage(): LanguageCode {\n  if (typeof window === 'undefined') return 'en';\n\n  const browserLang = navigator.language.split('-')[0] as LanguageCode;\n  return SUPPORTED_LANGUAGES[browserLang] ? browserLang : 'en';\n}\n\n// Auto-translate function using Google Translate API (free tier)\nasync function translateText(text: string, targetLang: LanguageCode): Promise<string> {\n  if (targetLang === 'en') return text; // No translation needed for English\n\n  const cacheKey = `${text}:${targetLang}`;\n\n  // Check cache first\n  if (translationCache.has(cacheKey)) {\n    return translationCache.get(cacheKey)!;\n  }\n\n  try {\n    // Using Google Translate API (you can replace with other services)\n    const response = await fetch(\n      `https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=${targetLang}&dt=t&q=${encodeURIComponent(text)}`\n    );\n\n    if (!response.ok) {\n      throw new Error('Translation failed');\n    }\n\n    const data = await response.json();\n    const translatedText = data[0]?.[0]?.[0] || text;\n\n    // Cache the translation\n    translationCache.set(cacheKey, translatedText);\n\n    return translatedText;\n  } catch (error) {\n    console.warn('Translation failed, using original text:', error);\n    return text;\n  }\n}\n\nexport function LanguageProvider({ children }: LanguageProviderProps) {\n  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>('en');\n  const [isTranslating, setIsTranslating] = useState(false);\n\n  // Initialize language from localStorage or browser detection\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('expense-tracker-language') as LanguageCode;\n    const initialLanguage = savedLanguage || detectBrowserLanguage();\n    \n    if (SUPPORTED_LANGUAGES[initialLanguage]) {\n      setCurrentLanguage(initialLanguage);\n    }\n  }, []);\n\n  // Clear translation cache when language changes to force re-translation\n  useEffect(() => {\n    translationCache.clear();\n  }, [currentLanguage]);\n\n  // Update document direction for RTL languages\n  useEffect(() => {\n    const isRTL = SUPPORTED_LANGUAGES[currentLanguage].rtl;\n    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';\n    document.documentElement.lang = currentLanguage;\n\n    // Add RTL class to body for styling\n    if (isRTL) {\n      document.body.classList.add('rtl');\n    } else {\n      document.body.classList.remove('rtl');\n    }\n  }, [currentLanguage]);\n\n  const setLanguage = (language: LanguageCode) => {\n    setCurrentLanguage(language);\n    localStorage.setItem('expense-tracker-language', language);\n  };\n\n  // Auto-translate function with caching\n  const t = (text: string): string => {\n    if (currentLanguage === 'en') {\n      return text; // No translation needed for English\n    }\n\n    const cacheKey = `${text}:${currentLanguage}`;\n\n    // Check cache first\n    if (translationCache.has(cacheKey)) {\n      return translationCache.get(cacheKey)!;\n    }\n\n    // If not in cache, start translation and return original text temporarily\n    setIsTranslating(true);\n    translateText(text, currentLanguage)\n      .then((translated) => {\n        setIsTranslating(false);\n        // Force re-render by updating a dummy state or using a callback\n        // This is a simplified approach - in production, you might want to use a more sophisticated state management\n      })\n      .catch(() => {\n        setIsTranslating(false);\n      });\n\n    return text; // Return original text while translating\n  };\n\n  const value: LanguageContextType = {\n    currentLanguage,\n    setLanguage,\n    t,\n    isRTL: SUPPORTED_LANGUAGES[currentLanguage].rtl,\n    languageData: SUPPORTED_LANGUAGES[currentLanguage],\n    isTranslating,\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n}\n\n// Custom hook for auto-translation with real-time updates\nexport function useTranslate(text: string): string {\n  const { currentLanguage } = useLanguage();\n  const [translatedText, setTranslatedText] = useState(text);\n\n  useEffect(() => {\n    if (currentLanguage === 'en') {\n      setTranslatedText(text);\n      return;\n    }\n\n    const cacheKey = `${text}:${currentLanguage}`;\n\n    // Check cache first\n    if (translationCache.has(cacheKey)) {\n      setTranslatedText(translationCache.get(cacheKey)!);\n      return;\n    }\n\n    // Translate if not in cache\n    translateText(text, currentLanguage)\n      .then((translated) => {\n        setTranslatedText(translated);\n      })\n      .catch(() => {\n        setTranslatedText(text);\n      });\n  }, [text, currentLanguage]);\n\n  return translatedText;\n}\n\n// Batch translation hook for multiple texts\nexport function useBatchTranslate(texts: string[]): string[] {\n  const { currentLanguage } = useLanguage();\n  const [translatedTexts, setTranslatedTexts] = useState(texts);\n\n  useEffect(() => {\n    if (currentLanguage === 'en') {\n      setTranslatedTexts(texts);\n      return;\n    }\n\n    const translateBatch = async () => {\n      const results = await Promise.all(\n        texts.map(text => translateText(text, currentLanguage))\n      );\n      setTranslatedTexts(results);\n    };\n\n    translateBatch();\n  }, [texts, currentLanguage]);\n\n  return translatedTexts;\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;;;AAFA;;AAKO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;QACN,KAAK;IACP;AACF;AAaA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAMvE,gDAAgD;AAChD,MAAM,mBAAmB,IAAI;AAE7B,0BAA0B;AAC1B,SAAS;IACP;;IAEA,MAAM,cAAc,UAAU,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;IACpD,OAAO,mBAAmB,CAAC,YAAY,GAAG,cAAc;AAC1D;AAEA,iEAAiE;AACjE,eAAe,cAAc,IAAY,EAAE,UAAwB;IACjE,IAAI,eAAe,MAAM,OAAO,MAAM,oCAAoC;IAE1E,MAAM,WAAW,AAAC,GAAU,OAAR,MAAK,KAAc,OAAX;IAE5B,oBAAoB;IACpB,IAAI,iBAAiB,GAAG,CAAC,WAAW;QAClC,OAAO,iBAAiB,GAAG,CAAC;IAC9B;IAEA,IAAI;YAWqB,SAAA;QAVvB,mEAAmE;QACnE,MAAM,WAAW,MAAM,MACrB,AAAC,2EAA+F,OAArB,YAAW,YAAmC,OAAzB,mBAAmB;QAGrH,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,iBAAiB,EAAA,SAAA,IAAI,CAAC,EAAE,cAAP,8BAAA,UAAA,MAAS,CAAC,EAAE,cAAZ,8BAAA,OAAc,CAAC,EAAE,KAAI;QAE5C,wBAAwB;QACxB,iBAAiB,GAAG,CAAC,UAAU;QAE/B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4CAA4C;QACzD,OAAO;IACT;AACF;AAEO,SAAS,iBAAiB,KAAmC;QAAnC,EAAE,QAAQ,EAAyB,GAAnC;;IAC/B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,MAAM,kBAAkB,iBAAiB;YAEzC,IAAI,mBAAmB,CAAC,gBAAgB,EAAE;gBACxC,mBAAmB;YACrB;QACF;qCAAG,EAAE;IAEL,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,iBAAiB,KAAK;QACxB;qCAAG;QAAC;KAAgB;IAEpB,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,QAAQ,mBAAmB,CAAC,gBAAgB,CAAC,GAAG;YACtD,SAAS,eAAe,CAAC,GAAG,GAAG,QAAQ,QAAQ;YAC/C,SAAS,eAAe,CAAC,IAAI,GAAG;YAEhC,oCAAoC;YACpC,IAAI,OAAO;gBACT,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YAC9B,OAAO;gBACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YACjC;QACF;qCAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAC;QACnB,mBAAmB;QACnB,aAAa,OAAO,CAAC,4BAA4B;IACnD;IAEA,uCAAuC;IACvC,MAAM,IAAI,CAAC;QACT,IAAI,oBAAoB,MAAM;YAC5B,OAAO,MAAM,oCAAoC;QACnD;QAEA,MAAM,WAAW,AAAC,GAAU,OAAR,MAAK,KAAmB,OAAhB;QAE5B,oBAAoB;QACpB,IAAI,iBAAiB,GAAG,CAAC,WAAW;YAClC,OAAO,iBAAiB,GAAG,CAAC;QAC9B;QAEA,0EAA0E;QAC1E,iBAAiB;QACjB,cAAc,MAAM,iBACjB,IAAI,CAAC,CAAC;YACL,iBAAiB;QACjB,gEAAgE;QAChE,6GAA6G;QAC/G,GACC,KAAK,CAAC;YACL,iBAAiB;QACnB;QAEF,OAAO,MAAM,yCAAyC;IACxD;IAEA,MAAM,QAA6B;QACjC;QACA;QACA;QACA,OAAO,mBAAmB,CAAC,gBAAgB,CAAC,GAAG;QAC/C,cAAc,mBAAmB,CAAC,gBAAgB;QAClD;IACF;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;GAhFgB;KAAA;AAkFT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS,aAAa,IAAY;;IACvC,MAAM,EAAE,eAAe,EAAE,GAAG;IAC5B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,oBAAoB,MAAM;gBAC5B,kBAAkB;gBAClB;YACF;YAEA,MAAM,WAAW,AAAC,GAAU,OAAR,MAAK,KAAmB,OAAhB;YAE5B,oBAAoB;YACpB,IAAI,iBAAiB,GAAG,CAAC,WAAW;gBAClC,kBAAkB,iBAAiB,GAAG,CAAC;gBACvC;YACF;YAEA,4BAA4B;YAC5B,cAAc,MAAM,iBACjB,IAAI;0CAAC,CAAC;oBACL,kBAAkB;gBACpB;yCACC,KAAK;0CAAC;oBACL,kBAAkB;gBACpB;;QACJ;iCAAG;QAAC;QAAM;KAAgB;IAE1B,OAAO;AACT;IA7BgB;;QACc;;;AA+BvB,SAAS,kBAAkB,KAAe;;IAC/C,MAAM,EAAE,eAAe,EAAE,GAAG;IAC5B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,oBAAoB,MAAM;gBAC5B,mBAAmB;gBACnB;YACF;YAEA,MAAM;8DAAiB;oBACrB,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,MAAM,GAAG;sEAAC,CAAA,OAAQ,cAAc,MAAM;;oBAExC,mBAAmB;gBACrB;;YAEA;QACF;sCAAG;QAAC;QAAO;KAAgB;IAE3B,OAAO;AACT;IArBgB;;QACc", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}