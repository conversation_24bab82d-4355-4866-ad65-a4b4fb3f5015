{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/lib/ocr.ts"], "sourcesContent": ["import Tesseract from 'tesseract.js';\n\nexport interface OCRResult {\n  text: string;\n  confidence: number;\n}\n\nexport interface ProcessedImage {\n  canvas: HTMLCanvasElement;\n  dataUrl: string;\n}\n\n/**\n * Preprocess image for better OCR accuracy\n */\nexport const preprocessImage = (file: File): Promise<ProcessedImage> => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n\n    if (!ctx) {\n      reject(new Error('Could not get canvas context'));\n      return;\n    }\n\n    img.onload = () => {\n      // Set canvas size\n      canvas.width = img.width;\n      canvas.height = img.height;\n\n      // Draw original image\n      ctx.drawImage(img, 0, 0);\n\n      // Get image data for processing\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // Convert to grayscale and increase contrast\n      for (let i = 0; i < data.length; i += 4) {\n        // Convert to grayscale using luminance formula\n        const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);\n        \n        // Increase contrast\n        const contrast = 1.5;\n        const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255));\n        const contrastedGray = Math.min(255, Math.max(0, factor * (gray - 128) + 128));\n\n        data[i] = contrastedGray;     // Red\n        data[i + 1] = contrastedGray; // Green\n        data[i + 2] = contrastedGray; // Blue\n        // Alpha channel (data[i + 3]) remains unchanged\n      }\n\n      // Put processed image data back\n      ctx.putImageData(imageData, 0, 0);\n\n      resolve({\n        canvas,\n        dataUrl: canvas.toDataURL('image/png')\n      });\n    };\n\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n\n    // Create object URL from file\n    const objectUrl = URL.createObjectURL(file);\n    img.src = objectUrl;\n  });\n};\n\n/**\n * Extract text from image using Tesseract.js\n */\nexport const extractTextFromImage = async (\n  imageSource: string | File | HTMLCanvasElement,\n  options?: {\n    language?: string;\n    logger?: (info: any) => void;\n  }\n): Promise<OCRResult> => {\n  try {\n    const { language = 'eng', logger } = options || {};\n\n    const result = await Tesseract.recognize(\n      imageSource,\n      language,\n      {\n        logger: logger || (() => {}),\n        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/$-: \\n',\n      }\n    );\n\n    return {\n      text: result.data.text,\n      confidence: result.data.confidence\n    };\n  } catch (error) {\n    console.error('OCR Error:', error);\n    throw new Error('Failed to extract text from image');\n  }\n};\n\n/**\n * Process receipt image and extract text\n */\nexport const processReceiptImage = async (\n  file: File,\n  onProgress?: (progress: number) => void\n): Promise<OCRResult> => {\n  try {\n    // Preprocess image\n    onProgress?.(10);\n    const processedImage = await preprocessImage(file);\n    \n    onProgress?.(30);\n    \n    // Extract text using OCR\n    const result = await extractTextFromImage(\n      processedImage.canvas,\n      {\n        language: 'eng',\n        logger: (info) => {\n          if (info.status === 'recognizing text') {\n            const progress = 30 + (info.progress * 0.7 * 100);\n            onProgress?.(Math.round(progress));\n          }\n        }\n      }\n    );\n\n    onProgress?.(100);\n    return result;\n  } catch (error) {\n    console.error('Receipt processing error:', error);\n    throw error;\n  }\n};\n\n/**\n * Validate image file\n */\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\n  const maxSize = 10 * 1024 * 1024; // 10MB\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n  if (!allowedTypes.includes(file.type)) {\n    return {\n      valid: false,\n      error: 'Please upload a valid image file (JPEG, PNG, or WebP)'\n    };\n  }\n\n  if (file.size > maxSize) {\n    return {\n      valid: false,\n      error: 'Image file size must be less than 10MB'\n    };\n  }\n\n  return { valid: true };\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAeO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,CAAC,KAAK;YACR,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,IAAI,MAAM,GAAG;YACX,kBAAkB;YAClB,OAAO,KAAK,GAAG,IAAI,KAAK;YACxB,OAAO,MAAM,GAAG,IAAI,MAAM;YAE1B,sBAAsB;YACtB,IAAI,SAAS,CAAC,KAAK,GAAG;YAEtB,gCAAgC;YAChC,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YACpE,MAAM,OAAO,UAAU,IAAI;YAE3B,6CAA6C;YAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;gBACvC,+CAA+C;gBAC/C,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;gBAEnF,oBAAoB;gBACpB,MAAM,WAAW;gBACjB,MAAM,SAAS,AAAC,MAAM,CAAC,WAAW,MAAM,GAAG,IAAK,CAAC,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC;gBAC7E,MAAM,iBAAiB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,IAAI;gBAEzE,IAAI,CAAC,EAAE,GAAG,gBAAoB,MAAM;gBACpC,IAAI,CAAC,IAAI,EAAE,GAAG,gBAAgB,QAAQ;gBACtC,IAAI,CAAC,IAAI,EAAE,GAAG,gBAAgB,OAAO;YACrC,gDAAgD;YAClD;YAEA,gCAAgC;YAChC,IAAI,YAAY,CAAC,WAAW,GAAG;YAE/B,QAAQ;gBACN;gBACA,SAAS,OAAO,SAAS,CAAC;YAC5B;QACF;QAEA,IAAI,OAAO,GAAG;YACZ,OAAO,IAAI,MAAM;QACnB;QAEA,8BAA8B;QAC9B,MAAM,YAAY,IAAI,eAAe,CAAC;QACtC,IAAI,GAAG,GAAG;IACZ;AACF;AAKO,MAAM,uBAAuB,OAClC,aACA;IAKA,IAAI;QACF,MAAM,EAAE,WAAW,KAAK,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC;QAEjD,MAAM,SAAS,MAAM,kJAAA,CAAA,UAAS,CAAC,SAAS,CACtC,aACA,UACA;YACE,QAAQ,UAAU,CAAC,KAAO,CAAC;YAC3B,yBAAyB;QAC3B;QAGF,OAAO;YACL,MAAM,OAAO,IAAI,CAAC,IAAI;YACtB,YAAY,OAAO,IAAI,CAAC,UAAU;QACpC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,MAAM,sBAAsB,OACjC,MACA;IAEA,IAAI;QACF,mBAAmB;QACnB,uBAAA,iCAAA,WAAa;QACb,MAAM,iBAAiB,MAAM,gBAAgB;QAE7C,uBAAA,iCAAA,WAAa;QAEb,yBAAyB;QACzB,MAAM,SAAS,MAAM,qBACnB,eAAe,MAAM,EACrB;YACE,UAAU;YACV,QAAQ,CAAC;gBACP,IAAI,KAAK,MAAM,KAAK,oBAAoB;oBACtC,MAAM,WAAW,KAAM,KAAK,QAAQ,GAAG,MAAM;oBAC7C,uBAAA,iCAAA,WAAa,KAAK,KAAK,CAAC;gBAC1B;YACF;QACF;QAGF,uBAAA,iCAAA,WAAa;QACb,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAKO,MAAM,oBAAoB,CAAC;IAChC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;IACzC,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAE3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/lib/receipt-parser.ts"], "sourcesContent": ["import { format, parse, isValid } from 'date-fns';\n\nexport interface ParsedReceiptData {\n  merchant: string;\n  amount: number;\n  date: Date | null;\n  suggestedCategory: string;\n  confidence: {\n    merchant: number;\n    amount: number;\n    date: number;\n    category: number;\n  };\n  rawText: string;\n}\n\n// Common merchant patterns and their categories\nconst MERCHANT_CATEGORIES: Record<string, string> = {\n  // Food & Dining\n  'mcdonalds': 'Food & Dining',\n  'burger king': 'Food & Dining',\n  'subway': 'Food & Dining',\n  'starbucks': 'Food & Dining',\n  'pizza': 'Food & Dining',\n  'restaurant': 'Food & Dining',\n  'cafe': 'Food & Dining',\n  'diner': 'Food & Dining',\n  'bistro': 'Food & Dining',\n  'grill': 'Food & Dining',\n  \n  // Transportation\n  'shell': 'Transportation',\n  'exxon': 'Transportation',\n  'chevron': 'Transportation',\n  'bp': 'Transportation',\n  'gas': 'Transportation',\n  'fuel': 'Transportation',\n  'uber': 'Transportation',\n  'lyft': 'Transportation',\n  'taxi': 'Transportation',\n  \n  // Shopping\n  'walmart': 'Shopping',\n  'target': 'Shopping',\n  'amazon': 'Shopping',\n  'costco': 'Shopping',\n  'home depot': 'Shopping',\n  'lowes': 'Shopping',\n  'best buy': 'Shopping',\n  'mall': 'Shopping',\n  'store': 'Shopping',\n  \n  // Entertainment\n  'cinema': 'Entertainment',\n  'theater': 'Entertainment',\n  'movie': 'Entertainment',\n  'netflix': 'Entertainment',\n  'spotify': 'Entertainment',\n  'gym': 'Entertainment',\n  'fitness': 'Entertainment',\n  \n  // Bills & Utilities\n  'electric': 'Bills & Utilities',\n  'water': 'Bills & Utilities',\n  'internet': 'Bills & Utilities',\n  'phone': 'Bills & Utilities',\n  'utility': 'Bills & Utilities',\n  'bill': 'Bills & Utilities',\n  \n  // Healthcare\n  'pharmacy': 'Healthcare',\n  'hospital': 'Healthcare',\n  'clinic': 'Healthcare',\n  'doctor': 'Healthcare',\n  'medical': 'Healthcare',\n  'health': 'Healthcare',\n};\n\n/**\n * Extract merchant name from receipt text\n */\nconst extractMerchant = (text: string): { merchant: string; confidence: number } => {\n  const lines = text.split('\\n').map(line => line.trim()).filter(line => line.length > 0);\n  \n  // Look for merchant name in first few lines\n  for (let i = 0; i < Math.min(5, lines.length); i++) {\n    const line = lines[i];\n    \n    // Skip lines that look like addresses, phone numbers, or common receipt headers\n    if (\n      /^\\d+\\s+/.test(line) || // Starts with numbers (likely address)\n      /^\\(\\d{3}\\)/.test(line) || // Phone number\n      /^tel:|^phone:|^receipt|^invoice/i.test(line) || // Common headers\n      line.length < 3 || // Too short\n      /^[0-9\\/\\-\\s]+$/.test(line) // Only numbers and separators\n    ) {\n      continue;\n    }\n    \n    // Clean up the line\n    const cleanedLine = line\n      .replace(/[^\\w\\s&'-]/g, ' ') // Remove special chars except &, ', -\n      .replace(/\\s+/g, ' ')\n      .trim();\n    \n    if (cleanedLine.length >= 3) {\n      return {\n        merchant: cleanedLine,\n        confidence: i === 0 ? 0.9 : Math.max(0.5, 0.9 - (i * 0.1))\n      };\n    }\n  }\n  \n  return { merchant: 'Unknown Merchant', confidence: 0.1 };\n};\n\n/**\n * Extract amount from receipt text\n */\nconst extractAmount = (text: string): { amount: number; confidence: number } => {\n  // Common patterns for total amounts\n  const totalPatterns = [\n    /total[:\\s]*\\$?(\\d+\\.?\\d*)/i,\n    /amount[:\\s]*\\$?(\\d+\\.?\\d*)/i,\n    /grand\\s*total[:\\s]*\\$?(\\d+\\.?\\d*)/i,\n    /final[:\\s]*\\$?(\\d+\\.?\\d*)/i,\n    /\\$(\\d+\\.\\d{2})\\s*$/m, // Dollar amount at end of line\n    /(\\d+\\.\\d{2})\\s*$(?!.*\\d+\\.\\d{2})/m, // Last decimal amount\n  ];\n  \n  let bestMatch = { amount: 0, confidence: 0 };\n  \n  for (const pattern of totalPatterns) {\n    const matches = text.match(pattern);\n    if (matches) {\n      const amount = parseFloat(matches[1]);\n      if (!isNaN(amount) && amount > 0) {\n        const confidence = pattern.source.includes('total') ? 0.9 : 0.7;\n        if (confidence > bestMatch.confidence) {\n          bestMatch = { amount, confidence };\n        }\n      }\n    }\n  }\n  \n  // If no total found, look for any monetary amount\n  if (bestMatch.confidence === 0) {\n    const amountMatches = text.match(/\\$?(\\d+\\.\\d{2})/g);\n    if (amountMatches) {\n      const amounts = amountMatches\n        .map(match => parseFloat(match.replace('$', '')))\n        .filter(amount => !isNaN(amount) && amount > 0)\n        .sort((a, b) => b - a); // Sort descending\n      \n      if (amounts.length > 0) {\n        bestMatch = { amount: amounts[0], confidence: 0.5 };\n      }\n    }\n  }\n  \n  return bestMatch;\n};\n\n/**\n * Extract date from receipt text\n */\nconst extractDate = (text: string): { date: Date | null; confidence: number } => {\n  const datePatterns = [\n    /(\\d{1,2}\\/\\d{1,2}\\/\\d{4})/g, // MM/DD/YYYY or M/D/YYYY\n    /(\\d{1,2}-\\d{1,2}-\\d{4})/g,   // MM-DD-YYYY or M-D-YYYY\n    /(\\d{4}-\\d{1,2}-\\d{1,2})/g,   // YYYY-MM-DD\n    /(\\d{1,2}\\/\\d{1,2}\\/\\d{2})/g, // MM/DD/YY or M/D/YY\n  ];\n  \n  for (const pattern of datePatterns) {\n    const matches = Array.from(text.matchAll(pattern));\n    for (const match of matches) {\n      const dateStr = match[1];\n      let parsedDate: Date | null = null;\n      \n      // Try different date formats\n      const formats = ['MM/dd/yyyy', 'M/d/yyyy', 'MM-dd-yyyy', 'M-d-yyyy', 'yyyy-MM-dd', 'MM/dd/yy', 'M/d/yy'];\n      \n      for (const formatStr of formats) {\n        try {\n          parsedDate = parse(dateStr, formatStr, new Date());\n          if (isValid(parsedDate)) {\n            // Check if date is reasonable (not too far in future/past)\n            const now = new Date();\n            const yearDiff = Math.abs(parsedDate.getFullYear() - now.getFullYear());\n            \n            if (yearDiff <= 2) {\n              return { date: parsedDate, confidence: 0.8 };\n            }\n          }\n        } catch (error) {\n          // Continue to next format\n        }\n      }\n    }\n  }\n  \n  return { date: null, confidence: 0 };\n};\n\n/**\n * Suggest category based on merchant name\n */\nconst suggestCategory = (merchant: string): { category: string; confidence: number } => {\n  const lowerMerchant = merchant.toLowerCase();\n  \n  // Direct matches\n  for (const [keyword, category] of Object.entries(MERCHANT_CATEGORIES)) {\n    if (lowerMerchant.includes(keyword)) {\n      return { category, confidence: 0.8 };\n    }\n  }\n  \n  // Fallback patterns\n  if (/food|eat|restaurant|cafe|diner|kitchen|grill|pizza|burger/i.test(merchant)) {\n    return { category: 'Food & Dining', confidence: 0.6 };\n  }\n  \n  if (/gas|fuel|station|oil|automotive/i.test(merchant)) {\n    return { category: 'Transportation', confidence: 0.6 };\n  }\n  \n  if (/store|shop|market|retail|mall/i.test(merchant)) {\n    return { category: 'Shopping', confidence: 0.6 };\n  }\n  \n  return { category: 'Other', confidence: 0.3 };\n};\n\n/**\n * Parse receipt text and extract structured data\n */\nexport const parseReceiptText = (text: string): ParsedReceiptData => {\n  const merchantResult = extractMerchant(text);\n  const amountResult = extractAmount(text);\n  const dateResult = extractDate(text);\n  const categoryResult = suggestCategory(merchantResult.merchant);\n  \n  return {\n    merchant: merchantResult.merchant,\n    amount: amountResult.amount,\n    date: dateResult.date,\n    suggestedCategory: categoryResult.category,\n    confidence: {\n      merchant: merchantResult.confidence,\n      amount: amountResult.confidence,\n      date: dateResult.confidence,\n      category: categoryResult.confidence,\n    },\n    rawText: text,\n  };\n};\n\n/**\n * Validate parsed receipt data\n */\nexport const validateParsedData = (data: ParsedReceiptData): { valid: boolean; errors: string[] } => {\n  const errors: string[] = [];\n  \n  if (!data.merchant || data.merchant === 'Unknown Merchant') {\n    errors.push('Could not identify merchant name');\n  }\n  \n  if (!data.amount || data.amount <= 0) {\n    errors.push('Could not identify valid amount');\n  }\n  \n  if (!data.date) {\n    errors.push('Could not identify transaction date');\n  }\n  \n  return {\n    valid: errors.length === 0,\n    errors,\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAgBA,gDAAgD;AAChD,MAAM,sBAA8C;IAClD,gBAAgB;IAChB,aAAa;IACb,eAAe;IACf,UAAU;IACV,aAAa;IACb,SAAS;IACT,cAAc;IACd,QAAQ;IACR,SAAS;IACT,UAAU;IACV,SAAS;IAET,iBAAiB;IACjB,SAAS;IACT,SAAS;IACT,WAAW;IACX,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IAER,WAAW;IACX,WAAW;IACX,UAAU;IACV,UAAU;IACV,UAAU;IACV,cAAc;IACd,SAAS;IACT,YAAY;IACZ,QAAQ;IACR,SAAS;IAET,gBAAgB;IAChB,UAAU;IACV,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;IACX,OAAO;IACP,WAAW;IAEX,oBAAoB;IACpB,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,SAAS;IACT,WAAW;IACX,QAAQ;IAER,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,UAAU;IACV,WAAW;IACX,UAAU;AACZ;AAEA;;CAEC,GACD,MAAM,kBAAkB,CAAC;IACvB,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAErF,4CAA4C;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;QAClD,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,gFAAgF;QAChF,IACE,UAAU,IAAI,CAAC,SAAS,uCAAuC;QAC/D,aAAa,IAAI,CAAC,SAAS,eAAe;QAC1C,mCAAmC,IAAI,CAAC,SAAS,iBAAiB;QAClE,KAAK,MAAM,GAAG,KAAK,YAAY;QAC/B,iBAAiB,IAAI,CAAC,MAAM,8BAA8B;UAC1D;YACA;QACF;QAEA,oBAAoB;QACpB,MAAM,cAAc,KACjB,OAAO,CAAC,eAAe,KAAK,sCAAsC;SAClE,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,IAAI,YAAY,MAAM,IAAI,GAAG;YAC3B,OAAO;gBACL,UAAU;gBACV,YAAY,MAAM,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,MAAO,IAAI;YACvD;QACF;IACF;IAEA,OAAO;QAAE,UAAU;QAAoB,YAAY;IAAI;AACzD;AAEA;;CAEC,GACD,MAAM,gBAAgB,CAAC;IACrB,oCAAoC;IACpC,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,YAAY;QAAE,QAAQ;QAAG,YAAY;IAAE;IAE3C,KAAK,MAAM,WAAW,cAAe;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC;QAC3B,IAAI,SAAS;YACX,MAAM,SAAS,WAAW,OAAO,CAAC,EAAE;YACpC,IAAI,CAAC,MAAM,WAAW,SAAS,GAAG;gBAChC,MAAM,aAAa,QAAQ,MAAM,CAAC,QAAQ,CAAC,WAAW,MAAM;gBAC5D,IAAI,aAAa,UAAU,UAAU,EAAE;oBACrC,YAAY;wBAAE;wBAAQ;oBAAW;gBACnC;YACF;QACF;IACF;IAEA,kDAAkD;IAClD,IAAI,UAAU,UAAU,KAAK,GAAG;QAC9B,MAAM,gBAAgB,KAAK,KAAK,CAAC;QACjC,IAAI,eAAe;YACjB,MAAM,UAAU,cACb,GAAG,CAAC,CAAA,QAAS,WAAW,MAAM,OAAO,CAAC,KAAK,MAC3C,MAAM,CAAC,CAAA,SAAU,CAAC,MAAM,WAAW,SAAS,GAC5C,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,IAAI,kBAAkB;YAE5C,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,YAAY;oBAAE,QAAQ,OAAO,CAAC,EAAE;oBAAE,YAAY;gBAAI;YACpD;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,cAAc,CAAC;IACnB,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,WAAW,aAAc;QAClC,MAAM,UAAU,MAAM,IAAI,CAAC,KAAK,QAAQ,CAAC;QACzC,KAAK,MAAM,SAAS,QAAS;YAC3B,MAAM,UAAU,KAAK,CAAC,EAAE;YACxB,IAAI,aAA0B;YAE9B,6BAA6B;YAC7B,MAAM,UAAU;gBAAC;gBAAc;gBAAY;gBAAc;gBAAY;gBAAc;gBAAY;aAAS;YAExG,KAAK,MAAM,aAAa,QAAS;gBAC/B,IAAI;oBACF,aAAa,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,SAAS,WAAW,IAAI;oBAC3C,IAAI,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,aAAa;wBACvB,2DAA2D;wBAC3D,MAAM,MAAM,IAAI;wBAChB,MAAM,WAAW,KAAK,GAAG,CAAC,WAAW,WAAW,KAAK,IAAI,WAAW;wBAEpE,IAAI,YAAY,GAAG;4BACjB,OAAO;gCAAE,MAAM;gCAAY,YAAY;4BAAI;wBAC7C;oBACF;gBACF,EAAE,OAAO,OAAO;gBACd,0BAA0B;gBAC5B;YACF;QACF;IACF;IAEA,OAAO;QAAE,MAAM;QAAM,YAAY;IAAE;AACrC;AAEA;;CAEC,GACD,MAAM,kBAAkB,CAAC;IACvB,MAAM,gBAAgB,SAAS,WAAW;IAE1C,iBAAiB;IACjB,KAAK,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,OAAO,CAAC,qBAAsB;QACrE,IAAI,cAAc,QAAQ,CAAC,UAAU;YACnC,OAAO;gBAAE;gBAAU,YAAY;YAAI;QACrC;IACF;IAEA,oBAAoB;IACpB,IAAI,6DAA6D,IAAI,CAAC,WAAW;QAC/E,OAAO;YAAE,UAAU;YAAiB,YAAY;QAAI;IACtD;IAEA,IAAI,mCAAmC,IAAI,CAAC,WAAW;QACrD,OAAO;YAAE,UAAU;YAAkB,YAAY;QAAI;IACvD;IAEA,IAAI,iCAAiC,IAAI,CAAC,WAAW;QACnD,OAAO;YAAE,UAAU;YAAY,YAAY;QAAI;IACjD;IAEA,OAAO;QAAE,UAAU;QAAS,YAAY;IAAI;AAC9C;AAKO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,iBAAiB,gBAAgB;IACvC,MAAM,eAAe,cAAc;IACnC,MAAM,aAAa,YAAY;IAC/B,MAAM,iBAAiB,gBAAgB,eAAe,QAAQ;IAE9D,OAAO;QACL,UAAU,eAAe,QAAQ;QACjC,QAAQ,aAAa,MAAM;QAC3B,MAAM,WAAW,IAAI;QACrB,mBAAmB,eAAe,QAAQ;QAC1C,YAAY;YACV,UAAU,eAAe,UAAU;YACnC,QAAQ,aAAa,UAAU;YAC/B,MAAM,WAAW,UAAU;YAC3B,UAAU,eAAe,UAAU;QACrC;QACA,SAAS;IACX;AACF;AAKO,MAAM,qBAAqB,CAAC;IACjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,oBAAoB;QAC1D,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,GAAG;QACpC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,EAAE;QACd,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/components/ReceiptUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { Upload, Camera, X, Loader2, CheckCircle, AlertCircle } from 'lucide-react';\nimport { processReceiptImage, validateImageFile } from '@/lib/ocr';\nimport { parseReceiptText, validateParsedData } from '@/lib/receipt-parser';\nimport type { ParsedReceiptData } from '@/lib/receipt-parser';\n\ninterface ReceiptUploadProps {\n  onDataExtracted: (data: ParsedReceiptData) => void;\n  onClose: () => void;\n}\n\nexport default function ReceiptUpload({ onDataExtracted, onClose }: ReceiptUploadProps) {\n  const [file, setFile] = useState<File | null>(null);\n  const [preview, setPreview] = useState<string | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [error, setError] = useState<string | null>(null);\n  const [extractedData, setExtractedData] = useState<ParsedReceiptData | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileSelect = (selectedFile: File) => {\n    const validation = validateImageFile(selectedFile);\n    if (!validation.valid) {\n      setError(validation.error || 'Invalid file');\n      return;\n    }\n\n    setFile(selectedFile);\n    setError(null);\n    setExtractedData(null);\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n    };\n    reader.readAsDataURL(selectedFile);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    const droppedFile = e.dataTransfer.files[0];\n    if (droppedFile) {\n      handleFileSelect(droppedFile);\n    }\n  };\n\n  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = e.target.files?.[0];\n    if (selectedFile) {\n      handleFileSelect(selectedFile);\n    }\n  };\n\n  const processReceipt = async () => {\n    if (!file) return;\n\n    setIsProcessing(true);\n    setProgress(0);\n    setError(null);\n\n    try {\n      // Extract text using OCR\n      const ocrResult = await processReceiptImage(file, setProgress);\n      \n      // Parse the extracted text\n      const parsedData = parseReceiptText(ocrResult.text);\n      \n      // Validate the parsed data\n      const validation = validateParsedData(parsedData);\n      \n      if (!validation.valid) {\n        setError(`Data extraction incomplete: ${validation.errors.join(', ')}`);\n      }\n\n      setExtractedData(parsedData);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to process receipt');\n    } finally {\n      setIsProcessing(false);\n      setProgress(0);\n    }\n  };\n\n  const handleConfirm = () => {\n    if (extractedData) {\n      onDataExtracted(extractedData);\n      onClose();\n    }\n  };\n\n  const reset = () => {\n    setFile(null);\n    setPreview(null);\n    setExtractedData(null);\n    setError(null);\n    setProgress(0);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Upload Receipt</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6 space-y-6\">\n          {!file && (\n            <div\n              className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors cursor-pointer\"\n              onDrop={handleDrop}\n              onDragOver={(e) => e.preventDefault()}\n              onClick={() => fileInputRef.current?.click()}\n            >\n              <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-lg font-medium text-gray-900 mb-2\">\n                Drop your receipt here or click to browse\n              </p>\n              <p className=\"text-sm text-gray-500\">\n                Supports JPEG, PNG, and WebP files up to 10MB\n              </p>\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileInput}\n                className=\"hidden\"\n              />\n            </div>\n          )}\n\n          {file && preview && (\n            <div className=\"space-y-4\">\n              <div className=\"relative\">\n                <img\n                  src={preview}\n                  alt=\"Receipt preview\"\n                  className=\"w-full max-h-64 object-contain rounded-lg border\"\n                />\n                <button\n                  onClick={reset}\n                  className=\"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n\n              {!isProcessing && !extractedData && (\n                <button\n                  onClick={processReceipt}\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  Process Receipt\n                </button>\n              )}\n\n              {isProcessing && (\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-center space-x-2\">\n                    <Loader2 className=\"h-5 w-5 animate-spin text-blue-600\" />\n                    <span className=\"text-sm text-gray-600\">Processing receipt...</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div\n                      className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                      style={{ width: `${progress}%` }}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {error && (\n                <div className=\"flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg\">\n                  <AlertCircle className=\"h-5 w-5\" />\n                  <span className=\"text-sm\">{error}</span>\n                </div>\n              )}\n\n              {extractedData && (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg\">\n                    <CheckCircle className=\"h-5 w-5\" />\n                    <span className=\"text-sm\">Receipt processed successfully!</span>\n                  </div>\n\n                  <div className=\"bg-gray-50 p-4 rounded-lg space-y-3\">\n                    <h3 className=\"font-medium text-gray-900\">Extracted Information</h3>\n                    \n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Merchant:</span>\n                        <p className=\"font-medium\">{extractedData.merchant}</p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Amount:</span>\n                        <p className=\"font-medium\">${extractedData.amount.toFixed(2)}</p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Date:</span>\n                        <p className=\"font-medium\">\n                          {extractedData.date ? extractedData.date.toLocaleDateString() : 'Not found'}\n                        </p>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Category:</span>\n                        <p className=\"font-medium\">{extractedData.suggestedCategory}</p>\n                      </div>\n                    </div>\n\n                    <div className=\"text-xs text-gray-500\">\n                      <p>Confidence: Merchant {Math.round(extractedData.confidence.merchant * 100)}%, \n                         Amount {Math.round(extractedData.confidence.amount * 100)}%, \n                         Date {Math.round(extractedData.confidence.date * 100)}%</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={handleConfirm}\n                      className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors\"\n                    >\n                      Use This Data\n                    </button>\n                    <button\n                      onClick={reset}\n                      className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors\"\n                    >\n                      Try Again\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAae,SAAS,cAAc,KAAgD;QAAhD,EAAE,eAAe,EAAE,OAAO,EAAsB,GAAhD;;IACpC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC7E,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE;QACrC,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,SAAS,WAAW,KAAK,IAAI;YAC7B;QACF;QAEA,QAAQ;QACR,SAAS;QACT,iBAAiB;QAEjB,iBAAiB;QACjB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;gBACJ;YAAX,YAAW,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;QAC7B;QACA,OAAO,aAAa,CAAC;IACvB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,MAAM,cAAc,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QAC3C,IAAI,aAAa;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,kBAAkB,CAAC;YACF;QAArB,MAAM,gBAAe,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM;QAEX,gBAAgB;QAChB,YAAY;QACZ,SAAS;QAET,IAAI;YACF,yBAAyB;YACzB,MAAM,YAAY,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;YAElD,2BAA2B;YAC3B,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,IAAI;YAElD,2BAA2B;YAC3B,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YAEtC,IAAI,CAAC,WAAW,KAAK,EAAE;gBACrB,SAAS,AAAC,+BAA2D,OAA7B,WAAW,MAAM,CAAC,IAAI,CAAC;YACjE;YAEA,iBAAiB;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;YAChB,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe;YACjB,gBAAgB;YAChB;QACF;IACF;IAEA,MAAM,QAAQ;QACZ,QAAQ;QACR,WAAW;QACX,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;wBACZ,CAAC,sBACA,6LAAC;4BACC,WAAU;4BACV,QAAQ;4BACR,YAAY,CAAC,IAAM,EAAE,cAAc;4BACnC,SAAS;oCAAM;wCAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK;;;8CAE1C,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,6LAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;;;;;;;wBAKf,QAAQ,yBACP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK;4CACL,KAAI;4CACJ,WAAU;;;;;;sDAEZ,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAIhB,CAAC,gBAAgB,CAAC,+BACjB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;gCAKF,8BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,AAAC,GAAW,OAAT,UAAS;gDAAG;;;;;;;;;;;;;;;;;gCAMtC,uBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;gCAI9B,+BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAE,WAAU;8EAAe,cAAc,QAAQ;;;;;;;;;;;;sEAEpD,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAE,WAAU;;wEAAc;wEAAE,cAAc,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;sEAE5D,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAE,WAAU;8EACV,cAAc,IAAI,GAAG,cAAc,IAAI,CAAC,kBAAkB,KAAK;;;;;;;;;;;;sEAGpE,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAE,WAAU;8EAAe,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;8DAI/D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;4DAAE;4DAAsB,KAAK,KAAK,CAAC,cAAc,UAAU,CAAC,QAAQ,GAAG;4DAAK;4DAClE,KAAK,KAAK,CAAC,cAAc,UAAU,CAAC,MAAM,GAAG;4DAAK;4DACpD,KAAK,KAAK,CAAC,cAAc,UAAU,CAAC,IAAI,GAAG;4DAAK;;;;;;;;;;;;;;;;;;sDAI7D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GA5OwB;KAAA", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/3/expense-tracker/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Upload, Receipt, Plus, Trash2, Edit } from 'lucide-react';\nimport ReceiptUpload from '@/components/ReceiptUpload';\n\ninterface Expense {\n  id: string;\n  merchant: string;\n  amount: number;\n  date: string;\n  category: string;\n}\n\nexport default function Home() {\n  const [expenses, setExpenses] = useState<Expense[]>([]);\n  const [showUpload, setShowUpload] = useState(false);\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  const handleReceiptData = (data: any) => {\n    const newExpense: Expense = {\n      id: Date.now().toString(),\n      merchant: data.merchant,\n      amount: data.amount,\n      date: data.date ? data.date.toLocaleDateString() : new Date().toLocaleDateString(),\n      category: data.suggestedCategory,\n    };\n    setExpenses(prev => [newExpense, ...prev]);\n  };\n\n  const handleAddExpense = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const formData = new FormData(e.currentTarget);\n    const newExpense: Expense = {\n      id: Date.now().toString(),\n      merchant: formData.get('merchant') as string,\n      amount: parseFloat(formData.get('amount') as string),\n      date: formData.get('date') as string,\n      category: formData.get('category') as string,\n    };\n    setExpenses(prev => [newExpense, ...prev]);\n    setShowAddForm(false);\n    e.currentTarget.reset();\n  };\n\n  const deleteExpense = (id: string) => {\n    setExpenses(prev => prev.filter(expense => expense.id !== id));\n  };\n\n  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Receipt className=\"h-8 w-8 text-blue-600\" />\n              <h1 className=\"ml-2 text-xl font-bold text-gray-900\">Expense Tracker</h1>\n            </div>\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={() => setShowUpload(true)}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\n              >\n                <Upload className=\"h-4 w-4\" />\n                <span>Scan Receipt</span>\n              </button>\n              <button\n                onClick={() => setShowAddForm(true)}\n                className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2\"\n              >\n                <Plus className=\"h-4 w-4\" />\n                <span>Add Expense</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Summary */}\n        <div className=\"bg-white rounded-lg shadow p-6 mb-8\">\n          <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Summary</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <p className=\"text-2xl font-bold text-gray-900\">${totalExpenses.toFixed(2)}</p>\n              <p className=\"text-sm text-gray-500\">Total Expenses</p>\n            </div>\n            <div className=\"text-center\">\n              <p className=\"text-2xl font-bold text-gray-900\">{expenses.length}</p>\n              <p className=\"text-sm text-gray-500\">Transactions</p>\n            </div>\n            <div className=\"text-center\">\n              <p className=\"text-2xl font-bold text-gray-900\">\n                ${expenses.length > 0 ? (totalExpenses / expenses.length).toFixed(2) : '0.00'}\n              </p>\n              <p className=\"text-sm text-gray-500\">Average</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Expenses List */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Recent Expenses</h3>\n          </div>\n          <div className=\"divide-y divide-gray-200\">\n            {expenses.length === 0 ? (\n              <div className=\"p-8 text-center text-gray-500\">\n                <Receipt className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                <p>No expenses yet. Start by scanning a receipt or adding an expense manually!</p>\n              </div>\n            ) : (\n              expenses.map((expense) => (\n                <div key={expense.id} className=\"p-6 flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between\">\n                      <h4 className=\"text-sm font-medium text-gray-900\">{expense.merchant}</h4>\n                      <p className=\"text-lg font-semibold text-gray-900\">${expense.amount.toFixed(2)}</p>\n                    </div>\n                    <div className=\"mt-1 flex items-center space-x-4 text-sm text-gray-500\">\n                      <span>{expense.category}</span>\n                      <span>•</span>\n                      <span>{expense.date}</span>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => deleteExpense(expense.id)}\n                    className=\"ml-4 text-red-600 hover:text-red-800\"\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n      </main>\n\n      {/* Receipt Upload Modal */}\n      {showUpload && (\n        <ReceiptUpload\n          onDataExtracted={handleReceiptData}\n          onClose={() => setShowUpload(false)}\n        />\n      )}\n\n      {/* Add Expense Modal */}\n      {showAddForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Add Expense</h2>\n            <form onSubmit={handleAddExpense} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Merchant\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"merchant\"\n                  required\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Store name\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Amount\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"amount\"\n                  step=\"0.01\"\n                  required\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"0.00\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Date\n                </label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  required\n                  defaultValue={new Date().toISOString().split('T')[0]}\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category\n                </label>\n                <select\n                  name=\"category\"\n                  required\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"Food & Dining\">Food & Dining</option>\n                  <option value=\"Transportation\">Transportation</option>\n                  <option value=\"Shopping\">Shopping</option>\n                  <option value=\"Entertainment\">Entertainment</option>\n                  <option value=\"Bills & Utilities\">Bills & Utilities</option>\n                  <option value=\"Healthcare\">Healthcare</option>\n                  <option value=\"Education\">Education</option>\n                  <option value=\"Travel\">Travel</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div className=\"flex space-x-3 pt-4\">\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700\"\n                >\n                  Add Expense\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => setShowAddForm(false)}\n                  className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400\"\n                >\n                  Cancel\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAce,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,MAAM;YACnB,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,kBAAkB,KAAK,IAAI,OAAO,kBAAkB;YAChF,UAAU,KAAK,iBAAiB;QAClC;QACA,YAAY,CAAA,OAAQ;gBAAC;mBAAe;aAAK;IAC3C;IAEA,MAAM,mBAAmB,CAAC;QACxB,EAAE,cAAc;QAChB,MAAM,WAAW,IAAI,SAAS,EAAE,aAAa;QAC7C,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,SAAS,GAAG,CAAC;YACvB,QAAQ,WAAW,SAAS,GAAG,CAAC;YAChC,MAAM,SAAS,GAAG,CAAC;YACnB,UAAU,SAAS,GAAG,CAAC;QACzB;QACA,YAAY,CAAA,OAAQ;gBAAC;mBAAe;aAAK;QACzC,eAAe;QACf,EAAE,aAAa,CAAC,KAAK;IACvB;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC5D;IAEA,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;IAE9E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;;;;;;;0CAEvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;oDAAmC;oDAAE,cAAc,OAAO,CAAC;;;;;;;0DACxE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC,SAAS,MAAM;;;;;;0DAChE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;oDAAmC;oDAC5C,SAAS,MAAM,GAAG,IAAI,CAAC,gBAAgB,SAAS,MAAM,EAAE,OAAO,CAAC,KAAK;;;;;;;0DAEzE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;sDAAE;;;;;;;;;;;2CAGL,SAAS,GAAG,CAAC,CAAC,wBACZ,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAqC,QAAQ,QAAQ;;;;;;0EACnE,6LAAC;gEAAE,WAAU;;oEAAsC;oEAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;kEAE9E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,QAAQ,QAAQ;;;;;;0EACvB,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAM,QAAQ,IAAI;;;;;;;;;;;;;;;;;;0DAGvB,6LAAC;gDACC,SAAS,IAAM,cAAc,QAAQ,EAAE;gDACvC,WAAU;0DAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;uCAhBZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;YA0B7B,4BACC,6LAAC,sIAAA,CAAA,UAAa;gBACZ,iBAAiB;gBACjB,SAAS,IAAM,cAAc;;;;;;YAKhC,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAK,UAAU;4BAAkB,WAAU;;8CAC1C,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;4CACpD,WAAU;;;;;;;;;;;;8CAGd,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,QAAQ;4CACR,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,6LAAC;oDAAO,OAAM;8DAAiB;;;;;;8DAC/B,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,6LAAC;oDAAO,OAAM;8DAAoB;;;;;;8DAClC,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAG1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA5NwB;KAAA", "debugId": null}}]}