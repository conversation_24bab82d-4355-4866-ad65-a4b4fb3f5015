# Expense Tracker with OCR Receipt Scanning

A modern expense tracking application built with Next.js that uses OCR technology to automatically extract data from receipt images. Track your expenses, analyze spending patterns, and manage budgets with ease.

## Features

- 📸 **OCR Receipt Scanning**: Upload receipt images and automatically extract merchant, amount, date, and category information
- 💰 **Expense Management**: Add, view, and delete expenses with an intuitive interface
- 📊 **Real-time Analytics**: View spending summaries and statistics
- 💾 **Local Storage**: Data persists in your browser's local storage
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
- 🎨 **Modern UI**: Clean, professional interface built with Tailwind CSS

## Demo Features

- **Scan Receipt**: Upload any receipt image to see OCR extraction in action (currently uses simulated OCR for demo purposes)
- **Add Expense**: Manually add expenses with form validation
- **Demo <PERSON>ton**: Quickly add sample expenses to test the interface
- **Clear All**: Reset all data and start fresh

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd expense-tracker
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## How to Use

### Scanning Receipts

1. Click the **"Scan Receipt"** button
2. Upload an image of your receipt (JPEG, PNG, or WebP)
3. Wait for the OCR processing to complete
4. Review the extracted data (merchant, amount, date, category)
5. Click **"Use This Data"** to add the expense

### Adding Expenses Manually

1. Click the **"Add Expense"** button
2. Fill in the expense details:
   - Merchant name
   - Amount
   - Date
   - Category
3. Click **"Add Expense"** to save

### Demo Mode

Click the **"Demo"** button to quickly add sample expenses and see how the interface works with data.

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **OCR**: Tesseract.js (for production implementation)
- **Storage**: Browser Local Storage
- **Build Tool**: Turbopack

## Project Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Main application page
│   └── globals.css         # Global styles
├── components/
│   └── ReceiptUpload.tsx   # Receipt upload and OCR component
└── lib/
    ├── ocr.ts              # OCR processing utilities
    ├── receipt-parser.ts   # Receipt text parsing logic
    ├── database.ts         # Database operations (Supabase)
    ├── validations.ts      # Form validation schemas
    ├── budget-utils.ts     # Budget calculation utilities
    ├── savings-utils.ts    # Savings goal utilities
    └── report-generator.ts # PDF report generation
```

## Future Enhancements

- **Real OCR Integration**: Replace simulated OCR with actual Tesseract.js implementation
- **Database Integration**: Connect to Supabase for persistent data storage
- **Budget Tracking**: Set and monitor spending budgets by category
- **Savings Goals**: Track progress toward financial goals
- **Monthly Reports**: Generate detailed PDF reports
- **Data Visualization**: Interactive charts and spending analysis
- **Export Features**: CSV and PDF export capabilities
- **User Authentication**: Multi-user support with secure login

## Development

### Running Tests

```bash
npm test
```

### Building for Production

```bash
npm run build
npm start
```

### Linting

```bash
npm run lint
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Icons by [Lucide](https://lucide.dev/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- OCR powered by [Tesseract.js](https://tesseract.projectnaptha.com/)
