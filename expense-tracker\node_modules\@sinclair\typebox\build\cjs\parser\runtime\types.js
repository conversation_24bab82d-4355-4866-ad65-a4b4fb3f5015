"use strict";

Object.defineProperty(exports, "__esModule", { value: true });
exports.As = exports.Identity = void 0;
exports.Context = Context;
exports.Array = Array;
exports.Const = Const;
exports.Ref = Ref;
exports.String = String;
exports.Ident = Ident;
exports.Number = Number;
exports.Optional = Optional;
exports.Tuple = Tuple;
exports.Union = Union;
/** Maps input to output. This is the default Mapping */
const Identity = (value) => value;
exports.Identity = Identity;
/** Maps the output as the given parameter T */
// prettier-ignore
const As = (mapping) => (_) => mapping;
exports.As = As;
/** `[Context]` Creates a Context Parser */
function Context(...args) {
    const [left, right, mapping] = args.length === 3 ? [args[0], args[1], args[2]] : [args[0], args[1], exports.Identity];
    return { type: 'Context', left, right, mapping };
}
/** `[EBNF]` Creates an Array Parser */
function Array(...args) {
    const [parser, mapping] = args.length === 2 ? [args[0], args[1]] : [args[0], exports.Identity];
    return { type: 'Array', parser, mapping };
}
/** `[TERM]` Creates a Const Parser */
function Const(...args) {
    const [value, mapping] = args.length === 2 ? [args[0], args[1]] : [args[0], exports.Identity];
    return { type: 'Const', value, mapping };
}
/** `[BNF]` Creates a Ref Parser. This Parser can only be used in the context of a Module */
function Ref(...args) {
    const [ref, mapping] = args.length === 2 ? [args[0], args[1]] : [args[0], exports.Identity];
    return { type: 'Ref', ref, mapping };
}
/** `[TERM]` Creates a String Parser. Options are an array of permissable quote characters */
function String(...params) {
    const [options, mapping] = params.length === 2 ? [params[0], params[1]] : [params[0], exports.Identity];
    return { type: 'String', options, mapping };
}
/** `[TERM]` Creates an Ident Parser where Ident matches any valid JavaScript identifier */
function Ident(...params) {
    const mapping = params.length === 1 ? params[0] : exports.Identity;
    return { type: 'Ident', mapping };
}
/** `[TERM]` Creates an Number Parser */
function Number(...params) {
    const mapping = params.length === 1 ? params[0] : exports.Identity;
    return { type: 'Number', mapping };
}
/** `[EBNF]` Creates an Optional Parser */
function Optional(...args) {
    const [parser, mapping] = args.length === 2 ? [args[0], args[1]] : [args[0], exports.Identity];
    return { type: 'Optional', parser, mapping };
}
/** `[BNF]` Creates a Tuple Parser */
function Tuple(...args) {
    const [parsers, mapping] = args.length === 2 ? [args[0], args[1]] : [args[0], exports.Identity];
    return { type: 'Tuple', parsers, mapping };
}
/** `[BNF]` Creates a Union parser */
function Union(...args) {
    const [parsers, mapping] = args.length === 2 ? [args[0], args[1]] : [args[0], exports.Identity];
    return { type: 'Union', parsers, mapping };
}
