@import "tailwindcss";

/* Custom CSS Variables for Themes */
:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Theme Variables */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-modal: #ffffff;

  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;

  --accent-primary: #3b82f6;
  --accent-secondary: #6366f1;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-error: #ef4444;
  --accent-info: #06b6d4;

  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-focus: #3b82f6;

  --glow-primary: rgba(59, 130, 246, 0.3);
  --glow-secondary: rgba(99, 102, 241, 0.3);
  --glow-accent: rgba(16, 185, 129, 0.3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 3D Transform Utilities */
.perspective-1000 {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Cyber Theme 3D Hover Effects */
.hover\:rotateX-2:hover {
  transform: rotateX(2deg) translateZ(0);
}

.hover\:rotateY-2:hover {
  transform: rotateY(2deg) translateZ(0);
}

.hover\:rotateX-3:hover {
  transform: rotateX(3deg) translateZ(0);
}

.hover\:rotateY-3:hover {
  transform: rotateY(3deg) translateZ(0);
}

.hover\:rotateX-5:hover {
  transform: rotateX(5deg) translateZ(0);
}

.hover\:rotateY-5:hover {
  transform: rotateY(5deg) translateZ(0);
}

.hover\:rotateX-4:hover {
  transform: rotateX(4deg) translateZ(0);
}

.hover\:rotateY-4:hover {
  transform: rotateY(4deg) translateZ(0);
}

/* Scale Variants */
.hover\:scale-108:hover {
  transform: scale(1.08);
}

.hover\:scale-110:hover {
  transform: scale(1.10);
}

/* Glow Effects */
.shadow-glow {
  box-shadow: 0 0 20px var(--glow-primary);
}

/* Futuristic Animations */
@keyframes cyber-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 255, 136, 0.6);
  }
}

@keyframes neon-pulse {
  0%, 100% {
    box-shadow: 0 0 25px rgba(255, 0, 110, 0.4);
  }
  50% {
    box-shadow: 0 0 45px rgba(255, 0, 110, 0.7);
  }
}

@keyframes matrix-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 255, 0, 0.8);
  }
}

.animate-cyber-pulse {
  animation: cyber-pulse 2s ease-in-out infinite;
}

.animate-neon-pulse {
  animation: neon-pulse 2s ease-in-out infinite;
}

.animate-matrix-pulse {
  animation: matrix-pulse 2s ease-in-out infinite;
}

/* Smooth Theme Transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Custom Scrollbar for Dark Themes */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary);
}

/* Futuristic Text Effects */
.text-cyber {
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.text-neon {
  color: #ff006e;
  text-shadow: 0 0 10px rgba(255, 0, 110, 0.5);
}

.text-matrix {
  color: #00ff00;
  text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

/* Backdrop Blur Support */
.backdrop-blur-cyber {
  backdrop-filter: blur(12px) saturate(180%);
}

/* Loading Animations */
@keyframes cyber-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.cyber-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.4), transparent);
  animation: cyber-loading 1.5s infinite;
}

/* RTL Language Support */
.rtl {
  direction: rtl;
}

/* RTL-specific adjustments */
.rtl .flex {
  flex-direction: row-reverse;
}

.rtl .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

.rtl .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

.rtl .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

.rtl .space-x-6 > * + * {
  margin-left: 0;
  margin-right: 1.5rem;
}

.rtl .space-x-8 > * + * {
  margin-left: 0;
  margin-right: 2rem;
}

/* RTL text alignment */
.rtl .text-left {
  text-align: right;
}

.rtl .text-right {
  text-align: left;
}

/* RTL positioning */
.rtl .left-0 {
  left: auto;
  right: 0;
}

.rtl .right-0 {
  right: auto;
  left: 0;
}

.rtl .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

.rtl .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

.rtl .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

.rtl .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* RTL padding adjustments */
.rtl .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

.rtl .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

.rtl .pl-6 {
  padding-left: 0;
  padding-right: 1.5rem;
}

.rtl .pr-6 {
  padding-right: 0;
  padding-left: 1.5rem;
}

/* RTL border radius adjustments */
.rtl .rounded-l-lg {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.rtl .rounded-r-lg {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

/* RTL dropdown positioning */
.rtl .absolute.right-0 {
  right: auto;
  left: 0;
}

.rtl .absolute.left-0 {
  left: auto;
  right: 0;
}

/* RTL transform origin for animations */
.rtl .transform-gpu {
  transform-origin: right center;
}

/* RTL-specific font adjustments for Arabic and Urdu */
[lang="ar"], [lang="ur"] {
  font-family: 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
  line-height: 1.8;
}

/* RTL-specific font adjustments for other languages */
[lang="hi"] {
  font-family: 'Noto Sans Devanagari', 'Arial Unicode MS', sans-serif;
}

[lang="bn"] {
  font-family: 'Noto Sans Bengali', 'Arial Unicode MS', sans-serif;
}

[lang="pa"] {
  font-family: 'Noto Sans Gurmukhi', 'Arial Unicode MS', sans-serif;
}

[lang="zh"] {
  font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
}

[lang="ja"] {
  font-family: 'Noto Sans JP', 'Hiragino Sans', sans-serif;
}

[lang="ko"] {
  font-family: 'Noto Sans KR', 'Malgun Gothic', sans-serif;
}

[lang="ru"] {
  font-family: 'Noto Sans', 'Arial', sans-serif;
}
