'use client';

import { useState, useEffect } from 'react';
import { Upload, Receipt, Plus, Trash2, BarChart3, Camera, Palette, Globe } from 'lucide-react';
import ReceiptUpload from '@/components/ReceiptUpload';
import Dashboard from '@/components/Dashboard';
import CameraCapture from '@/components/CameraCapture';
import ExportPanel from '@/components/ExportPanel';
import ThemeSwitcher from '@/components/ThemeSwitcher';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { ThemedCard, ThemedButton, ThemedInput } from '@/components/ThemeComponents';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslate } from '@/contexts/LanguageContext';

interface Expense {
  id: string;
  merchant: string;
  amount: number;
  date: string;
  category: string;
}

// Sample data for demo
const sampleExpenses: Expense[] = [
  {
    id: '1',
    merchant: 'Starbucks Coffee',
    amount: 8.75,
    date: new Date().toLocaleDateString(),
    category: 'Food & Dining'
  },
  {
    id: '2',
    merchant: 'Shell Gas Station',
    amount: 41.08,
    date: new Date(Date.now() - 86400000).toLocaleDateString(), // Yesterday
    category: 'Transportation'
  },
  {
    id: '3',
    merchant: 'Walmart Supercenter',
    amount: 11.63,
    date: new Date(Date.now() - 172800000).toLocaleDateString(), // 2 days ago
    category: 'Shopping'
  }
];

export default function Home() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [showUpload, setShowUpload] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [showThemeSwitcher, setShowThemeSwitcher] = useState(false);
  const [showLanguageSwitcher, setShowLanguageSwitcher] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const { currentTheme, theme } = useTheme();
  const t = useTranslate;

  // Load expenses from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('expenses');
    if (saved) {
      setExpenses(JSON.parse(saved));
    } else {
      // Use sample data if no saved data
      setExpenses(sampleExpenses);
    }
  }, []);

  // Save expenses to localStorage whenever expenses change
  useEffect(() => {
    if (expenses.length > 0) {
      localStorage.setItem('expenses', JSON.stringify(expenses));
    }
  }, [expenses]);

  const handleReceiptData = (data: any) => {
    const newExpense: Expense = {
      id: Date.now().toString(),
      merchant: data.merchant,
      amount: data.amount,
      date: data.date ? data.date.toLocaleDateString() : new Date().toLocaleDateString(),
      category: data.suggestedCategory,
    };
    setExpenses(prev => [newExpense, ...prev]);
  };

  const handleAddExpense = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const newExpense: Expense = {
      id: Date.now().toString(),
      merchant: formData.get('merchant') as string,
      amount: parseFloat(formData.get('amount') as string),
      date: formData.get('date') as string,
      category: formData.get('category') as string,
    };
    setExpenses(prev => [newExpense, ...prev]);
    setShowAddForm(false);
    e.currentTarget.reset();
  };

  const deleteExpense = (id: string) => {
    setExpenses(prev => prev.filter(expense => expense.id !== id));
  };

  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

  return (
    <div
      className="min-h-screen transition-all duration-300"
      style={{ backgroundColor: theme.colors.bg.secondary }}
    >
      {/* Header */}
      <header
        className="shadow-sm border-b transition-all duration-300"
        style={{
          backgroundColor: theme.colors.bg.card,
          borderColor: theme.colors.border.primary
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Receipt
                className="h-8 w-8 transition-colors duration-300"
                style={{ color: theme.colors.accent.primary }}
              />
              <h1
                className={`ml-2 text-xl font-bold transition-colors duration-300 ${
                  currentTheme === 'cyber' ? 'text-cyber' :
                  currentTheme === 'neon' ? 'text-neon' :
                  currentTheme === 'matrix' ? 'text-matrix' : ''
                }`}
                style={{ color: theme.colors.text.primary }}
              >
                {t('Smart Expense Tracker')}
              </h1>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <ThemedButton
                onClick={() => setActiveTab('dashboard')}
                variant={activeTab === 'dashboard' ? 'primary' : 'secondary'}
                size="sm"
                cyber3D={currentTheme === 'cyber'}
                className="flex items-center space-x-2"
              >
                <BarChart3 className="h-4 w-4" />
                <span>{t('Dashboard')}</span>
              </ThemedButton>
              <ThemedButton
                onClick={() => setActiveTab('expenses')}
                variant={activeTab === 'expenses' ? 'primary' : 'secondary'}
                size="sm"
                cyber3D={currentTheme === 'cyber'}
                className="flex items-center space-x-2"
              >
                <Receipt className="h-4 w-4" />
                <span>{t('Expenses')}</span>
              </ThemedButton>
            </nav>
            <div className="flex space-x-3">
              <ThemedButton
                onClick={() => setShowCamera(true)}
                variant="primary"
                size="sm"
                cyber3D={currentTheme === 'cyber'}
                className="flex items-center space-x-2"
              >
                <Camera className="h-4 w-4" />
                <span className="hidden sm:inline">{t('Camera')}</span>
              </ThemedButton>
              <ThemedButton
                onClick={() => setShowUpload(true)}
                variant="secondary"
                size="sm"
                cyber3D={currentTheme === 'cyber'}
                className="flex items-center space-x-2"
              >
                <Upload className="h-4 w-4" />
                <span className="hidden sm:inline">{t('Upload')}</span>
              </ThemedButton>
              <ThemedButton
                onClick={() => setShowAddForm(true)}
                variant="success"
                size="sm"
                cyber3D={currentTheme === 'cyber'}
                className="flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">{t('Add')}</span>
              </ThemedButton>
              <ThemedButton
                onClick={() => {
                  // Demo: Add a random sample expense
                  const samples = [
                    { merchant: 'McDonald\'s', amount: 12.45, category: 'Food & Dining' },
                    { merchant: 'Target', amount: 67.89, category: 'Shopping' },
                    { merchant: 'Uber', amount: 15.30, category: 'Transportation' },
                    { merchant: 'Netflix', amount: 15.99, category: 'Entertainment' },
                    { merchant: 'Starbucks', amount: 8.75, category: 'Food & Dining' },
                    { merchant: 'Shell', amount: 41.08, category: 'Transportation' },
                    { merchant: 'Amazon', amount: 29.99, category: 'Shopping' },
                    { merchant: 'Gym Membership', amount: 45.00, category: 'Healthcare' }
                  ];
                  const sample = samples[Math.floor(Math.random() * samples.length)];
                  const newExpense: Expense = {
                    id: Date.now().toString(),
                    merchant: sample.merchant,
                    amount: sample.amount,
                    date: new Date().toLocaleDateString(),
                    category: sample.category,
                  };
                  setExpenses(prev => [newExpense, ...prev]);
                }}
                variant="warning"
                size="sm"
                cyber3D={currentTheme === 'cyber'}
                className="flex items-center space-x-2"
              >
                <Receipt className="h-4 w-4" />
                <span className="hidden sm:inline">{t('Demo')}</span>
              </ThemedButton>
              <ThemedButton
                onClick={() => setShowThemeSwitcher(true)}
                variant="secondary"
                size="sm"
                cyber3D={currentTheme === 'cyber'}
                className="flex items-center space-x-2"
              >
                <Palette className="h-4 w-4" />
                <span className="hidden sm:inline">{t('Themes')}</span>
              </ThemedButton>
              <LanguageSwitcher />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && <Dashboard expenses={expenses} />}

        {activeTab === 'expenses' && (
          <div className="space-y-6">
            {/* Export Panel */}
            <ExportPanel expenses={expenses} />

            {/* Quick Summary */}
            <ThemedCard className="p-6" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
              <h2
                className="text-lg font-medium mb-4"
                style={{ color: theme.colors.text.primary }}
              >
                {t('Quick Summary')}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p
                    className={`text-2xl font-bold ${
                      currentTheme === 'cyber' ? 'text-cyber' :
                      currentTheme === 'neon' ? 'text-neon' :
                      currentTheme === 'matrix' ? 'text-matrix' : ''
                    }`}
                    style={{ color: theme.colors.text.primary }}
                  >
                    ${totalExpenses.toFixed(2)}
                  </p>
                  <p
                    className="text-sm"
                    style={{ color: theme.colors.text.secondary }}
                  >
                    {t('Total Expenses')}
                  </p>
                </div>
                <div className="text-center">
                  <p
                    className={`text-2xl font-bold ${
                      currentTheme === 'cyber' ? 'text-cyber' :
                      currentTheme === 'neon' ? 'text-neon' :
                      currentTheme === 'matrix' ? 'text-matrix' : ''
                    }`}
                    style={{ color: theme.colors.text.primary }}
                  >
                    {expenses.length}
                  </p>
                  <p
                    className="text-sm"
                    style={{ color: theme.colors.text.secondary }}
                  >
                    {t('Transactions')}
                  </p>
                </div>
                <div className="text-center">
                  <p
                    className={`text-2xl font-bold ${
                      currentTheme === 'cyber' ? 'text-cyber' :
                      currentTheme === 'neon' ? 'text-neon' :
                      currentTheme === 'matrix' ? 'text-matrix' : ''
                    }`}
                    style={{ color: theme.colors.text.primary }}
                  >
                    ${expenses.length > 0 ? (totalExpenses / expenses.length).toFixed(2) : '0.00'}
                  </p>
                  <p
                    className="text-sm"
                    style={{ color: theme.colors.text.secondary }}
                  >
                    {t('Average')}
                  </p>
                </div>
              </div>
            </ThemedCard>

            {/* Expenses List */}
            <ThemedCard hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
              <div
                className="px-6 py-4 border-b flex justify-between items-center"
                style={{ borderColor: theme.colors.border.primary }}
              >
                <h3
                  className="text-lg font-medium"
                  style={{ color: theme.colors.text.primary }}
                >
                  All Expenses
                </h3>
                {expenses.length > 0 && (
                  <ThemedButton
                    onClick={() => {
                      setExpenses([]);
                      localStorage.removeItem('expenses');
                    }}
                    variant="error"
                    size="sm"
                    cyber3D={currentTheme === 'cyber'}
                  >
                    Clear All
                  </ThemedButton>
                )}
              </div>
              <div
                className="divide-y"
                style={{ borderColor: theme.colors.border.primary }}
              >
                {expenses.length === 0 ? (
                  <div className="p-8 text-center">
                    <Receipt
                      className="h-12 w-12 mx-auto mb-4"
                      style={{ color: theme.colors.text.tertiary }}
                    />
                    <p style={{ color: theme.colors.text.secondary }}>
                      No expenses yet. Start by scanning a receipt or adding an expense manually!
                    </p>
                  </div>
                ) : (
                  expenses.map((expense) => (
                    <div
                      key={expense.id}
                      className={`p-6 flex items-center justify-between transition-colors duration-200 ${
                        currentTheme === 'cyber' ? 'hover:bg-green-400/5' :
                        currentTheme === 'neon' ? 'hover:bg-pink-500/5' :
                        currentTheme === 'matrix' ? 'hover:bg-green-500/5' :
                        currentTheme === 'dark' ? 'hover:bg-gray-700/50' :
                        'hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4
                            className="text-sm font-medium"
                            style={{ color: theme.colors.text.primary }}
                          >
                            {expense.merchant}
                          </h4>
                          <p
                            className={`text-lg font-semibold ${
                              currentTheme === 'cyber' ? 'text-cyber' :
                              currentTheme === 'neon' ? 'text-neon' :
                              currentTheme === 'matrix' ? 'text-matrix' : ''
                            }`}
                            style={{ color: theme.colors.text.primary }}
                          >
                            ${expense.amount.toFixed(2)}
                          </p>
                        </div>
                        <div className="mt-1 flex items-center space-x-4 text-sm">
                          <span
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                            style={{
                              backgroundColor: `${theme.colors.accent.primary}20`,
                              color: theme.colors.accent.primary
                            }}
                          >
                            {expense.category}
                          </span>
                          <span style={{ color: theme.colors.text.tertiary }}>•</span>
                          <span style={{ color: theme.colors.text.secondary }}>{expense.date}</span>
                        </div>
                      </div>
                      <ThemedButton
                        onClick={() => deleteExpense(expense.id)}
                        variant="error"
                        size="sm"
                        cyber3D={currentTheme === 'cyber'}
                        className="ml-4 !p-2"
                      >
                        <Trash2 className="h-4 w-4" />
                      </ThemedButton>
                    </div>
                  ))
                )}
              </div>
            </ThemedCard>
          </div>
        )}
      </main>

      {/* Camera Capture */}
      {showCamera && (
        <CameraCapture
          onCapture={(file) => {
            setShowCamera(false);
            // Process the captured file
            const reader = new FileReader();
            reader.onload = () => {
              // For demo, we'll simulate processing the captured image
              const mockData = {
                merchant: 'Camera Captured Receipt',
                amount: Math.round((Math.random() * 50 + 10) * 100) / 100,
                date: new Date(),
                suggestedCategory: 'Food & Dining'
              };
              handleReceiptData(mockData);
            };
            reader.readAsDataURL(file);
          }}
          onClose={() => setShowCamera(false)}
        />
      )}

      {/* Receipt Upload Modal */}
      {showUpload && (
        <ReceiptUpload
          onDataExtracted={handleReceiptData}
          onClose={() => setShowUpload(false)}
        />
      )}

      {/* Theme Switcher Modal */}
      {showThemeSwitcher && (
        <ThemeSwitcher
          showModal={true}
          onClose={() => setShowThemeSwitcher(false)}
        />
      )}

      {/* Language Switcher Modal */}
      {showLanguageSwitcher && (
        <LanguageSwitcher
          showModal={true}
          onClose={() => setShowLanguageSwitcher(false)}
        />
      )}

      {/* Add Expense Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ThemedCard className="max-w-md w-full p-6" hover3D={currentTheme === 'cyber'}>
            <h2
              className={`text-xl font-semibold mb-4 ${
                currentTheme === 'cyber' ? 'text-cyber' :
                currentTheme === 'neon' ? 'text-neon' :
                currentTheme === 'matrix' ? 'text-matrix' : ''
              }`}
              style={{ color: theme.colors.text.primary }}
            >
              Add Expense
            </h2>
            <form onSubmit={handleAddExpense} className="space-y-4">
              <div>
                <label
                  className="block text-sm font-medium mb-1"
                  style={{ color: theme.colors.text.secondary }}
                >
                  Merchant
                </label>
                <ThemedInput
                  type="text"
                  name="merchant"
                  placeholder="Store name"
                />
              </div>
              <div>
                <label
                  className="block text-sm font-medium mb-1"
                  style={{ color: theme.colors.text.secondary }}
                >
                  Amount
                </label>
                <ThemedInput
                  type="number"
                  name="amount"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label
                  className="block text-sm font-medium mb-1"
                  style={{ color: theme.colors.text.secondary }}
                >
                  Date
                </label>
                <ThemedInput
                  type="date"
                  name="date"
                  value={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div>
                <label
                  className="block text-sm font-medium mb-1"
                  style={{ color: theme.colors.text.secondary }}
                >
                  Category
                </label>
                <select
                  name="category"
                  required
                  className={`w-full px-4 py-2 rounded-lg transition-all duration-300 ease-out focus:outline-none focus:ring-2 ${
                    currentTheme === 'cyber' ? 'bg-black/80 border-2 border-green-400/50 text-green-400 focus:border-green-400 focus:ring-green-400/30' :
                    currentTheme === 'neon' ? 'bg-gray-900/80 border-2 border-pink-500/50 text-pink-500 focus:border-pink-500 focus:ring-pink-500/30' :
                    currentTheme === 'matrix' ? 'bg-black/80 border-2 border-green-500/50 text-green-500 focus:border-green-500 focus:ring-green-500/30' :
                    currentTheme === 'dark' ? 'bg-gray-800 border border-gray-600 text-gray-100 focus:border-blue-400 focus:ring-blue-400/20' :
                    'bg-white border border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500/20'
                  }`}
                >
                  <option value="Food & Dining">Food & Dining</option>
                  <option value="Transportation">Transportation</option>
                  <option value="Shopping">Shopping</option>
                  <option value="Entertainment">Entertainment</option>
                  <option value="Bills & Utilities">Bills & Utilities</option>
                  <option value="Healthcare">Healthcare</option>
                  <option value="Education">Education</option>
                  <option value="Travel">Travel</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div className="flex space-x-3 pt-4">
                <ThemedButton
                  type="submit"
                  variant="success"
                  cyber3D={currentTheme === 'cyber'}
                  className="flex-1"
                >
                  Add Expense
                </ThemedButton>
                <ThemedButton
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  variant="secondary"
                  cyber3D={currentTheme === 'cyber'}
                  className="flex-1"
                >
                  Cancel
                </ThemedButton>
              </div>
            </form>
          </ThemedCard>
        </div>
      )}
    </div>
  );
}


