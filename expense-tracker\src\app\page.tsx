'use client';

import { useState, useEffect } from 'react';
import { Upload, Receipt, Plus, Trash2, BarChart3, Camera, Smartphone } from 'lucide-react';
import ReceiptUpload from '@/components/ReceiptUpload';
import Dashboard from '@/components/Dashboard';
import CameraCapture from '@/components/CameraCapture';
import ExportPanel from '@/components/ExportPanel';

interface Expense {
  id: string;
  merchant: string;
  amount: number;
  date: string;
  category: string;
}

// Sample data for demo
const sampleExpenses: Expense[] = [
  {
    id: '1',
    merchant: 'Starbucks Coffee',
    amount: 8.75,
    date: new Date().toLocaleDateString(),
    category: 'Food & Dining'
  },
  {
    id: '2',
    merchant: 'Shell Gas Station',
    amount: 41.08,
    date: new Date(Date.now() - 86400000).toLocaleDateString(), // Yesterday
    category: 'Transportation'
  },
  {
    id: '3',
    merchant: 'Walmart Supercenter',
    amount: 11.63,
    date: new Date(Date.now() - 172800000).toLocaleDateString(), // 2 days ago
    category: 'Shopping'
  }
];

export default function Home() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [showUpload, setShowUpload] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');

  // Load expenses from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('expenses');
    if (saved) {
      setExpenses(JSON.parse(saved));
    } else {
      // Use sample data if no saved data
      setExpenses(sampleExpenses);
    }
  }, []);

  // Save expenses to localStorage whenever expenses change
  useEffect(() => {
    if (expenses.length > 0) {
      localStorage.setItem('expenses', JSON.stringify(expenses));
    }
  }, [expenses]);

  const handleReceiptData = (data: any) => {
    const newExpense: Expense = {
      id: Date.now().toString(),
      merchant: data.merchant,
      amount: data.amount,
      date: data.date ? data.date.toLocaleDateString() : new Date().toLocaleDateString(),
      category: data.suggestedCategory,
    };
    setExpenses(prev => [newExpense, ...prev]);
  };

  const handleAddExpense = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const newExpense: Expense = {
      id: Date.now().toString(),
      merchant: formData.get('merchant') as string,
      amount: parseFloat(formData.get('amount') as string),
      date: formData.get('date') as string,
      category: formData.get('category') as string,
    };
    setExpenses(prev => [newExpense, ...prev]);
    setShowAddForm(false);
    e.currentTarget.reset();
  };

  const deleteExpense = (id: string) => {
    setExpenses(prev => prev.filter(expense => expense.id !== id));
  };

  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Receipt className="h-8 w-8 text-blue-600" />
              <h1 className="ml-2 text-xl font-bold text-gray-900">Smart Expense Tracker</h1>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeTab === 'dashboard'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <BarChart3 className="h-4 w-4 inline mr-2" />
                Dashboard
              </button>
              <button
                onClick={() => setActiveTab('expenses')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeTab === 'expenses'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Receipt className="h-4 w-4 inline mr-2" />
                Expenses
              </button>
            </nav>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowCamera(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 shadow-sm"
              >
                <Camera className="h-4 w-4" />
                <span className="hidden sm:inline">Camera</span>
              </button>
              <button
                onClick={() => setShowUpload(true)}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 flex items-center space-x-2 shadow-sm"
              >
                <Upload className="h-4 w-4" />
                <span className="hidden sm:inline">Upload</span>
              </button>
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2 shadow-sm"
              >
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">Add Expense</span>
              </button>
              <button
                onClick={() => {
                  // Demo: Add a random sample expense
                  const samples = [
                    { merchant: 'McDonald\'s', amount: 12.45, category: 'Food & Dining' },
                    { merchant: 'Target', amount: 67.89, category: 'Shopping' },
                    { merchant: 'Uber', amount: 15.30, category: 'Transportation' },
                    { merchant: 'Netflix', amount: 15.99, category: 'Entertainment' },
                    { merchant: 'Starbucks', amount: 8.75, category: 'Food & Dining' },
                    { merchant: 'Shell', amount: 41.08, category: 'Transportation' },
                    { merchant: 'Amazon', amount: 29.99, category: 'Shopping' },
                    { merchant: 'Gym Membership', amount: 45.00, category: 'Healthcare' }
                  ];
                  const sample = samples[Math.floor(Math.random() * samples.length)];
                  const newExpense: Expense = {
                    id: Date.now().toString(),
                    merchant: sample.merchant,
                    amount: sample.amount,
                    date: new Date().toLocaleDateString(),
                    category: sample.category,
                  };
                  setExpenses(prev => [newExpense, ...prev]);
                }}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center space-x-2 shadow-sm"
              >
                <Receipt className="h-4 w-4" />
                <span className="hidden sm:inline">Demo</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && <Dashboard expenses={expenses} />}

        {activeTab === 'expenses' && (
          <div className="space-y-6">
            {/* Export Panel */}
            <ExportPanel expenses={expenses} />

            {/* Quick Summary */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">${totalExpenses.toFixed(2)}</p>
                  <p className="text-sm text-gray-500">Total Expenses</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">{expenses.length}</p>
                  <p className="text-sm text-gray-500">Transactions</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">
                    ${expenses.length > 0 ? (totalExpenses / expenses.length).toFixed(2) : '0.00'}
                  </p>
                  <p className="text-sm text-gray-500">Average</p>
                </div>
              </div>
            </div>

            {/* Expenses List */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">All Expenses</h3>
                {expenses.length > 0 && (
                  <button
                    onClick={() => {
                      setExpenses([]);
                      localStorage.removeItem('expenses');
                    }}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Clear All
                  </button>
                )}
              </div>
              <div className="divide-y divide-gray-200">
                {expenses.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <Receipt className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No expenses yet. Start by scanning a receipt or adding an expense manually!</p>
                  </div>
                ) : (
                  expenses.map((expense) => (
                    <div key={expense.id} className="p-6 flex items-center justify-between hover:bg-gray-50">
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-900">{expense.merchant}</h4>
                          <p className="text-lg font-semibold text-gray-900">${expense.amount.toFixed(2)}</p>
                        </div>
                        <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {expense.category}
                          </span>
                          <span>•</span>
                          <span>{expense.date}</span>
                        </div>
                      </div>
                      <button
                        onClick={() => deleteExpense(expense.id)}
                        className="ml-4 text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Camera Capture */}
      {showCamera && (
        <CameraCapture
          onCapture={(file) => {
            setShowCamera(false);
            // Process the captured file
            const reader = new FileReader();
            reader.onload = () => {
              // For demo, we'll simulate processing the captured image
              const mockData = {
                merchant: 'Camera Captured Receipt',
                amount: Math.round((Math.random() * 50 + 10) * 100) / 100,
                date: new Date(),
                suggestedCategory: 'Food & Dining'
              };
              handleReceiptData(mockData);
            };
            reader.readAsDataURL(file);
          }}
          onClose={() => setShowCamera(false)}
        />
      )}

      {/* Receipt Upload Modal */}
      {showUpload && (
        <ReceiptUpload
          onDataExtracted={handleReceiptData}
          onClose={() => setShowUpload(false)}
        />
      )}

      {/* Add Expense Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Add Expense</h2>
            <form onSubmit={handleAddExpense} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Merchant
                </label>
                <input
                  type="text"
                  name="merchant"
                  required
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Store name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount
                </label>
                <input
                  type="number"
                  name="amount"
                  step="0.01"
                  required
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  type="date"
                  name="date"
                  required
                  defaultValue={new Date().toISOString().split('T')[0]}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  name="category"
                  required
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Food & Dining">Food & Dining</option>
                  <option value="Transportation">Transportation</option>
                  <option value="Shopping">Shopping</option>
                  <option value="Entertainment">Entertainment</option>
                  <option value="Bills & Utilities">Bills & Utilities</option>
                  <option value="Healthcare">Healthcare</option>
                  <option value="Education">Education</option>
                  <option value="Travel">Travel</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                >
                  Add Expense
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}


