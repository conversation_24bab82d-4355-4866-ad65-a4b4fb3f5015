'use client';

import { useState, useRef, useCallback } from 'react';
import { Camera, X, RotateCcw, Check, Zap } from 'lucide-react';

interface CameraCaptureProps {
  onCapture: (file: File) => void;
  onClose: () => void;
}

export default function CameraCapture({ onCapture, onClose }: CameraCaptureProps) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const startCamera = useCallback(async () => {
    try {
      setError(null);
      
      // Request camera access with optimal settings for document capture
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera on mobile
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          aspectRatio: { ideal: 16/9 }
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsStreaming(true);
      }
    } catch (err) {
      console.error('Camera access error:', err);
      setError('Unable to access camera. Please check permissions and try again.');
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsStreaming(false);
  }, []);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Apply image enhancements for better OCR
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
    enhanceImageForOCR(imageData);
    context.putImageData(imageData, 0, 0);

    // Convert to data URL
    const dataURL = canvas.toDataURL('image/jpeg', 0.9);
    setCapturedImage(dataURL);
    stopCamera();
  }, [stopCamera]);

  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    startCamera();
  }, [startCamera]);

  const confirmCapture = useCallback(() => {
    if (!capturedImage) return;

    // Convert data URL to File
    fetch(capturedImage)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], `receipt-${Date.now()}.jpg`, { type: 'image/jpeg' });
        onCapture(file);
        onClose();
      })
      .catch(err => {
        console.error('Error converting image:', err);
        setError('Failed to process captured image');
      });
  }, [capturedImage, onCapture, onClose]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-black text-white">
        <div className="flex items-center space-x-3">
          <Camera className="h-6 w-6" />
          <div>
            <h2 className="text-lg font-semibold">Capture Receipt</h2>
            <p className="text-sm text-gray-300">Position receipt in frame and tap capture</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-2 rounded-full bg-gray-800 hover:bg-gray-700"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      {/* Camera View */}
      <div className="flex-1 relative overflow-hidden">
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 z-10">
            <div className="bg-red-600 text-white p-6 rounded-lg max-w-sm mx-4 text-center">
              <p className="mb-4">{error}</p>
              <button
                onClick={() => setError(null)}
                className="bg-white text-red-600 px-4 py-2 rounded-lg font-medium"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {capturedImage ? (
          // Show captured image
          <div className="relative w-full h-full flex items-center justify-center bg-black">
            <img
              src={capturedImage}
              alt="Captured receipt"
              className="max-w-full max-h-full object-contain"
            />
            
            {/* Overlay with enhancement indicator */}
            <div className="absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm flex items-center space-x-2">
              <Zap className="h-4 w-4" />
              <span>Enhanced for OCR</span>
            </div>
          </div>
        ) : (
          // Show camera stream
          <div className="relative w-full h-full">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
              onLoadedMetadata={startCamera}
            />
            
            {/* Camera overlay guides */}
            <div className="absolute inset-0 pointer-events-none">
              {/* Corner guides */}
              <div className="absolute top-8 left-8 w-8 h-8 border-l-2 border-t-2 border-white opacity-75"></div>
              <div className="absolute top-8 right-8 w-8 h-8 border-r-2 border-t-2 border-white opacity-75"></div>
              <div className="absolute bottom-8 left-8 w-8 h-8 border-l-2 border-b-2 border-white opacity-75"></div>
              <div className="absolute bottom-8 right-8 w-8 h-8 border-r-2 border-b-2 border-white opacity-75"></div>
              
              {/* Center guide */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="border-2 border-dashed border-white opacity-50 w-80 h-60 rounded-lg"></div>
              </div>
              
              {/* Instructions */}
              <div className="absolute bottom-32 left-0 right-0 text-center">
                <div className="bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg mx-4">
                  <p className="text-sm">Position receipt within the frame</p>
                  <p className="text-xs text-gray-300 mt-1">Ensure good lighting and avoid shadows</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Hidden canvas for image processing */}
        <canvas ref={canvasRef} className="hidden" />
      </div>

      {/* Controls */}
      <div className="bg-black p-6">
        {capturedImage ? (
          // Captured image controls
          <div className="flex items-center justify-center space-x-6">
            <button
              onClick={retakePhoto}
              className="flex items-center space-x-2 bg-gray-700 text-white px-6 py-3 rounded-full hover:bg-gray-600"
            >
              <RotateCcw className="h-5 w-5" />
              <span>Retake</span>
            </button>
            <button
              onClick={confirmCapture}
              className="flex items-center space-x-2 bg-green-600 text-white px-8 py-3 rounded-full hover:bg-green-700"
            >
              <Check className="h-5 w-5" />
              <span>Use Photo</span>
            </button>
          </div>
        ) : (
          // Camera controls
          <div className="flex items-center justify-center">
            {isStreaming ? (
              <button
                onClick={capturePhoto}
                className="w-20 h-20 bg-white rounded-full border-4 border-gray-300 hover:border-blue-500 transition-colors flex items-center justify-center"
              >
                <div className="w-16 h-16 bg-white rounded-full shadow-lg"></div>
              </button>
            ) : (
              <button
                onClick={startCamera}
                className="flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-full hover:bg-blue-700"
              >
                <Camera className="h-5 w-5" />
                <span>Start Camera</span>
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Enhance image for better OCR results
 */
function enhanceImageForOCR(imageData: ImageData) {
  const data = imageData.data;
  
  for (let i = 0; i < data.length; i += 4) {
    // Convert to grayscale
    const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
    
    // Increase contrast
    const contrast = 1.5;
    const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255));
    const enhanced = Math.min(255, Math.max(0, factor * (gray - 128) + 128));
    
    // Apply to all channels
    data[i] = enhanced;     // Red
    data[i + 1] = enhanced; // Green
    data[i + 2] = enhanced; // Blue
    // Alpha channel remains unchanged
  }
}
