'use client';

import { useMemo } from 'react';
import { TrendingUp, TrendingDown, DollarSign, ShoppingCart, Calendar, Target } from 'lucide-react';
import { SpendingTrendChart, CategoryBreakdownChart, MonthlyComparisonChart } from './Charts';
import InsightsPanel from './InsightsPanel';
import { ThemedCard } from './ThemeComponents';
import { useTheme } from '@/contexts/ThemeContext';

interface Expense {
  id: string;
  merchant: string;
  amount: number;
  date: string;
  category: string;
}

interface DashboardProps {
  expenses: Expense[];
}

export default function Dashboard({ expenses }: DashboardProps) {
  const { currentTheme, theme } = useTheme();

  // Calculate analytics
  const analytics = useMemo(() => {
    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();
    const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
    const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;

    // This month expenses
    const thisMonthExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate.getMonth() === thisMonth && expenseDate.getFullYear() === thisYear;
    });

    // Last month expenses
    const lastMonthExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate.getMonth() === lastMonth && expenseDate.getFullYear() === lastMonthYear;
    });

    // Today's expenses
    const today = now.toDateString();
    const todayExpenses = expenses.filter(expense => 
      new Date(expense.date).toDateString() === today
    );

    // This week expenses
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay());
    const thisWeekExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate >= weekStart;
    });

    // Category breakdown
    const categoryTotals = expenses.reduce((acc, expense) => {
      acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
      return acc;
    }, {} as Record<string, number>);

    // Spending trend (last 30 days)
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return date.toDateString();
    });

    const dailySpending = last30Days.map(dateStr => {
      const dayExpenses = expenses.filter(expense => 
        new Date(expense.date).toDateString() === dateStr
      );
      return dayExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    });

    // Calculate totals
    const thisMonthTotal = thisMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const lastMonthTotal = lastMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const todayTotal = todayExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const thisWeekTotal = thisWeekExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    // Calculate changes
    const monthlyChange = lastMonthTotal > 0 ? ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100 : 0;
    const avgDailySpending = thisMonthExpenses.length > 0 ? thisMonthTotal / new Date().getDate() : 0;

    return {
      thisMonthTotal,
      lastMonthTotal,
      todayTotal,
      thisWeekTotal,
      totalExpenses,
      monthlyChange,
      avgDailySpending,
      categoryTotals,
      dailySpending,
      last30Days,
      expenseCount: expenses.length,
      avgExpenseAmount: expenses.length > 0 ? totalExpenses / expenses.length : 0
    };
  }, [expenses]);

  // Prepare chart data
  const spendingTrendData = {
    labels: analytics.last30Days.map(date => {
      const d = new Date(date);
      return `${d.getMonth() + 1}/${d.getDate()}`;
    }),
    amounts: analytics.dailySpending
  };

  const categoryData = Object.entries(analytics.categoryTotals).map(([category, amount]) => ({
    category,
    amount,
    color: getCategoryColor(category)
  }));

  const monthlyComparisonData = [
    {
      month: 'Last Month',
      currentYear: analytics.lastMonthTotal,
      previousYear: 0 // Could calculate year-over-year if we had more data
    },
    {
      month: 'This Month',
      currentYear: analytics.thisMonthTotal,
      previousYear: 0
    }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="This Month"
          value={`$${analytics.thisMonthTotal.toFixed(2)}`}
          change={analytics.monthlyChange}
          icon={<DollarSign className="h-6 w-6" />}
          color="blue"
        />
        <MetricCard
          title="This Week"
          value={`$${analytics.thisWeekTotal.toFixed(2)}`}
          subtitle={`${Math.ceil(analytics.thisWeekTotal / 7)} avg/day`}
          icon={<Calendar className="h-6 w-6" />}
          color="green"
        />
        <MetricCard
          title="Today"
          value={`$${analytics.todayTotal.toFixed(2)}`}
          subtitle={`vs $${analytics.avgDailySpending.toFixed(2)} avg`}
          icon={<ShoppingCart className="h-6 w-6" />}
          color="purple"
        />
        <MetricCard
          title="Total Expenses"
          value={analytics.expenseCount.toString()}
          subtitle={`$${analytics.avgExpenseAmount.toFixed(2)} avg`}
          icon={<Target className="h-6 w-6" />}
          color="orange"
        />
      </div>

      {/* AI Insights */}
      <InsightsPanel expenses={expenses} />

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ThemedCard className="p-6" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
          <h3
            className="text-lg font-medium mb-4"
            style={{ color: theme.colors.text.primary }}
          >
            Spending Trend (30 Days)
          </h3>
          <SpendingTrendChart data={spendingTrendData} />
        </ThemedCard>

        <ThemedCard className="p-6" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
          <h3
            className="text-lg font-medium mb-4"
            style={{ color: theme.colors.text.primary }}
          >
            Category Breakdown
          </h3>
          {categoryData.length > 0 ? (
            <CategoryBreakdownChart data={categoryData} />
          ) : (
            <div
              className="h-64 flex items-center justify-center"
              style={{ color: theme.colors.text.secondary }}
            >
              No expense data available
            </div>
          )}
        </ThemedCard>
      </div>

      {/* Monthly Comparison */}
      <ThemedCard className="p-6" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
        <h3
          className="text-lg font-medium mb-4"
          style={{ color: theme.colors.text.primary }}
        >
          Monthly Comparison
        </h3>
        <MonthlyComparisonChart data={monthlyComparisonData} />
      </ThemedCard>

      {/* Top Categories */}
      <ThemedCard className="p-6" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
        <h3
          className="text-lg font-medium mb-4"
          style={{ color: theme.colors.text.primary }}
        >
          Top Spending Categories
        </h3>
        <div className="space-y-4">
          {Object.entries(analytics.categoryTotals)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([category, amount]) => {
              const percentage = (amount / analytics.totalExpenses) * 100;
              return (
                <div key={category} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: getCategoryColor(category) }}
                    />
                    <span
                      className="text-sm font-medium"
                      style={{ color: theme.colors.text.primary }}
                    >
                      {category}
                    </span>
                  </div>
                  <div className="text-right">
                    <div
                      className="text-sm font-medium"
                      style={{ color: theme.colors.text.primary }}
                    >
                      ${amount.toFixed(2)}
                    </div>
                    <div
                      className="text-xs"
                      style={{ color: theme.colors.text.secondary }}
                    >
                      {percentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      </ThemedCard>
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: string;
  change?: number;
  subtitle?: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'purple' | 'orange';
}

function MetricCard({ title, value, change, subtitle, icon, color }: MetricCardProps) {
  const { currentTheme, theme } = useTheme();

  const getColorClasses = () => {
    const baseColors = {
      blue: theme.colors.accent.primary,
      green: theme.colors.accent.success,
      purple: theme.colors.accent.secondary,
      orange: theme.colors.accent.warning
    };

    return {
      backgroundColor: `${baseColors[color]}20`,
      color: baseColors[color]
    };
  };

  return (
    <ThemedCard className="p-6" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p
            className="text-sm font-medium"
            style={{ color: theme.colors.text.secondary }}
          >
            {title}
          </p>
          <p
            className={`text-2xl font-semibold mt-1 ${
              currentTheme === 'cyber' ? 'text-cyber' :
              currentTheme === 'neon' ? 'text-neon' :
              currentTheme === 'matrix' ? 'text-matrix' : ''
            }`}
            style={{ color: theme.colors.text.primary }}
          >
            {value}
          </p>
          {change !== undefined && (
            <div className="flex items-center mt-2">
              {change >= 0 ? (
                <TrendingUp
                  className="h-4 w-4 mr-1"
                  style={{ color: theme.colors.accent.success }}
                />
              ) : (
                <TrendingDown
                  className="h-4 w-4 mr-1"
                  style={{ color: theme.colors.accent.error }}
                />
              )}
              <span
                className="text-sm"
                style={{ color: change >= 0 ? theme.colors.accent.success : theme.colors.accent.error }}
              >
                {Math.abs(change).toFixed(1)}%
              </span>
            </div>
          )}
          {subtitle && (
            <p
              className="text-xs mt-1"
              style={{ color: theme.colors.text.tertiary }}
            >
              {subtitle}
            </p>
          )}
        </div>
        <div
          className="p-3 rounded-lg"
          style={getColorClasses()}
        >
          {icon}
        </div>
      </div>
    </ThemedCard>
  );
}

function getCategoryColor(category: string): string {
  const colors: Record<string, string> = {
    'Food & Dining': '#EF4444',
    'Transportation': '#3B82F6',
    'Shopping': '#8B5CF6',
    'Entertainment': '#F59E0B',
    'Bills & Utilities': '#10B981',
    'Healthcare': '#EC4899',
    'Education': '#6366F1',
    'Travel': '#14B8A6',
    'Other': '#6B7280'
  };
  return colors[category] || '#6B7280';
}
