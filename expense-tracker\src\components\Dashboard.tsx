'use client';

import { useMemo } from 'react';
import { TrendingUp, TrendingDown, DollarSign, ShoppingCart, Calendar, Target } from 'lucide-react';
import { SpendingTrendChart, CategoryBreakdownChart, MonthlyComparisonChart } from './Charts';
import InsightsPanel from './InsightsPanel';

interface Expense {
  id: string;
  merchant: string;
  amount: number;
  date: string;
  category: string;
}

interface DashboardProps {
  expenses: Expense[];
}

export default function Dashboard({ expenses }: DashboardProps) {
  // Calculate analytics
  const analytics = useMemo(() => {
    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();
    const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
    const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;

    // This month expenses
    const thisMonthExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate.getMonth() === thisMonth && expenseDate.getFullYear() === thisYear;
    });

    // Last month expenses
    const lastMonthExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate.getMonth() === lastMonth && expenseDate.getFullYear() === lastMonthYear;
    });

    // Today's expenses
    const today = now.toDateString();
    const todayExpenses = expenses.filter(expense => 
      new Date(expense.date).toDateString() === today
    );

    // This week expenses
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay());
    const thisWeekExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate >= weekStart;
    });

    // Category breakdown
    const categoryTotals = expenses.reduce((acc, expense) => {
      acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
      return acc;
    }, {} as Record<string, number>);

    // Spending trend (last 30 days)
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return date.toDateString();
    });

    const dailySpending = last30Days.map(dateStr => {
      const dayExpenses = expenses.filter(expense => 
        new Date(expense.date).toDateString() === dateStr
      );
      return dayExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    });

    // Calculate totals
    const thisMonthTotal = thisMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const lastMonthTotal = lastMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const todayTotal = todayExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const thisWeekTotal = thisWeekExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    // Calculate changes
    const monthlyChange = lastMonthTotal > 0 ? ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100 : 0;
    const avgDailySpending = thisMonthExpenses.length > 0 ? thisMonthTotal / new Date().getDate() : 0;

    return {
      thisMonthTotal,
      lastMonthTotal,
      todayTotal,
      thisWeekTotal,
      totalExpenses,
      monthlyChange,
      avgDailySpending,
      categoryTotals,
      dailySpending,
      last30Days,
      expenseCount: expenses.length,
      avgExpenseAmount: expenses.length > 0 ? totalExpenses / expenses.length : 0
    };
  }, [expenses]);

  // Prepare chart data
  const spendingTrendData = {
    labels: analytics.last30Days.map(date => {
      const d = new Date(date);
      return `${d.getMonth() + 1}/${d.getDate()}`;
    }),
    amounts: analytics.dailySpending
  };

  const categoryData = Object.entries(analytics.categoryTotals).map(([category, amount]) => ({
    category,
    amount,
    color: getCategoryColor(category)
  }));

  const monthlyComparisonData = [
    {
      month: 'Last Month',
      currentYear: analytics.lastMonthTotal,
      previousYear: 0 // Could calculate year-over-year if we had more data
    },
    {
      month: 'This Month',
      currentYear: analytics.thisMonthTotal,
      previousYear: 0
    }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="This Month"
          value={`$${analytics.thisMonthTotal.toFixed(2)}`}
          change={analytics.monthlyChange}
          icon={<DollarSign className="h-6 w-6" />}
          color="blue"
        />
        <MetricCard
          title="This Week"
          value={`$${analytics.thisWeekTotal.toFixed(2)}`}
          subtitle={`${Math.ceil(analytics.thisWeekTotal / 7)} avg/day`}
          icon={<Calendar className="h-6 w-6" />}
          color="green"
        />
        <MetricCard
          title="Today"
          value={`$${analytics.todayTotal.toFixed(2)}`}
          subtitle={`vs $${analytics.avgDailySpending.toFixed(2)} avg`}
          icon={<ShoppingCart className="h-6 w-6" />}
          color="purple"
        />
        <MetricCard
          title="Total Expenses"
          value={analytics.expenseCount.toString()}
          subtitle={`$${analytics.avgExpenseAmount.toFixed(2)} avg`}
          icon={<Target className="h-6 w-6" />}
          color="orange"
        />
      </div>

      {/* AI Insights */}
      <InsightsPanel expenses={expenses} />

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Spending Trend (30 Days)</h3>
          <SpendingTrendChart data={spendingTrendData} />
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Category Breakdown</h3>
          {categoryData.length > 0 ? (
            <CategoryBreakdownChart data={categoryData} />
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              No expense data available
            </div>
          )}
        </div>
      </div>

      {/* Monthly Comparison */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Comparison</h3>
        <MonthlyComparisonChart data={monthlyComparisonData} />
      </div>

      {/* Top Categories */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Top Spending Categories</h3>
        <div className="space-y-4">
          {Object.entries(analytics.categoryTotals)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([category, amount]) => {
              const percentage = (amount / analytics.totalExpenses) * 100;
              return (
                <div key={category} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: getCategoryColor(category) }}
                    />
                    <span className="text-sm font-medium text-gray-900">{category}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      ${amount.toFixed(2)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {percentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: string;
  change?: number;
  subtitle?: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'purple' | 'orange';
}

function MetricCard({ title, value, change, subtitle, icon, color }: MetricCardProps) {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    purple: 'text-purple-600 bg-purple-100',
    orange: 'text-orange-600 bg-orange-100'
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-semibold text-gray-900 mt-1">{value}</p>
          {change !== undefined && (
            <div className="flex items-center mt-2">
              {change >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(change).toFixed(1)}%
              </span>
            </div>
          )}
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          {icon}
        </div>
      </div>
    </div>
  );
}

function getCategoryColor(category: string): string {
  const colors: Record<string, string> = {
    'Food & Dining': '#EF4444',
    'Transportation': '#3B82F6',
    'Shopping': '#8B5CF6',
    'Entertainment': '#F59E0B',
    'Bills & Utilities': '#10B981',
    'Healthcare': '#EC4899',
    'Education': '#6366F1',
    'Travel': '#14B8A6',
    'Other': '#6B7280'
  };
  return colors[category] || '#6B7280';
}
