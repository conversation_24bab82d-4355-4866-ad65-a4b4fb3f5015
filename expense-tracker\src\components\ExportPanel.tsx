'use client';

import { useState } from 'react';
import { Download, FileText, BarChart, Calendar, Loader2 } from 'lucide-react';
import { generatePDFReport, exportToCSV, generateReportHTML } from '@/lib/report-generator';

interface Expense {
  id: string;
  merchant: string;
  amount: number;
  date: string;
  category: string;
}

interface ExportPanelProps {
  expenses: Expense[];
}

export default function ExportPanel({ expenses }: ExportPanelProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [exportType, setExportType] = useState<string | null>(null);

  const generateMonthlyReport = async () => {
    if (expenses.length === 0) {
      alert('No expenses to export');
      return;
    }

    setIsGenerating(true);
    setExportType('pdf');

    try {
      // Prepare report data
      const now = new Date();
      const reportData = {
        month: now.toLocaleString('default', { month: 'long' }),
        year: now.getFullYear(),
        totalExpenses: expenses.reduce((sum, e) => sum + e.amount, 0),
        totalTransactions: expenses.length,
        categoryBreakdown: generateCategoryBreakdown(expenses),
        budgetAnalysis: [], // Would be populated with actual budget data
        topExpenses: expenses
          .sort((a, b) => b.amount - a.amount)
          .slice(0, 10)
          .map(e => ({
            merchant: e.merchant,
            amount: e.amount,
            date: e.date,
            category: e.category
          })),
        insights: generateQuickInsights(expenses),
        comparisonToPreviousMonth: {
          totalChange: 0,
          totalChangePercentage: 0,
          categoryChanges: []
        }
      };

      await generatePDFReport(reportData);
    } catch (error) {
      console.error('Error generating PDF report:', error);
      alert('Failed to generate PDF report');
    } finally {
      setIsGenerating(false);
      setExportType(null);
    }
  };

  const exportCSV = () => {
    if (expenses.length === 0) {
      alert('No expenses to export');
      return;
    }

    setIsGenerating(true);
    setExportType('csv');

    try {
      const reportData = {
        month: new Date().toLocaleString('default', { month: 'long' }),
        year: new Date().getFullYear(),
        totalExpenses: 0,
        totalTransactions: 0,
        categoryBreakdown: generateCategoryBreakdown(expenses),
        budgetAnalysis: [],
        topExpenses: [],
        insights: [],
        comparisonToPreviousMonth: {
          totalChange: 0,
          totalChangePercentage: 0,
          categoryChanges: []
        }
      };

      exportToCSV(reportData);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export CSV');
    } finally {
      setIsGenerating(false);
      setExportType(null);
    }
  };

  const previewReport = () => {
    if (expenses.length === 0) {
      alert('No expenses to preview');
      return;
    }

    const reportData = {
      month: new Date().toLocaleString('default', { month: 'long' }),
      year: new Date().getFullYear(),
      totalExpenses: expenses.reduce((sum, e) => sum + e.amount, 0),
      totalTransactions: expenses.length,
      categoryBreakdown: generateCategoryBreakdown(expenses),
      budgetAnalysis: [],
      topExpenses: expenses
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 10)
        .map(e => ({
          merchant: e.merchant,
          amount: e.amount,
          date: e.date,
          category: e.category
        })),
      insights: generateQuickInsights(expenses),
      comparisonToPreviousMonth: {
        totalChange: 0,
        totalChangePercentage: 0,
        categoryChanges: []
      }
    };

    const htmlContent = generateReportHTML(reportData);
    const newWindow = window.open('', '_blank');
    if (newWindow) {
      newWindow.document.write(htmlContent);
      newWindow.document.close();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-green-100 rounded-lg">
          <Download className="h-6 w-6 text-green-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Export & Reports</h2>
          <p className="text-sm text-gray-500">Generate detailed reports and export your data</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* PDF Report */}
        <div className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
          <div className="flex items-center space-x-3 mb-3">
            <FileText className="h-8 w-8 text-red-500" />
            <div>
              <h3 className="font-medium text-gray-900">PDF Report</h3>
              <p className="text-sm text-gray-500">Comprehensive monthly report</p>
            </div>
          </div>
          <button
            onClick={generateMonthlyReport}
            disabled={isGenerating && exportType === 'pdf'}
            className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            {isGenerating && exportType === 'pdf' ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Generating...</span>
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                <span>Download PDF</span>
              </>
            )}
          </button>
        </div>

        {/* CSV Export */}
        <div className="border border-gray-200 rounded-lg p-4 hover:border-green-300 transition-colors">
          <div className="flex items-center space-x-3 mb-3">
            <BarChart className="h-8 w-8 text-green-500" />
            <div>
              <h3 className="font-medium text-gray-900">CSV Export</h3>
              <p className="text-sm text-gray-500">Raw data for analysis</p>
            </div>
          </div>
          <button
            onClick={exportCSV}
            disabled={isGenerating && exportType === 'csv'}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            {isGenerating && exportType === 'csv' ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Exporting...</span>
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                <span>Export CSV</span>
              </>
            )}
          </button>
        </div>

        {/* Preview Report */}
        <div className="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors">
          <div className="flex items-center space-x-3 mb-3">
            <Calendar className="h-8 w-8 text-purple-500" />
            <div>
              <h3 className="font-medium text-gray-900">Preview Report</h3>
              <p className="text-sm text-gray-500">View before downloading</p>
            </div>
          </div>
          <button
            onClick={previewReport}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 flex items-center justify-center space-x-2"
          >
            <FileText className="h-4 w-4" />
            <span>Preview</span>
          </button>
        </div>
      </div>

      {/* Export Summary */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-2">Export Summary</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Total Expenses:</span>
            <span className="ml-2 font-medium">{expenses.length}</span>
          </div>
          <div>
            <span className="text-gray-500">Total Amount:</span>
            <span className="ml-2 font-medium">
              ${expenses.reduce((sum, e) => sum + e.amount, 0).toFixed(2)}
            </span>
          </div>
          <div>
            <span className="text-gray-500">Categories:</span>
            <span className="ml-2 font-medium">
              {new Set(expenses.map(e => e.category)).size}
            </span>
          </div>
          <div>
            <span className="text-gray-500">Date Range:</span>
            <span className="ml-2 font-medium">
              {expenses.length > 0 ? getDateRange(expenses) : 'N/A'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Helper functions
function generateCategoryBreakdown(expenses: Expense[]) {
  const categoryTotals = expenses.reduce((acc, expense) => {
    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
    return acc;
  }, {} as Record<string, number>);

  const total = expenses.reduce((sum, e) => sum + e.amount, 0);

  return Object.entries(categoryTotals).map(([category, amount]) => ({
    category,
    amount,
    percentage: total > 0 ? (amount / total) * 100 : 0,
    transactionCount: expenses.filter(e => e.category === category).length
  }));
}

function generateQuickInsights(expenses: Expense[]): string[] {
  const insights: string[] = [];
  
  if (expenses.length === 0) return insights;

  const total = expenses.reduce((sum, e) => sum + e.amount, 0);
  const average = total / expenses.length;
  
  insights.push(`Average expense amount: $${average.toFixed(2)}`);
  
  const categoryTotals = expenses.reduce((acc, expense) => {
    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
    return acc;
  }, {} as Record<string, number>);

  const topCategory = Object.entries(categoryTotals).reduce((a, b) => a[1] > b[1] ? a : b);
  insights.push(`Highest spending category: ${topCategory[0]} ($${topCategory[1].toFixed(2)})`);

  return insights;
}

function getDateRange(expenses: Expense[]): string {
  const dates = expenses.map(e => new Date(e.date)).sort((a, b) => a.getTime() - b.getTime());
  const start = dates[0];
  const end = dates[dates.length - 1];
  
  if (start.toDateString() === end.toDateString()) {
    return start.toLocaleDateString();
  }
  
  return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
}
