'use client';

import { useState } from 'react';
import { Download, FileText, BarChart, Calendar, Loader2 } from 'lucide-react';
import { generatePDFReport, exportToCSV, generateReportHTML } from '@/lib/report-generator';
import { ThemedCard, ThemedButton } from './ThemeComponents';
import { useTheme } from '@/contexts/ThemeContext';

interface Expense {
  id: string;
  merchant: string;
  amount: number;
  date: string;
  category: string;
}

interface ExportPanelProps {
  expenses: Expense[];
}

export default function ExportPanel({ expenses }: ExportPanelProps) {
  const { currentTheme, theme } = useTheme();
  const [isGenerating, setIsGenerating] = useState(false);
  const [exportType, setExportType] = useState<string | null>(null);

  const generateMonthlyReport = async () => {
    if (expenses.length === 0) {
      alert('No expenses to export');
      return;
    }

    setIsGenerating(true);
    setExportType('pdf');

    try {
      // Prepare report data
      const now = new Date();
      const reportData = {
        month: now.toLocaleString('default', { month: 'long' }),
        year: now.getFullYear(),
        totalExpenses: expenses.reduce((sum, e) => sum + e.amount, 0),
        totalTransactions: expenses.length,
        categoryBreakdown: generateCategoryBreakdown(expenses),
        budgetAnalysis: [], // Would be populated with actual budget data
        topExpenses: expenses
          .sort((a, b) => b.amount - a.amount)
          .slice(0, 10)
          .map(e => ({
            merchant: e.merchant,
            amount: e.amount,
            date: e.date,
            category: e.category
          })),
        insights: generateQuickInsights(expenses),
        comparisonToPreviousMonth: {
          totalChange: 0,
          totalChangePercentage: 0,
          categoryChanges: []
        }
      };

      await generatePDFReport(reportData);
    } catch (error) {
      console.error('Error generating PDF report:', error);
      alert('Failed to generate PDF report');
    } finally {
      setIsGenerating(false);
      setExportType(null);
    }
  };

  const exportCSV = () => {
    if (expenses.length === 0) {
      alert('No expenses to export');
      return;
    }

    setIsGenerating(true);
    setExportType('csv');

    try {
      const reportData = {
        month: new Date().toLocaleString('default', { month: 'long' }),
        year: new Date().getFullYear(),
        totalExpenses: 0,
        totalTransactions: 0,
        categoryBreakdown: generateCategoryBreakdown(expenses),
        budgetAnalysis: [],
        topExpenses: [],
        insights: [],
        comparisonToPreviousMonth: {
          totalChange: 0,
          totalChangePercentage: 0,
          categoryChanges: []
        }
      };

      exportToCSV(reportData);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export CSV');
    } finally {
      setIsGenerating(false);
      setExportType(null);
    }
  };

  const previewReport = () => {
    if (expenses.length === 0) {
      alert('No expenses to preview');
      return;
    }

    const reportData = {
      month: new Date().toLocaleString('default', { month: 'long' }),
      year: new Date().getFullYear(),
      totalExpenses: expenses.reduce((sum, e) => sum + e.amount, 0),
      totalTransactions: expenses.length,
      categoryBreakdown: generateCategoryBreakdown(expenses),
      budgetAnalysis: [],
      topExpenses: expenses
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 10)
        .map(e => ({
          merchant: e.merchant,
          amount: e.amount,
          date: e.date,
          category: e.category
        })),
      insights: generateQuickInsights(expenses),
      comparisonToPreviousMonth: {
        totalChange: 0,
        totalChangePercentage: 0,
        categoryChanges: []
      }
    };

    const htmlContent = generateReportHTML(reportData);
    const newWindow = window.open('', '_blank');
    if (newWindow) {
      newWindow.document.write(htmlContent);
      newWindow.document.close();
    }
  };

  return (
    <ThemedCard className="p-6" hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
      <div className="flex items-center space-x-3 mb-6">
        <div
          className="p-2 rounded-lg"
          style={{
            backgroundColor: `${theme.colors.accent.success}20`,
            color: theme.colors.accent.success
          }}
        >
          <Download className="h-6 w-6" />
        </div>
        <div>
          <h2
            className={`text-xl font-semibold ${
              currentTheme === 'cyber' ? 'text-cyber' :
              currentTheme === 'neon' ? 'text-neon' :
              currentTheme === 'matrix' ? 'text-matrix' : ''
            }`}
            style={{ color: theme.colors.text.primary }}
          >
            Export & Reports
          </h2>
          <p
            className="text-sm"
            style={{ color: theme.colors.text.secondary }}
          >
            Generate detailed reports and export your data
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* PDF Report */}
        <ThemedCard
          className="p-4 border-2 transition-colors duration-300"
          style={{ borderColor: theme.colors.border.secondary }}
          hover3D={currentTheme === 'cyber'}
        >
          <div className="flex items-center space-x-3 mb-3">
            <FileText
              className="h-8 w-8"
              style={{ color: theme.colors.accent.error }}
            />
            <div>
              <h3
                className="font-medium"
                style={{ color: theme.colors.text.primary }}
              >
                PDF Report
              </h3>
              <p
                className="text-sm"
                style={{ color: theme.colors.text.secondary }}
              >
                Comprehensive monthly report
              </p>
            </div>
          </div>
          <ThemedButton
            onClick={generateMonthlyReport}
            disabled={isGenerating && exportType === 'pdf'}
            variant="error"
            cyber3D={currentTheme === 'cyber'}
            className="w-full flex items-center justify-center space-x-2"
          >
            {isGenerating && exportType === 'pdf' ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Generating...</span>
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                <span>Download PDF</span>
              </>
            )}
          </ThemedButton>
        </ThemedCard>

        {/* CSV Export */}
        <ThemedCard
          className="p-4 border-2 transition-colors duration-300"
          style={{ borderColor: theme.colors.border.secondary }}
          hover3D={currentTheme === 'cyber'}
        >
          <div className="flex items-center space-x-3 mb-3">
            <BarChart
              className="h-8 w-8"
              style={{ color: theme.colors.accent.success }}
            />
            <div>
              <h3
                className="font-medium"
                style={{ color: theme.colors.text.primary }}
              >
                CSV Export
              </h3>
              <p
                className="text-sm"
                style={{ color: theme.colors.text.secondary }}
              >
                Raw data for analysis
              </p>
            </div>
          </div>
          <ThemedButton
            onClick={exportCSV}
            disabled={isGenerating && exportType === 'csv'}
            variant="success"
            cyber3D={currentTheme === 'cyber'}
            className="w-full flex items-center justify-center space-x-2"
          >
            {isGenerating && exportType === 'csv' ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Exporting...</span>
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                <span>Export CSV</span>
              </>
            )}
          </ThemedButton>
        </ThemedCard>

        {/* Preview Report */}
        <ThemedCard
          className="p-4 border-2 transition-colors duration-300"
          style={{ borderColor: theme.colors.border.secondary }}
          hover3D={currentTheme === 'cyber'}
        >
          <div className="flex items-center space-x-3 mb-3">
            <Calendar
              className="h-8 w-8"
              style={{ color: theme.colors.accent.secondary }}
            />
            <div>
              <h3
                className="font-medium"
                style={{ color: theme.colors.text.primary }}
              >
                Preview Report
              </h3>
              <p
                className="text-sm"
                style={{ color: theme.colors.text.secondary }}
              >
                View before downloading
              </p>
            </div>
          </div>
          <ThemedButton
            onClick={previewReport}
            variant="secondary"
            cyber3D={currentTheme === 'cyber'}
            className="w-full flex items-center justify-center space-x-2"
          >
            <FileText className="h-4 w-4" />
            <span>Preview</span>
          </ThemedButton>
        </ThemedCard>
      </div>

      {/* Export Summary */}
      <div
        className="mt-6 p-4 rounded-lg"
        style={{ backgroundColor: theme.colors.bg.tertiary }}
      >
        <h4
          className="font-medium mb-2"
          style={{ color: theme.colors.text.primary }}
        >
          Export Summary
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span style={{ color: theme.colors.text.secondary }}>Total Expenses:</span>
            <span
              className="ml-2 font-medium"
              style={{ color: theme.colors.text.primary }}
            >
              {expenses.length}
            </span>
          </div>
          <div>
            <span style={{ color: theme.colors.text.secondary }}>Total Amount:</span>
            <span
              className="ml-2 font-medium"
              style={{ color: theme.colors.text.primary }}
            >
              ${expenses.reduce((sum, e) => sum + e.amount, 0).toFixed(2)}
            </span>
          </div>
          <div>
            <span style={{ color: theme.colors.text.secondary }}>Categories:</span>
            <span
              className="ml-2 font-medium"
              style={{ color: theme.colors.text.primary }}
            >
              {new Set(expenses.map(e => e.category)).size}
            </span>
          </div>
          <div>
            <span style={{ color: theme.colors.text.secondary }}>Date Range:</span>
            <span
              className="ml-2 font-medium"
              style={{ color: theme.colors.text.primary }}
            >
              {expenses.length > 0 ? getDateRange(expenses) : 'N/A'}
            </span>
          </div>
        </div>
      </div>
    </ThemedCard>
  );
}

// Helper functions
function generateCategoryBreakdown(expenses: Expense[]) {
  const categoryTotals = expenses.reduce((acc, expense) => {
    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
    return acc;
  }, {} as Record<string, number>);

  const total = expenses.reduce((sum, e) => sum + e.amount, 0);

  return Object.entries(categoryTotals).map(([category, amount]) => ({
    category,
    amount,
    percentage: total > 0 ? (amount / total) * 100 : 0,
    transactionCount: expenses.filter(e => e.category === category).length
  }));
}

function generateQuickInsights(expenses: Expense[]): string[] {
  const insights: string[] = [];
  
  if (expenses.length === 0) return insights;

  const total = expenses.reduce((sum, e) => sum + e.amount, 0);
  const average = total / expenses.length;
  
  insights.push(`Average expense amount: $${average.toFixed(2)}`);
  
  const categoryTotals = expenses.reduce((acc, expense) => {
    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
    return acc;
  }, {} as Record<string, number>);

  const topCategory = Object.entries(categoryTotals).reduce((a, b) => a[1] > b[1] ? a : b);
  insights.push(`Highest spending category: ${topCategory[0]} ($${topCategory[1].toFixed(2)})`);

  return insights;
}

function getDateRange(expenses: Expense[]): string {
  const dates = expenses.map(e => new Date(e.date)).sort((a, b) => a.getTime() - b.getTime());
  const start = dates[0];
  const end = dates[dates.length - 1];
  
  if (start.toDateString() === end.toDateString()) {
    return start.toLocaleDateString();
  }
  
  return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
}
