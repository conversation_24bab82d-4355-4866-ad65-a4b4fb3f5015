'use client';

import { useMemo } from 'react';
import { Brain, TrendingUp, AlertTriangle, CheckCircle, Info, Lightbulb, Zap } from 'lucide-react';
import { generateSpendingInsights, type SpendingInsight } from '@/lib/ai-insights';
import { ThemedCard } from './ThemeComponents';
import { useTheme } from '@/contexts/ThemeContext';

interface Expense {
  id: string;
  merchant: string;
  amount: number;
  date: string;
  category: string;
}

interface InsightsPanelProps {
  expenses: Expense[];
}

export default function InsightsPanel({ expenses }: InsightsPanelProps) {
  const { currentTheme, theme } = useTheme();

  const insights = useMemo(() => {
    return generateSpendingInsights(expenses);
  }, [expenses]);

  const getInsightIcon = (type: SpendingInsight['type']) => {
    switch (type) {
      case 'alert':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <Lightbulb className="h-5 w-5 text-gray-500" />;
    }
  };

  const getInsightBorderColor = (type: SpendingInsight['type']) => {
    switch (type) {
      case 'alert':
        return 'border-l-red-500 bg-red-50';
      case 'warning':
        return 'border-l-yellow-500 bg-yellow-50';
      case 'success':
        return 'border-l-green-500 bg-green-50';
      case 'info':
        return 'border-l-blue-500 bg-blue-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const getPriorityBadge = (priority: SpendingInsight['priority']) => {
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-blue-100 text-blue-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[priority]}`}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };

  const highPriorityInsights = insights.filter(insight => insight.priority === 'high');
  const otherInsights = insights.filter(insight => insight.priority !== 'high');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div
          className="p-2 rounded-lg"
          style={{
            backgroundColor: `${theme.colors.accent.secondary}20`,
            color: theme.colors.accent.secondary
          }}
        >
          <Brain className="h-6 w-6" />
        </div>
        <div>
          <h2
            className={`text-xl font-semibold ${
              currentTheme === 'cyber' ? 'text-cyber' :
              currentTheme === 'neon' ? 'text-neon' :
              currentTheme === 'matrix' ? 'text-matrix' : ''
            }`}
            style={{ color: theme.colors.text.primary }}
          >
            AI Insights
          </h2>
          <p
            className="text-sm"
            style={{ color: theme.colors.text.secondary }}
          >
            Personalized spending analysis and recommendations
          </p>
        </div>
      </div>

      {/* High Priority Alerts */}
      {highPriorityInsights.length > 0 && (
        <ThemedCard
          className="border-2"
          style={{ borderColor: theme.colors.accent.error }}
          hover3D={currentTheme === 'cyber'}
        >
          <div
            className="px-4 py-3 border-b"
            style={{
              borderColor: theme.colors.accent.error,
              backgroundColor: `${theme.colors.accent.error}20`
            }}
          >
            <div className="flex items-center space-x-2">
              <Zap
                className="h-4 w-4"
                style={{ color: theme.colors.accent.error }}
              />
              <h3
                className="text-sm font-medium"
                style={{ color: theme.colors.accent.error }}
              >
                Urgent Attention Required
              </h3>
            </div>
          </div>
          <div className="p-4 space-y-3">
            {highPriorityInsights.map((insight, index) => (
              <InsightCard key={index} insight={insight} />
            ))}
          </div>
        </ThemedCard>
      )}

      {/* All Insights */}
      <ThemedCard hover3D={currentTheme === 'cyber'} glow={currentTheme !== 'light'}>
        <div
          className="px-6 py-4 border-b"
          style={{ borderColor: theme.colors.border.primary }}
        >
          <div className="flex items-center justify-between">
            <h3
              className="text-lg font-medium"
              style={{ color: theme.colors.text.primary }}
            >
              Smart Recommendations
            </h3>
            <span
              className="text-sm"
              style={{ color: theme.colors.text.secondary }}
            >
              {insights.length} insights
            </span>
          </div>
        </div>
        <div
          className="divide-y"
          style={{ borderColor: theme.colors.border.primary }}
        >
          {insights.length === 0 ? (
            <div className="p-8 text-center">
              <Brain
                className="h-12 w-12 mx-auto mb-4"
                style={{ color: theme.colors.text.tertiary }}
              />
              <p style={{ color: theme.colors.text.secondary }}>
                Add more expenses to get AI-powered insights
              </p>
            </div>
          ) : (
            insights.map((insight, index) => (
              <div key={index} className="p-6">
                <InsightCard insight={insight} />
              </div>
            ))
          )}
        </div>
      </ThemedCard>

      {/* Insights Summary */}
      {insights.length > 0 && (
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="h-5 w-5 text-purple-600" />
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Insights Summary</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">High Priority:</span>
                  <span className="ml-2 font-medium text-red-600">
                    {insights.filter(i => i.priority === 'high').length}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Opportunities:</span>
                  <span className="ml-2 font-medium text-green-600">
                    {insights.filter(i => i.type === 'success').length}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Recommendations:</span>
                  <span className="ml-2 font-medium text-blue-600">
                    {insights.filter(i => i.action).length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface InsightCardProps {
  insight: SpendingInsight;
}

function InsightCard({ insight }: InsightCardProps) {
  const getInsightIcon = (type: SpendingInsight['type']) => {
    switch (type) {
      case 'alert':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <Lightbulb className="h-5 w-5 text-gray-500" />;
    }
  };

  const getInsightBorderColor = (type: SpendingInsight['type']) => {
    switch (type) {
      case 'alert':
        return 'border-l-red-500 bg-red-50';
      case 'warning':
        return 'border-l-yellow-500 bg-yellow-50';
      case 'success':
        return 'border-l-green-500 bg-green-50';
      case 'info':
        return 'border-l-blue-500 bg-blue-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const getPriorityBadge = (priority: SpendingInsight['priority']) => {
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-blue-100 text-blue-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[priority]}`}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };

  return (
    <div className={`border-l-4 p-4 rounded-r-lg ${getInsightBorderColor(insight.type)}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-0.5">
          {getInsightIcon(insight.type)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-sm font-medium text-gray-900">{insight.title}</h4>
            {getPriorityBadge(insight.priority)}
          </div>
          <p className="text-sm text-gray-700 mb-2">{insight.description}</p>
          
          {insight.action && (
            <div className="mt-3 p-3 bg-white rounded-md border border-gray-200">
              <div className="flex items-start space-x-2">
                <Lightbulb className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-gray-600">{insight.action}</p>
              </div>
            </div>
          )}
          
          {(insight.category || insight.amount) && (
            <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
              {insight.category && (
                <span className="inline-flex items-center">
                  Category: <span className="ml-1 font-medium">{insight.category}</span>
                </span>
              )}
              {insight.amount && (
                <span className="inline-flex items-center">
                  Amount: <span className="ml-1 font-medium">${insight.amount.toFixed(2)}</span>
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
