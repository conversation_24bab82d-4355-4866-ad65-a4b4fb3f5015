'use client';

import React, { useState } from 'react';
import { Globe, Check, Loader2 } from 'lucide-react';
import { useLanguage, SUPPORTED_LANGUAGES, LanguageCode } from '@/contexts/LanguageContext';
import { ThemedCard, ThemedButton } from './ThemeComponents';
import { useTheme } from '@/contexts/ThemeContext';

interface LanguageSwitcherProps {
  showModal?: boolean;
  onClose?: () => void;
}

export default function LanguageSwitcher({ showModal = false, onClose }: LanguageSwitcherProps) {
  const { currentLanguage, setLanguage, isTranslating } = useLanguage();
  const { currentTheme, theme } = useTheme();
  const [isOpen, setIsOpen] = useState(showModal);

  const handleLanguageChange = (langCode: LanguageCode) => {
    setLanguage(langCode);
    setIsOpen(false);
    onClose?.();
  };

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  if (showModal && isOpen) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
        <ThemedCard 
          className="max-w-2xl w-full max-h-[80vh] overflow-y-auto"
          hover3D={currentTheme === 'cyber'}
          glow={currentTheme !== 'light'}
        >
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div 
                  className="p-2 rounded-lg"
                  style={{ 
                    backgroundColor: `${theme.colors.accent.primary}20`,
                    color: theme.colors.accent.primary 
                  }}
                >
                  <Globe className="h-6 w-6" />
                </div>
                <div>
                  <h2 
                    className={`text-xl font-semibold ${
                      currentTheme === 'cyber' ? 'text-cyber' :
                      currentTheme === 'neon' ? 'text-neon' :
                      currentTheme === 'matrix' ? 'text-matrix' : ''
                    }`}
                    style={{ color: theme.colors.text.primary }}
                  >
                    Choose Language
                  </h2>
                  <p 
                    className="text-sm"
                    style={{ color: theme.colors.text.secondary }}
                  >
                    Select your preferred language for the interface
                  </p>
                </div>
              </div>
              {isTranslating && (
                <div className="flex items-center space-x-2">
                  <Loader2 
                    className="h-4 w-4 animate-spin" 
                    style={{ color: theme.colors.accent.primary }}
                  />
                  <span 
                    className="text-sm"
                    style={{ color: theme.colors.text.secondary }}
                  >
                    Translating...
                  </span>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {Object.values(SUPPORTED_LANGUAGES).map((lang) => (
                <ThemedButton
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  variant={currentLanguage === lang.code ? 'primary' : 'secondary'}
                  cyber3D={currentTheme === 'cyber'}
                  className={`p-4 flex items-center justify-between text-left transition-all duration-300 ${
                    currentLanguage === lang.code 
                      ? 'ring-2 ring-offset-2' 
                      : 'hover:scale-105'
                  }`}
                  style={{
                    ringColor: currentLanguage === lang.code ? theme.colors.accent.primary : 'transparent',
                    ringOffsetColor: theme.colors.bg.card
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{lang.flag}</span>
                    <div>
                      <div 
                        className="font-medium"
                        style={{ color: theme.colors.text.primary }}
                      >
                        {lang.nativeName}
                      </div>
                      <div 
                        className="text-sm"
                        style={{ color: theme.colors.text.secondary }}
                      >
                        {lang.name}
                      </div>
                    </div>
                  </div>
                  {currentLanguage === lang.code && (
                    <Check 
                      className="h-5 w-5" 
                      style={{ color: theme.colors.accent.success }}
                    />
                  )}
                </ThemedButton>
              ))}
            </div>

            <div className="mt-6 flex justify-end">
              <ThemedButton
                onClick={() => {
                  setIsOpen(false);
                  onClose?.();
                }}
                variant="secondary"
                cyber3D={currentTheme === 'cyber'}
              >
                Close
              </ThemedButton>
            </div>
          </div>
        </ThemedCard>
      </div>
    );
  }

  // Compact dropdown version
  return (
    <div className="relative">
      <ThemedButton
        onClick={toggleOpen}
        variant="secondary"
        size="sm"
        cyber3D={currentTheme === 'cyber'}
        className="flex items-center space-x-2"
      >
        <Globe className="h-4 w-4" />
        <span className="text-lg">{SUPPORTED_LANGUAGES[currentLanguage].flag}</span>
        <span className="hidden sm:inline">{SUPPORTED_LANGUAGES[currentLanguage].nativeName}</span>
      </ThemedButton>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 z-50">
          <ThemedCard 
            className="w-80 max-h-96 overflow-y-auto"
            hover3D={currentTheme === 'cyber'}
            glow={currentTheme !== 'light'}
          >
            <div className="p-4">
              <div className="grid grid-cols-1 gap-2">
                {Object.values(SUPPORTED_LANGUAGES).map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => handleLanguageChange(lang.code)}
                    className={`p-3 rounded-lg flex items-center justify-between transition-all duration-200 ${
                      currentLanguage === lang.code 
                        ? 'ring-2' 
                        : 'hover:scale-105'
                    }`}
                    style={{
                      backgroundColor: currentLanguage === lang.code 
                        ? `${theme.colors.accent.primary}20` 
                        : 'transparent',
                      ringColor: currentLanguage === lang.code ? theme.colors.accent.primary : 'transparent'
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">{lang.flag}</span>
                      <div className="text-left">
                        <div 
                          className="font-medium text-sm"
                          style={{ color: theme.colors.text.primary }}
                        >
                          {lang.nativeName}
                        </div>
                        <div 
                          className="text-xs"
                          style={{ color: theme.colors.text.secondary }}
                        >
                          {lang.name}
                        </div>
                      </div>
                    </div>
                    {currentLanguage === lang.code && (
                      <Check 
                        className="h-4 w-4" 
                        style={{ color: theme.colors.accent.success }}
                      />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </ThemedCard>
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
