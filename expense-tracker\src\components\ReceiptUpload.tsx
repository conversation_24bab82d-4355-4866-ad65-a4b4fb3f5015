'use client';

import { useState, useRef } from 'react';
import { Upload, X, Loader2, <PERSON><PERSON><PERSON><PERSON>, AlertCircle } from 'lucide-react';

// Simple OCR simulation for demo purposes
const simulateOCR = async (file: File): Promise<string> => {
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Return simulated receipt text based on filename or random
  const sampleReceipts = [
    `WALMART SUPERCENTER
123 MAIN ST
ANYTOWN, ST 12345

Date: ${new Date().toLocaleDateString()}
Time: 14:30

GROCERIES
MILK                 $3.99
BREAD                $2.49
EGGS                 $4.29

SUBTOTAL            $10.77
TAX                  $0.86
TOTAL               $11.63

THANK YOU FOR SHOPPING`,

    `STARBUCKS COFFEE

${new Date().toLocaleDateString()} 10:45 AM

LATTE GRANDE         $5.25
CROISSANT            $3.50

TOTAL                $8.75

THANK YOU`,

    `SHELL STATION #12345

FUEL PURCHASE
GRADE: REGULAR
GALLONS: 12.456
PRICE/GAL: $3.299

FUEL TOTAL          $41.08

DATE: ${new Date().toLocaleDateString()}
TIME: 08:15`
  ];

  return sampleReceipts[Math.floor(Math.random() * sampleReceipts.length)];
};

// Simple receipt parser
const parseReceiptText = (text: string) => {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  // Extract merchant (usually first meaningful line)
  let merchant = 'Unknown Merchant';
  for (const line of lines.slice(0, 3)) {
    if (line.length > 3 && !line.match(/^\d+/) && !line.includes('$')) {
      merchant = line;
      break;
    }
  }

  // Extract amount (look for TOTAL)
  let amount = 0;
  for (const line of lines) {
    if (line.toUpperCase().includes('TOTAL')) {
      const match = line.match(/\$?(\d+\.?\d*)/);
      if (match) {
        amount = parseFloat(match[1]);
        break;
      }
    }
  }

  // If no total found, look for any dollar amount
  if (amount === 0) {
    for (const line of lines.reverse()) {
      const match = line.match(/\$(\d+\.\d{2})/);
      if (match) {
        amount = parseFloat(match[1]);
        break;
      }
    }
  }

  // Extract date
  let date = new Date();
  for (const line of lines) {
    const dateMatch = line.match(/(\d{1,2}\/\d{1,2}\/\d{4})/);
    if (dateMatch) {
      date = new Date(dateMatch[1]);
      break;
    }
  }

  // Suggest category based on merchant
  let suggestedCategory = 'Other';
  const merchantLower = merchant.toLowerCase();
  if (merchantLower.includes('walmart') || merchantLower.includes('target') || merchantLower.includes('store')) {
    suggestedCategory = 'Shopping';
  } else if (merchantLower.includes('starbucks') || merchantLower.includes('restaurant') || merchantLower.includes('cafe')) {
    suggestedCategory = 'Food & Dining';
  } else if (merchantLower.includes('shell') || merchantLower.includes('gas') || merchantLower.includes('fuel')) {
    suggestedCategory = 'Transportation';
  }

  return {
    merchant,
    amount,
    date,
    suggestedCategory,
    rawText: text
  };
};

interface ReceiptUploadProps {
  onDataExtracted: (data: any) => void;
  onClose: () => void;
}

export default function ReceiptUpload({ onDataExtracted, onClose }: ReceiptUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [extractedData, setExtractedData] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateImageFile = (file: File) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Please upload a valid image file (JPEG, PNG, or WebP)' };
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'Image file size must be less than 10MB' };
    }

    return { valid: true };
  };

  const handleFileSelect = (selectedFile: File) => {
    const validation = validateImageFile(selectedFile);
    if (!validation.valid) {
      setError(validation.error || 'Invalid file');
      return;
    }

    setFile(selectedFile);
    setError(null);
    setExtractedData(null);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(selectedFile);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      handleFileSelect(selectedFile);
    }
  };

  const processReceipt = async () => {
    if (!file) return;

    setIsProcessing(true);
    setError(null);

    try {
      // Simulate OCR processing
      const ocrText = await simulateOCR(file);

      // Parse the extracted text
      const parsedData = parseReceiptText(ocrText);

      setExtractedData(parsedData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process receipt');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleConfirm = () => {
    if (extractedData) {
      onDataExtracted(extractedData);
      onClose();
    }
  };

  const reset = () => {
    setFile(null);
    setPreview(null);
    setExtractedData(null);
    setError(null);
    setProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Upload Receipt</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {!file && (
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors cursor-pointer"
              onDrop={handleDrop}
              onDragOver={(e) => e.preventDefault()}
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                Drop your receipt here or click to browse
              </p>
              <p className="text-sm text-gray-500">
                Supports JPEG, PNG, and WebP files up to 10MB
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileInput}
                className="hidden"
              />
            </div>
          )}

          {file && preview && (
            <div className="space-y-4">
              <div className="relative">
                <img
                  src={preview}
                  alt="Receipt preview"
                  className="w-full max-h-64 object-contain rounded-lg border"
                />
                <button
                  onClick={reset}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>

              {!isProcessing && !extractedData && (
                <button
                  onClick={processReceipt}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Process Receipt
                </button>
              )}

              {isProcessing && (
                <div className="flex items-center justify-center space-x-2">
                  <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                  <span className="text-sm text-gray-600">Processing receipt...</span>
                </div>
              )}

              {error && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
                  <AlertCircle className="h-5 w-5" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {extractedData && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg">
                    <CheckCircle className="h-5 w-5" />
                    <span className="text-sm">Receipt processed successfully!</span>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                    <h3 className="font-medium text-gray-900">Extracted Information</h3>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Merchant:</span>
                        <p className="font-medium">{extractedData.merchant}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Amount:</span>
                        <p className="font-medium">${extractedData.amount.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Date:</span>
                        <p className="font-medium">
                          {extractedData.date ? extractedData.date.toLocaleDateString() : 'Not found'}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Category:</span>
                        <p className="font-medium">{extractedData.suggestedCategory}</p>
                      </div>
                    </div>

                    <div className="text-xs text-gray-500">
                      <p>Data extracted from receipt image using OCR simulation</p>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={handleConfirm}
                      className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                    >
                      Use This Data
                    </button>
                    <button
                      onClick={reset}
                      className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
