'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ap, <PERSON><PERSON>les, Code, X, Check } from 'lucide-react';
import { useTheme, themes, type ThemeType } from '@/contexts/ThemeContext';
import { ThemedCard, ThemedButton } from './ThemeComponents';

interface ThemeSwitcherProps {
  showModal?: boolean;
  onClose?: () => void;
}

export default function ThemeSwitcher({ showModal = false, onClose }: ThemeSwitcherProps) {
  const { currentTheme, setTheme } = useTheme();
  const [previewTheme, setPreviewTheme] = useState<ThemeType | null>(null);

  const themeIcons = {
    light: <Monitor className="h-5 w-5" />,
    dark: <Moon className="h-5 w-5" />,
    cyber: <Zap className="h-5 w-5" />,
    neon: <Sparkles className="h-5 w-5" />,
    matrix: <Code className="h-5 w-5" />,
  };

  const handleThemeSelect = (theme: ThemeType) => {
    setTheme(theme);
    if (onClose) onClose();
  };

  const handlePreview = (theme: ThemeType) => {
    setPreviewTheme(theme);
    // Temporarily apply theme for preview
    const root = document.documentElement;
    const themeColors = themes[theme].colors;
    
    root.style.setProperty('--bg-primary', themeColors.bg.primary);
    root.style.setProperty('--bg-secondary', themeColors.bg.secondary);
    root.style.setProperty('--text-primary', themeColors.text.primary);
    root.style.setProperty('--accent-primary', themeColors.accent.primary);
  };

  const stopPreview = () => {
    setPreviewTheme(null);
    // Restore current theme
    const root = document.documentElement;
    const themeColors = themes[currentTheme].colors;
    
    root.style.setProperty('--bg-primary', themeColors.bg.primary);
    root.style.setProperty('--bg-secondary', themeColors.bg.secondary);
    root.style.setProperty('--text-primary', themeColors.text.primary);
    root.style.setProperty('--accent-primary', themeColors.accent.primary);
  };

  if (!showModal) {
    // Compact theme switcher button
    return (
      <div className="relative">
        <ThemedButton
          onClick={() => {}}
          variant="secondary"
          size="sm"
          className="flex items-center space-x-2"
          cyber3D={currentTheme === 'cyber'}
        >
          <Palette className="h-4 w-4" />
          <span className="hidden sm:inline">{themes[currentTheme].name}</span>
        </ThemedButton>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <ThemedCard className="max-w-4xl w-full max-h-[90vh] overflow-y-auto" hover3D={currentTheme === 'cyber'}>
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${
                currentTheme === 'cyber' ? 'bg-green-400/20 text-green-400' :
                currentTheme === 'neon' ? 'bg-pink-500/20 text-pink-500' :
                currentTheme === 'matrix' ? 'bg-green-500/20 text-green-500' :
                currentTheme === 'dark' ? 'bg-blue-500/20 text-blue-400' :
                'bg-blue-100 text-blue-600'
              }`}>
                <Palette className="h-6 w-6" />
              </div>
              <div>
                <h2 className={`text-xl font-semibold ${
                  currentTheme === 'cyber' ? 'text-green-400' :
                  currentTheme === 'neon' ? 'text-pink-500' :
                  currentTheme === 'matrix' ? 'text-green-500' :
                  currentTheme === 'dark' ? 'text-gray-100' :
                  'text-gray-900'
                }`}>
                  Choose Your Theme
                </h2>
                <p className={`text-sm ${
                  currentTheme === 'cyber' ? 'text-green-400/70' :
                  currentTheme === 'neon' ? 'text-pink-500/70' :
                  currentTheme === 'matrix' ? 'text-green-500/70' :
                  currentTheme === 'dark' ? 'text-gray-400' :
                  'text-gray-500'
                }`}>
                  Select a theme to customize your experience
                </p>
              </div>
            </div>
            <ThemedButton
              onClick={onClose}
              variant="secondary"
              size="sm"
              className="!p-2"
            >
              <X className="h-4 w-4" />
            </ThemedButton>
          </div>

          {/* Theme Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(themes).map(([themeId, theme]) => (
              <ThemePreviewCard
                key={themeId}
                theme={theme}
                isActive={currentTheme === themeId}
                isPreview={previewTheme === themeId}
                onSelect={() => handleThemeSelect(themeId as ThemeType)}
                onPreview={() => handlePreview(themeId as ThemeType)}
                onStopPreview={stopPreview}
                icon={themeIcons[themeId as ThemeType]}
              />
            ))}
          </div>

          {/* Preview Notice */}
          {previewTheme && (
            <div className={`mt-6 p-4 rounded-lg border-2 ${
              currentTheme === 'cyber' ? 'bg-green-400/10 border-green-400/50 text-green-400' :
              currentTheme === 'neon' ? 'bg-pink-500/10 border-pink-500/50 text-pink-500' :
              currentTheme === 'matrix' ? 'bg-green-500/10 border-green-500/50 text-green-500' :
              currentTheme === 'dark' ? 'bg-blue-500/10 border-blue-500/50 text-blue-400' :
              'bg-blue-50 border-blue-200 text-blue-700'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Sparkles className="h-5 w-5" />
                  <span className="font-medium">
                    Previewing {themes[previewTheme].name}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <ThemedButton
                    onClick={() => handleThemeSelect(previewTheme)}
                    variant="success"
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <Check className="h-4 w-4" />
                    <span>Apply</span>
                  </ThemedButton>
                  <ThemedButton
                    onClick={stopPreview}
                    variant="secondary"
                    size="sm"
                  >
                    Cancel
                  </ThemedButton>
                </div>
              </div>
            </div>
          )}
        </div>
      </ThemedCard>
    </div>
  );
}

interface ThemePreviewCardProps {
  theme: any;
  isActive: boolean;
  isPreview: boolean;
  onSelect: () => void;
  onPreview: () => void;
  onStopPreview: () => void;
  icon: React.ReactNode;
}

function ThemePreviewCard({ 
  theme, 
  isActive, 
  isPreview, 
  onSelect, 
  onPreview, 
  onStopPreview, 
  icon 
}: ThemePreviewCardProps) {
  const { currentTheme } = useTheme();
  
  const getPreviewStyles = () => {
    const styles = {
      backgroundColor: theme.colors.bg.primary,
      borderColor: theme.colors.border.primary,
      color: theme.colors.text.primary,
    };
    
    if (theme.id === 'cyber') {
      return {
        ...styles,
        boxShadow: '0 0 20px rgba(0, 255, 136, 0.3)',
        border: '2px solid rgba(0, 255, 136, 0.5)',
      };
    } else if (theme.id === 'neon') {
      return {
        ...styles,
        boxShadow: '0 0 25px rgba(255, 0, 110, 0.4)',
        border: '2px solid rgba(255, 0, 110, 0.5)',
      };
    } else if (theme.id === 'matrix') {
      return {
        ...styles,
        boxShadow: '0 0 20px rgba(0, 255, 0, 0.4)',
        border: '2px solid rgba(0, 255, 0, 0.5)',
      };
    }
    
    return styles;
  };

  return (
    <div
      className={`
        relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300
        transform hover:scale-105 hover:rotate-1
        ${isActive ? 'ring-4 ring-blue-500/50' : ''}
        ${isPreview ? 'ring-4 ring-yellow-500/50' : ''}
        ${theme.id === 'cyber' ? 'hover:shadow-[0_0_30px_rgba(0,255,136,0.5)]' : ''}
        ${theme.id === 'neon' ? 'hover:shadow-[0_0_35px_rgba(255,0,110,0.6)]' : ''}
        ${theme.id === 'matrix' ? 'hover:shadow-[0_0_30px_rgba(0,255,0,0.6)]' : ''}
      `}
      style={getPreviewStyles()}
      onMouseEnter={onPreview}
      onMouseLeave={onStopPreview}
      onClick={onSelect}
    >
      {/* Active indicator */}
      {isActive && (
        <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
          <Check className="h-4 w-4 text-white" />
        </div>
      )}

      {/* Theme preview content */}
      <div className="space-y-3">
        <div className="flex items-center space-x-3">
          <div 
            className="p-2 rounded-lg"
            style={{ backgroundColor: theme.colors.accent.primary, color: theme.colors.text.inverse }}
          >
            {icon}
          </div>
          <div>
            <h3 className="font-semibold">{theme.name}</h3>
            <p className="text-sm opacity-70">{theme.description}</p>
          </div>
        </div>

        {/* Mini preview elements */}
        <div className="space-y-2">
          <div 
            className="h-2 rounded-full"
            style={{ backgroundColor: theme.colors.accent.primary }}
          />
          <div className="flex space-x-2">
            <div 
              className="h-2 w-1/3 rounded-full"
              style={{ backgroundColor: theme.colors.accent.secondary }}
            />
            <div 
              className="h-2 w-1/4 rounded-full"
              style={{ backgroundColor: theme.colors.accent.success }}
            />
            <div 
              className="h-2 w-1/5 rounded-full"
              style={{ backgroundColor: theme.colors.accent.warning }}
            />
          </div>
        </div>

        {/* Special effects for futuristic themes */}
        {(theme.id === 'cyber' || theme.id === 'neon' || theme.id === 'matrix') && (
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white/5 pointer-events-none rounded-lg" />
        )}
      </div>
    </div>
  );
}
