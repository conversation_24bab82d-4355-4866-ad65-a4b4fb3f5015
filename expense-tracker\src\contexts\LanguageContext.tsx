'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Supported languages with their metadata
export const SUPPORTED_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
  },
  zh: {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
    rtl: false,
  },
  hi: {
    code: 'hi',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
    rtl: false,
  },
  es: {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    rtl: false,
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    rtl: true,
  },
  bn: {
    code: 'bn',
    name: 'Bengali',
    nativeName: 'বাংলা',
    flag: '🇧🇩',
    rtl: false,
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    rtl: false,
  },
  ru: {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    flag: '🇷🇺',
    rtl: false,
  },
  pt: {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    flag: '🇧🇷',
    rtl: false,
  },
  ur: {
    code: 'ur',
    name: 'Urdu',
    nativeName: 'اردو',
    flag: '🇵🇰',
    rtl: true,
  },
  id: {
    code: 'id',
    name: 'Indonesian',
    nativeName: 'Bahasa Indonesia',
    flag: '🇮🇩',
    rtl: false,
  },
  sw: {
    code: 'sw',
    name: 'Swahili',
    nativeName: 'Kiswahili',
    flag: '🇰🇪',
    rtl: false,
  },
  pa: {
    code: 'pa',
    name: 'Punjabi',
    nativeName: 'ਪੰਜਾਬੀ',
    flag: '🇮🇳',
    rtl: false,
  },
  ko: {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷',
    rtl: false,
  },
  de: {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    rtl: false,
  },
  ja: {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    rtl: false,
  },
} as const;

export type LanguageCode = keyof typeof SUPPORTED_LANGUAGES;

interface LanguageContextType {
  currentLanguage: LanguageCode;
  setLanguage: (language: LanguageCode) => void;
  t: (text: string) => string;
  isRTL: boolean;
  languageData: typeof SUPPORTED_LANGUAGES[LanguageCode];
  isTranslating: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

// Translation cache to avoid repeated API calls
const translationCache = new Map<string, string>();

// Detect browser language
function detectBrowserLanguage(): LanguageCode {
  if (typeof window === 'undefined') return 'en';

  const browserLang = navigator.language.split('-')[0] as LanguageCode;
  return SUPPORTED_LANGUAGES[browserLang] ? browserLang : 'en';
}

// Auto-translate function using Google Translate API (free tier)
async function translateText(text: string, targetLang: LanguageCode): Promise<string> {
  if (targetLang === 'en') return text; // No translation needed for English

  const cacheKey = `${text}:${targetLang}`;

  // Check cache first
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey)!;
  }

  try {
    // Using Google Translate API (you can replace with other services)
    const response = await fetch(
      `https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=${targetLang}&dt=t&q=${encodeURIComponent(text)}`
    );

    if (!response.ok) {
      throw new Error('Translation failed');
    }

    const data = await response.json();
    const translatedText = data[0]?.[0]?.[0] || text;

    // Cache the translation
    translationCache.set(cacheKey, translatedText);

    return translatedText;
  } catch (error) {
    console.warn('Translation failed, using original text:', error);
    return text;
  }
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>('en');
  const [isTranslating, setIsTranslating] = useState(false);

  // Initialize language from localStorage or browser detection
  useEffect(() => {
    const savedLanguage = localStorage.getItem('expense-tracker-language') as LanguageCode;
    const initialLanguage = savedLanguage || detectBrowserLanguage();
    
    if (SUPPORTED_LANGUAGES[initialLanguage]) {
      setCurrentLanguage(initialLanguage);
    }
  }, []);

  // Clear translation cache when language changes to force re-translation
  useEffect(() => {
    translationCache.clear();
  }, [currentLanguage]);

  // Update document direction for RTL languages
  useEffect(() => {
    const isRTL = SUPPORTED_LANGUAGES[currentLanguage].rtl;
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;

    // Add RTL class to body for styling
    if (isRTL) {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }, [currentLanguage]);

  const setLanguage = (language: LanguageCode) => {
    setCurrentLanguage(language);
    localStorage.setItem('expense-tracker-language', language);
  };

  // Auto-translate function with caching
  const t = (text: string): string => {
    if (currentLanguage === 'en') {
      return text; // No translation needed for English
    }

    const cacheKey = `${text}:${currentLanguage}`;

    // Check cache first
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!;
    }

    // If not in cache, start translation and return original text temporarily
    setIsTranslating(true);
    translateText(text, currentLanguage)
      .then((translated) => {
        setIsTranslating(false);
        // Force re-render by updating a dummy state or using a callback
        // This is a simplified approach - in production, you might want to use a more sophisticated state management
      })
      .catch(() => {
        setIsTranslating(false);
      });

    return text; // Return original text while translating
  };

  const value: LanguageContextType = {
    currentLanguage,
    setLanguage,
    t,
    isRTL: SUPPORTED_LANGUAGES[currentLanguage].rtl,
    languageData: SUPPORTED_LANGUAGES[currentLanguage],
    isTranslating,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Custom hook for auto-translation with real-time updates
export function useTranslate(text: string): string {
  const { currentLanguage } = useLanguage();
  const [translatedText, setTranslatedText] = useState(text);

  useEffect(() => {
    if (currentLanguage === 'en') {
      setTranslatedText(text);
      return;
    }

    const cacheKey = `${text}:${currentLanguage}`;

    // Check cache first
    if (translationCache.has(cacheKey)) {
      setTranslatedText(translationCache.get(cacheKey)!);
      return;
    }

    // Translate if not in cache
    translateText(text, currentLanguage)
      .then((translated) => {
        setTranslatedText(translated);
      })
      .catch(() => {
        setTranslatedText(text);
      });
  }, [text, currentLanguage]);

  return translatedText;
}

// Batch translation hook for multiple texts
export function useBatchTranslate(texts: string[]): string[] {
  const { currentLanguage } = useLanguage();
  const [translatedTexts, setTranslatedTexts] = useState(texts);

  useEffect(() => {
    if (currentLanguage === 'en') {
      setTranslatedTexts(texts);
      return;
    }

    const translateBatch = async () => {
      const results = await Promise.all(
        texts.map(text => translateText(text, currentLanguage))
      );
      setTranslatedTexts(results);
    };

    translateBatch();
  }, [texts, currentLanguage]);

  return translatedTexts;
}
