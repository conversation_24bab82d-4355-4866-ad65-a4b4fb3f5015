'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export type ThemeType = 'light' | 'dark' | 'cyber' | 'neon' | 'matrix';

export interface Theme {
  id: ThemeType;
  name: string;
  description: string;
  colors: {
    // Background colors
    bg: {
      primary: string;
      secondary: string;
      tertiary: string;
      card: string;
      modal: string;
    };
    // Text colors
    text: {
      primary: string;
      secondary: string;
      tertiary: string;
      inverse: string;
    };
    // Accent colors
    accent: {
      primary: string;
      secondary: string;
      success: string;
      warning: string;
      error: string;
      info: string;
    };
    // Border colors
    border: {
      primary: string;
      secondary: string;
      focus: string;
    };
    // Special effects
    glow: {
      primary: string;
      secondary: string;
      accent: string;
    };
  };
  effects: {
    blur: string;
    shadow: string;
    glow: string;
    gradient: string;
    hover: {
      scale: string;
      glow: string;
      shadow: string;
    };
  };
}

export const themes: Record<ThemeType, Theme> = {
  light: {
    id: 'light',
    name: 'Light Mode',
    description: 'Clean and bright interface',
    colors: {
      bg: {
        primary: '#ffffff',
        secondary: '#f8fafc',
        tertiary: '#f1f5f9',
        card: '#ffffff',
        modal: '#ffffff',
      },
      text: {
        primary: '#1f2937',
        secondary: '#6b7280',
        tertiary: '#9ca3af',
        inverse: '#ffffff',
      },
      accent: {
        primary: '#3b82f6',
        secondary: '#6366f1',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        info: '#06b6d4',
      },
      border: {
        primary: '#e5e7eb',
        secondary: '#d1d5db',
        focus: '#3b82f6',
      },
      glow: {
        primary: 'rgba(59, 130, 246, 0.3)',
        secondary: 'rgba(99, 102, 241, 0.3)',
        accent: 'rgba(16, 185, 129, 0.3)',
      },
    },
    effects: {
      blur: 'backdrop-blur-sm',
      shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      glow: '0 0 20px rgba(59, 130, 246, 0.3)',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      hover: {
        scale: 'scale-105',
        glow: '0 0 25px rgba(59, 130, 246, 0.4)',
        shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.2)',
      },
    },
  },
  dark: {
    id: 'dark',
    name: 'Dark Mode',
    description: 'Easy on the eyes',
    colors: {
      bg: {
        primary: '#111827',
        secondary: '#1f2937',
        tertiary: '#374151',
        card: '#1f2937',
        modal: '#111827',
      },
      text: {
        primary: '#f9fafb',
        secondary: '#d1d5db',
        tertiary: '#9ca3af',
        inverse: '#111827',
      },
      accent: {
        primary: '#60a5fa',
        secondary: '#818cf8',
        success: '#34d399',
        warning: '#fbbf24',
        error: '#f87171',
        info: '#22d3ee',
      },
      border: {
        primary: '#374151',
        secondary: '#4b5563',
        focus: '#60a5fa',
      },
      glow: {
        primary: 'rgba(96, 165, 250, 0.3)',
        secondary: 'rgba(129, 140, 248, 0.3)',
        accent: 'rgba(52, 211, 153, 0.3)',
      },
    },
    effects: {
      blur: 'backdrop-blur-sm',
      shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',
      glow: '0 0 20px rgba(96, 165, 250, 0.3)',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      hover: {
        scale: 'scale-105',
        glow: '0 0 25px rgba(96, 165, 250, 0.4)',
        shadow: '0 10px 25px -5px rgba(0, 0, 0, 0.4)',
      },
    },
  },
  cyber: {
    id: 'cyber',
    name: 'Cyber Black',
    description: 'Futuristic super black with neon accents',
    colors: {
      bg: {
        primary: '#000000',
        secondary: '#0a0a0a',
        tertiary: '#111111',
        card: 'rgba(0, 0, 0, 0.9)',
        modal: 'rgba(0, 0, 0, 0.95)',
      },
      text: {
        primary: '#00ff88',
        secondary: '#00ccff',
        tertiary: '#888888',
        inverse: '#000000',
      },
      accent: {
        primary: '#00ff88',
        secondary: '#00ccff',
        success: '#00ff00',
        warning: '#ffaa00',
        error: '#ff0055',
        info: '#0088ff',
      },
      border: {
        primary: '#00ff88',
        secondary: '#00ccff',
        focus: '#00ff88',
      },
      glow: {
        primary: 'rgba(0, 255, 136, 0.5)',
        secondary: 'rgba(0, 204, 255, 0.5)',
        accent: 'rgba(0, 255, 0, 0.5)',
      },
    },
    effects: {
      blur: 'backdrop-blur-md',
      shadow: '0 0 30px rgba(0, 255, 136, 0.3)',
      glow: '0 0 40px rgba(0, 255, 136, 0.6)',
      gradient: 'linear-gradient(135deg, #00ff88 0%, #00ccff 100%)',
      hover: {
        scale: 'scale-110',
        glow: '0 0 50px rgba(0, 255, 136, 0.8)',
        shadow: '0 20px 40px -10px rgba(0, 255, 136, 0.4)',
      },
    },
  },
  neon: {
    id: 'neon',
    name: 'Neon Dreams',
    description: 'Vibrant neon with dark background',
    colors: {
      bg: {
        primary: '#0d1117',
        secondary: '#161b22',
        tertiary: '#21262d',
        card: 'rgba(13, 17, 23, 0.9)',
        modal: 'rgba(13, 17, 23, 0.95)',
      },
      text: {
        primary: '#ff006e',
        secondary: '#8338ec',
        tertiary: '#aaaaaa',
        inverse: '#0d1117',
      },
      accent: {
        primary: '#ff006e',
        secondary: '#8338ec',
        success: '#06ffa5',
        warning: '#ffbe0b',
        error: '#fb5607',
        info: '#3a86ff',
      },
      border: {
        primary: '#ff006e',
        secondary: '#8338ec',
        focus: '#ff006e',
      },
      glow: {
        primary: 'rgba(255, 0, 110, 0.5)',
        secondary: 'rgba(131, 56, 236, 0.5)',
        accent: 'rgba(6, 255, 165, 0.5)',
      },
    },
    effects: {
      blur: 'backdrop-blur-lg',
      shadow: '0 0 25px rgba(255, 0, 110, 0.4)',
      glow: '0 0 35px rgba(255, 0, 110, 0.7)',
      gradient: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',
      hover: {
        scale: 'scale-108',
        glow: '0 0 45px rgba(255, 0, 110, 0.9)',
        shadow: '0 15px 35px -8px rgba(255, 0, 110, 0.5)',
      },
    },
  },
  matrix: {
    id: 'matrix',
    name: 'Matrix Code',
    description: 'Green matrix-style interface',
    colors: {
      bg: {
        primary: '#000000',
        secondary: '#001100',
        tertiary: '#002200',
        card: 'rgba(0, 17, 0, 0.9)',
        modal: 'rgba(0, 0, 0, 0.95)',
      },
      text: {
        primary: '#00ff00',
        secondary: '#00cc00',
        tertiary: '#008800',
        inverse: '#000000',
      },
      accent: {
        primary: '#00ff00',
        secondary: '#00cc00',
        success: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000',
        info: '#00ffff',
      },
      border: {
        primary: '#00ff00',
        secondary: '#00cc00',
        focus: '#00ff00',
      },
      glow: {
        primary: 'rgba(0, 255, 0, 0.5)',
        secondary: 'rgba(0, 204, 0, 0.5)',
        accent: 'rgba(0, 255, 0, 0.7)',
      },
    },
    effects: {
      blur: 'backdrop-blur-sm',
      shadow: '0 0 20px rgba(0, 255, 0, 0.4)',
      glow: '0 0 30px rgba(0, 255, 0, 0.8)',
      gradient: 'linear-gradient(135deg, #00ff00 0%, #00cc00 100%)',
      hover: {
        scale: 'scale-105',
        glow: '0 0 40px rgba(0, 255, 0, 1)',
        shadow: '0 10px 30px -5px rgba(0, 255, 0, 0.6)',
      },
    },
  },
};

interface ThemeContextType {
  currentTheme: ThemeType;
  theme: Theme;
  setTheme: (theme: ThemeType) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [currentTheme, setCurrentTheme] = useState<ThemeType>('light');

  useEffect(() => {
    const savedTheme = localStorage.getItem('expense-tracker-theme') as ThemeType;
    if (savedTheme && themes[savedTheme]) {
      setCurrentTheme(savedTheme);
    }
  }, []);

  const setTheme = (theme: ThemeType) => {
    setCurrentTheme(theme);
    localStorage.setItem('expense-tracker-theme', theme);
    
    // Apply CSS custom properties
    const root = document.documentElement;
    const themeColors = themes[theme].colors;
    
    // Set CSS variables
    root.style.setProperty('--bg-primary', themeColors.bg.primary);
    root.style.setProperty('--bg-secondary', themeColors.bg.secondary);
    root.style.setProperty('--bg-tertiary', themeColors.bg.tertiary);
    root.style.setProperty('--bg-card', themeColors.bg.card);
    root.style.setProperty('--bg-modal', themeColors.bg.modal);
    
    root.style.setProperty('--text-primary', themeColors.text.primary);
    root.style.setProperty('--text-secondary', themeColors.text.secondary);
    root.style.setProperty('--text-tertiary', themeColors.text.tertiary);
    root.style.setProperty('--text-inverse', themeColors.text.inverse);
    
    root.style.setProperty('--accent-primary', themeColors.accent.primary);
    root.style.setProperty('--accent-secondary', themeColors.accent.secondary);
    root.style.setProperty('--accent-success', themeColors.accent.success);
    root.style.setProperty('--accent-warning', themeColors.accent.warning);
    root.style.setProperty('--accent-error', themeColors.accent.error);
    root.style.setProperty('--accent-info', themeColors.accent.info);
    
    root.style.setProperty('--border-primary', themeColors.border.primary);
    root.style.setProperty('--border-secondary', themeColors.border.secondary);
    root.style.setProperty('--border-focus', themeColors.border.focus);
    
    root.style.setProperty('--glow-primary', themeColors.glow.primary);
    root.style.setProperty('--glow-secondary', themeColors.glow.secondary);
    root.style.setProperty('--glow-accent', themeColors.glow.accent);
  };

  const toggleTheme = () => {
    const themeOrder: ThemeType[] = ['light', 'dark', 'cyber', 'neon', 'matrix'];
    const currentIndex = themeOrder.indexOf(currentTheme);
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    setTheme(themeOrder[nextIndex]);
  };

  const value = {
    currentTheme,
    theme: themes[currentTheme],
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
