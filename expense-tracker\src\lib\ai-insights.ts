import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, subMonths, differenceInDays } from 'date-fns';

interface Expense {
  id: string;
  merchant: string;
  amount: number;
  date: string;
  category: string;
}

export interface SpendingInsight {
  type: 'warning' | 'info' | 'success' | 'alert';
  title: string;
  description: string;
  action?: string;
  priority: 'low' | 'medium' | 'high';
  category?: string;
  amount?: number;
}

export interface SpendingPattern {
  pattern: string;
  frequency: number;
  averageAmount: number;
  category: string;
  confidence: number;
}

export interface AnomalyDetection {
  isAnomaly: boolean;
  type: 'amount' | 'frequency' | 'category' | 'merchant';
  description: string;
  severity: 'low' | 'medium' | 'high';
  expectedValue: number;
  actualValue: number;
}

/**
 * Generate AI-powered spending insights
 */
export const generateSpendingInsights = (expenses: Expense[]): SpendingInsight[] => {
  const insights: SpendingInsight[] = [];
  
  if (expenses.length === 0) {
    return [{
      type: 'info',
      title: 'Start Tracking Your Expenses',
      description: 'Add some expenses to get personalized insights and recommendations.',
      priority: 'low'
    }];
  }

  // Analyze spending patterns
  const patterns = analyzeSpendingPatterns(expenses);
  const anomalies = detectAnomalies(expenses);
  const trends = analyzeTrends(expenses);
  
  // Generate insights based on patterns
  patterns.forEach(pattern => {
    if (pattern.confidence > 0.7) {
      insights.push({
        type: 'info',
        title: `Regular ${pattern.category} Spending`,
        description: `You spend an average of $${pattern.averageAmount.toFixed(2)} on ${pattern.category} ${pattern.pattern}. This accounts for ${((pattern.averageAmount * pattern.frequency) / getTotalSpending(expenses) * 100).toFixed(1)}% of your total spending.`,
        priority: 'low',
        category: pattern.category,
        amount: pattern.averageAmount
      });
    }
  });

  // Generate anomaly alerts
  anomalies.forEach(anomaly => {
    if (anomaly.isAnomaly && anomaly.severity !== 'low') {
      insights.push({
        type: anomaly.severity === 'high' ? 'alert' : 'warning',
        title: `Unusual ${anomaly.type} Detected`,
        description: anomaly.description,
        priority: anomaly.severity === 'high' ? 'high' : 'medium'
      });
    }
  });

  // Generate trend insights
  if (trends.monthlyGrowth > 20) {
    insights.push({
      type: 'warning',
      title: 'Spending Increase Alert',
      description: `Your spending has increased by ${trends.monthlyGrowth.toFixed(1)}% compared to last month. Consider reviewing your budget.`,
      priority: 'high',
      action: 'Review recent expenses and identify areas to cut back'
    });
  } else if (trends.monthlyGrowth < -10) {
    insights.push({
      type: 'success',
      title: 'Great Job Saving!',
      description: `You've reduced your spending by ${Math.abs(trends.monthlyGrowth).toFixed(1)}% this month. Keep up the good work!`,
      priority: 'low'
    });
  }

  // Category-specific insights
  const categoryInsights = generateCategoryInsights(expenses);
  insights.push(...categoryInsights);

  // Predictive insights
  const predictions = generatePredictiveInsights(expenses);
  insights.push(...predictions);

  return insights.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });
};

/**
 * Analyze spending patterns
 */
const analyzeSpendingPatterns = (expenses: Expense[]): SpendingPattern[] => {
  const patterns: SpendingPattern[] = [];
  const categoryGroups = groupByCategory(expenses);

  Object.entries(categoryGroups).forEach(([category, categoryExpenses]) => {
    // Analyze weekly patterns
    const weeklySpending = analyzeWeeklyPattern(categoryExpenses);
    if (weeklySpending.frequency > 0.5) {
      patterns.push({
        pattern: 'weekly',
        frequency: weeklySpending.frequency,
        averageAmount: weeklySpending.average,
        category,
        confidence: weeklySpending.frequency
      });
    }

    // Analyze daily patterns
    const dailySpending = analyzeDailyPattern(categoryExpenses);
    if (dailySpending.frequency > 0.3) {
      patterns.push({
        pattern: 'daily',
        frequency: dailySpending.frequency,
        averageAmount: dailySpending.average,
        category,
        confidence: dailySpending.frequency
      });
    }
  });

  return patterns;
};

/**
 * Detect spending anomalies
 */
const detectAnomalies = (expenses: Expense[]): AnomalyDetection[] => {
  const anomalies: AnomalyDetection[] = [];
  
  // Amount anomalies
  const amounts = expenses.map(e => e.amount);
  const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;
  const stdDev = Math.sqrt(amounts.reduce((sum, amt) => sum + Math.pow(amt - avgAmount, 2), 0) / amounts.length);
  
  expenses.forEach(expense => {
    const zScore = Math.abs((expense.amount - avgAmount) / stdDev);
    if (zScore > 2) { // More than 2 standard deviations
      anomalies.push({
        isAnomaly: true,
        type: 'amount',
        description: `Unusually ${expense.amount > avgAmount ? 'high' : 'low'} expense of $${expense.amount.toFixed(2)} at ${expense.merchant}`,
        severity: zScore > 3 ? 'high' : 'medium',
        expectedValue: avgAmount,
        actualValue: expense.amount
      });
    }
  });

  // Frequency anomalies
  const categoryFrequency = analyzeFrequencyAnomalies(expenses);
  anomalies.push(...categoryFrequency);

  return anomalies;
};

/**
 * Analyze spending trends
 */
const analyzeTrends = (expenses: Expense[]) => {
  const now = new Date();
  const thisMonth = expenses.filter(e => {
    const expenseDate = new Date(e.date);
    return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();
  });
  
  const lastMonth = expenses.filter(e => {
    const expenseDate = new Date(e.date);
    const lastMonthDate = subMonths(now, 1);
    return expenseDate.getMonth() === lastMonthDate.getMonth() && expenseDate.getFullYear() === lastMonthDate.getFullYear();
  });

  const thisMonthTotal = thisMonth.reduce((sum, e) => sum + e.amount, 0);
  const lastMonthTotal = lastMonth.reduce((sum, e) => sum + e.amount, 0);
  
  const monthlyGrowth = lastMonthTotal > 0 ? ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100 : 0;

  return {
    monthlyGrowth,
    thisMonthTotal,
    lastMonthTotal
  };
};

/**
 * Generate category-specific insights
 */
const generateCategoryInsights = (expenses: Expense[]): SpendingInsight[] => {
  const insights: SpendingInsight[] = [];
  const categoryTotals = groupByCategory(expenses);
  const totalSpending = getTotalSpending(expenses);

  Object.entries(categoryTotals).forEach(([category, categoryExpenses]) => {
    const categoryTotal = categoryExpenses.reduce((sum, e) => sum + e.amount, 0);
    const percentage = (categoryTotal / totalSpending) * 100;

    if (percentage > 40) {
      insights.push({
        type: 'warning',
        title: `High ${category} Spending`,
        description: `${category} accounts for ${percentage.toFixed(1)}% of your total spending ($${categoryTotal.toFixed(2)}). Consider if this aligns with your priorities.`,
        priority: 'medium',
        category,
        amount: categoryTotal
      });
    }

    // Detect frequent small purchases
    const smallPurchases = categoryExpenses.filter(e => e.amount < 10);
    if (smallPurchases.length > 10) {
      const smallTotal = smallPurchases.reduce((sum, e) => sum + e.amount, 0);
      insights.push({
        type: 'info',
        title: `Frequent Small ${category} Purchases`,
        description: `You made ${smallPurchases.length} small purchases in ${category} totaling $${smallTotal.toFixed(2)}. These add up over time!`,
        priority: 'low',
        category,
        amount: smallTotal
      });
    }
  });

  return insights;
};

/**
 * Generate predictive insights
 */
const generatePredictiveInsights = (expenses: Expense[]): SpendingInsight[] => {
  const insights: SpendingInsight[] = [];
  
  // Predict monthly spending based on current trend
  const now = new Date();
  const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
  const daysPassed = now.getDate();
  
  const thisMonthExpenses = expenses.filter(e => {
    const expenseDate = new Date(e.date);
    return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();
  });
  
  const currentMonthSpending = thisMonthExpenses.reduce((sum, e) => sum + e.amount, 0);
  const dailyAverage = currentMonthSpending / daysPassed;
  const projectedMonthlySpending = dailyAverage * daysInMonth;
  
  if (daysPassed > 7) { // Only predict after a week of data
    insights.push({
      type: 'info',
      title: 'Monthly Spending Projection',
      description: `Based on your current spending pattern, you're projected to spend $${projectedMonthlySpending.toFixed(2)} this month.`,
      priority: 'low',
      amount: projectedMonthlySpending
    });
  }

  return insights;
};

// Helper functions
const groupByCategory = (expenses: Expense[]) => {
  return expenses.reduce((groups, expense) => {
    const category = expense.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(expense);
    return groups;
  }, {} as Record<string, Expense[]>);
};

const getTotalSpending = (expenses: Expense[]) => {
  return expenses.reduce((sum, expense) => sum + expense.amount, 0);
};

const analyzeWeeklyPattern = (expenses: Expense[]) => {
  // Simplified weekly pattern analysis
  const weeklyTotals: number[] = [];
  // Implementation would analyze weekly spending patterns
  return { frequency: 0.5, average: 50 }; // Placeholder
};

const analyzeDailyPattern = (expenses: Expense[]) => {
  // Simplified daily pattern analysis
  return { frequency: 0.3, average: 15 }; // Placeholder
};

const analyzeFrequencyAnomalies = (expenses: Expense[]): AnomalyDetection[] => {
  // Simplified frequency anomaly detection
  return []; // Placeholder
};
