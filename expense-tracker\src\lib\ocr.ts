import Tesseract from 'tesseract.js';

export interface OCRResult {
  text: string;
  confidence: number;
  words: Array<{
    text: string;
    confidence: number;
    bbox: {
      x0: number;
      y0: number;
      x1: number;
      y1: number;
    };
  }>;
}

export interface ProcessedImage {
  canvas: HTMLCanvasElement;
  dataUrl: string;
}

export interface ImageProcessingOptions {
  contrast: number;
  brightness: number;
  sharpen: boolean;
  denoise: boolean;
  autoRotate: boolean;
}

/**
 * Advanced image preprocessing for optimal OCR results
 */
export const preprocessImage = (
  file: File,
  options: ImageProcessingOptions = {
    contrast: 1.5,
    brightness: 1.1,
    sharpen: true,
    denoise: true,
    autoRotate: true
  }
): Promise<ProcessedImage> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject(new Error('Could not get canvas context'));
      return;
    }

    img.onload = () => {
      // Auto-detect and correct rotation
      let { width, height } = img;
      let rotation = 0;

      if (options.autoRotate) {
        // Simple heuristic: if width > height significantly, might need rotation
        if (width > height * 1.5) {
          rotation = 0; // Landscape, likely correct
        } else if (height > width * 1.5) {
          rotation = 90; // Portrait, might need rotation for receipt
        }
      }

      // Set canvas size accounting for rotation
      if (rotation === 90 || rotation === 270) {
        canvas.width = height;
        canvas.height = width;
      } else {
        canvas.width = width;
        canvas.height = height;
      }

      // Apply rotation if needed
      if (rotation !== 0) {
        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate((rotation * Math.PI) / 180);
        ctx.translate(-width / 2, -height / 2);
      }

      // Draw original image
      ctx.drawImage(img, 0, 0);

      // Get image data for processing
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // Advanced image processing
      for (let i = 0; i < data.length; i += 4) {
        // Convert to grayscale using luminance formula
        let gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);

        // Apply brightness
        gray = Math.min(255, Math.max(0, gray * options.brightness));

        // Apply contrast
        const factor = (259 * (options.contrast * 255 + 255)) / (255 * (259 - options.contrast * 255));
        gray = Math.min(255, Math.max(0, factor * (gray - 128) + 128));

        // Apply to all channels
        data[i] = gray;     // Red
        data[i + 1] = gray; // Green
        data[i + 2] = gray; // Blue
        // Alpha channel (data[i + 3]) remains unchanged
      }

      // Apply sharpening filter if enabled
      if (options.sharpen) {
        applySharpenFilter(data, canvas.width, canvas.height);
      }

      // Apply denoising if enabled
      if (options.denoise) {
        applyDenoiseFilter(data, canvas.width, canvas.height);
      }

      // Put processed image data back
      ctx.putImageData(imageData, 0, 0);

      resolve({
        canvas,
        dataUrl: canvas.toDataURL('image/png')
      });
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    // Create object URL from file
    const objectUrl = URL.createObjectURL(file);
    img.src = objectUrl;
  });
};

/**
 * Apply sharpening filter to image data
 */
const applySharpenFilter = (data: Uint8ClampedArray, width: number, height: number) => {
  const kernel = [
    0, -1, 0,
    -1, 5, -1,
    0, -1, 0
  ];

  const output = new Uint8ClampedArray(data);

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let sum = 0;
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const idx = ((y + ky) * width + (x + kx)) * 4;
          sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];
        }
      }
      const idx = (y * width + x) * 4;
      const value = Math.min(255, Math.max(0, sum));
      output[idx] = value;
      output[idx + 1] = value;
      output[idx + 2] = value;
    }
  }

  data.set(output);
};

/**
 * Apply simple denoising filter
 */
const applyDenoiseFilter = (data: Uint8ClampedArray, width: number, height: number) => {
  const output = new Uint8ClampedArray(data);

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let sum = 0;
      let count = 0;

      // 3x3 median filter
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const idx = ((y + ky) * width + (x + kx)) * 4;
          sum += data[idx];
          count++;
        }
      }

      const idx = (y * width + x) * 4;
      const value = Math.round(sum / count);
      output[idx] = value;
      output[idx + 1] = value;
      output[idx + 2] = value;
    }
  }

  data.set(output);
};

/**
 * Extract text from image using advanced Tesseract.js configuration
 */
export const extractTextFromImage = async (
  imageSource: string | File | HTMLCanvasElement,
  options?: {
    language?: string;
    logger?: (info: any) => void;
    psm?: number;
    oem?: number;
  }
): Promise<OCRResult> => {
  try {
    const {
      language = 'eng',
      logger,
      psm = 6, // Assume a single uniform block of text
      oem = 3  // Default OCR Engine Mode
    } = options || {};

    // Advanced Tesseract configuration for receipts
    const result = await Tesseract.recognize(
      imageSource,
      language,
      {
        logger: logger || (() => {}),
        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/$-:()# \n\t',
        tessedit_pageseg_mode: psm,
        tessedit_ocr_engine_mode: oem,
        preserve_interword_spaces: '1',
        user_defined_dpi: '300',
        // Receipt-specific optimizations
        textord_heavy_nr: '1',
        textord_noise_normratio: '2',
        textord_noise_sizelimit: '0.5',
        // Improve number recognition
        classify_enable_learning: '0',
        classify_enable_adaptive_matcher: '0',
      }
    );

    // Extract word-level data with confidence scores
    const words = result.data.words.map(word => ({
      text: word.text,
      confidence: word.confidence,
      bbox: {
        x0: word.bbox.x0,
        y0: word.bbox.y0,
        x1: word.bbox.x1,
        y1: word.bbox.y1,
      }
    }));

    return {
      text: result.data.text,
      confidence: result.data.confidence,
      words
    };
  } catch (error) {
    console.error('OCR Error:', error);
    throw new Error('Failed to extract text from image');
  }
};

/**
 * Multi-pass OCR with different configurations for better accuracy
 */
export const extractTextMultiPass = async (
  imageSource: string | File | HTMLCanvasElement,
  onProgress?: (progress: number, pass: string) => void
): Promise<OCRResult> => {
  const passes = [
    { name: 'Standard', psm: 6, oem: 3 },
    { name: 'Single Block', psm: 8, oem: 3 },
    { name: 'Single Line', psm: 7, oem: 3 },
    { name: 'Sparse Text', psm: 11, oem: 3 }
  ];

  let bestResult: OCRResult | null = null;
  let bestConfidence = 0;

  for (let i = 0; i < passes.length; i++) {
    const pass = passes[i];
    onProgress?.(((i + 1) / passes.length) * 100, pass.name);

    try {
      const result = await extractTextFromImage(imageSource, {
        psm: pass.psm,
        oem: pass.oem,
        logger: (info) => {
          if (info.status === 'recognizing text') {
            const passProgress = (i / passes.length) * 100;
            const currentProgress = passProgress + (info.progress * (100 / passes.length));
            onProgress?.(currentProgress, `${pass.name} - ${Math.round(info.progress * 100)}%`);
          }
        }
      });

      if (result.confidence > bestConfidence) {
        bestResult = result;
        bestConfidence = result.confidence;
      }
    } catch (error) {
      console.warn(`OCR pass ${pass.name} failed:`, error);
    }
  }

  if (!bestResult) {
    throw new Error('All OCR passes failed');
  }

  return bestResult;
};

/**
 * Advanced receipt processing with multiple enhancement techniques
 */
export const processReceiptImage = async (
  file: File,
  onProgress?: (progress: number, status: string) => void
): Promise<OCRResult> => {
  try {
    onProgress?.(5, 'Analyzing image...');

    // Step 1: Preprocess image with optimal settings for receipts
    const processedImage = await preprocessImage(file, {
      contrast: 1.8,
      brightness: 1.2,
      sharpen: true,
      denoise: true,
      autoRotate: true
    });

    onProgress?.(20, 'Image enhanced, starting OCR...');

    // Step 2: Multi-pass OCR for best results
    const result = await extractTextMultiPass(
      processedImage.canvas,
      (progress, pass) => {
        const overallProgress = 20 + (progress * 0.8);
        onProgress?.(Math.round(overallProgress), `OCR Pass: ${pass}`);
      }
    );

    onProgress?.(100, 'Processing complete!');
    return result;
  } catch (error) {
    console.error('Receipt processing error:', error);
    throw error;
  }
};

/**
 * Process receipt with fallback strategies
 */
export const processReceiptWithFallback = async (
  file: File,
  onProgress?: (progress: number, status: string) => void
): Promise<OCRResult> => {
  const strategies = [
    {
      name: 'High Quality',
      options: { contrast: 1.8, brightness: 1.2, sharpen: true, denoise: true, autoRotate: true }
    },
    {
      name: 'High Contrast',
      options: { contrast: 2.2, brightness: 1.0, sharpen: true, denoise: false, autoRotate: true }
    },
    {
      name: 'Minimal Processing',
      options: { contrast: 1.2, brightness: 1.1, sharpen: false, denoise: false, autoRotate: false }
    }
  ];

  let bestResult: OCRResult | null = null;
  let bestConfidence = 0;

  for (let i = 0; i < strategies.length; i++) {
    const strategy = strategies[i];

    try {
      onProgress?.(
        (i / strategies.length) * 100,
        `Trying ${strategy.name} processing...`
      );

      const processedImage = await preprocessImage(file, strategy.options);
      const result = await extractTextFromImage(processedImage.canvas, {
        logger: (info) => {
          if (info.status === 'recognizing text') {
            const strategyProgress = (i / strategies.length) * 100;
            const currentProgress = strategyProgress + (info.progress * (100 / strategies.length));
            onProgress?.(currentProgress, `${strategy.name}: ${Math.round(info.progress * 100)}%`);
          }
        }
      });

      if (result.confidence > bestConfidence) {
        bestResult = result;
        bestConfidence = result.confidence;
      }

      // If we get good confidence, use it
      if (result.confidence > 80) {
        break;
      }
    } catch (error) {
      console.warn(`Strategy ${strategy.name} failed:`, error);
    }
  }

  if (!bestResult) {
    throw new Error('All processing strategies failed');
  }

  onProgress?.(100, `Best result: ${bestResult.confidence.toFixed(1)}% confidence`);
  return bestResult;
};

/**
 * Validate image file
 */
export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Please upload a valid image file (JPEG, PNG, or WebP)'
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Image file size must be less than 10MB'
    };
  }

  return { valid: true };
};
